import type { StoryOutline, PlotPoint, Character, Setting, GenerationOptions } from '../types'

export class ContentGenerator {
  static async generateStoryContent(
    outline: StoryOutline,
    options: GenerationOptions
  ): Promise<string> {
    let content = `# ${outline.title}\n\n`
    content += `*${outline.summary}*\n\n`
    
    // Generate content for each plot point
    for (let i = 0; i < outline.plotPoints.length; i++) {
      const plotPoint = outline.plotPoints[i]
      const chapterContent = await this.generateChapterContent(
        plotPoint,
        outline,
        options,
        i + 1
      )
      content += chapterContent + '\n\n'
    }
    
    return content
  }

  private static async generateChapterContent(
    plotPoint: PlotPoint,
    outline: StoryOutline,
    options: GenerationOptions,
    chapterNumber: number
  ): Promise<string> {
    const characters = outline.characters.filter(c => 
      plotPoint.characters.includes(c.id)
    )
    const setting = outline.settings.find(s => s.id === plotPoint.setting)
    
    let content = `## Chapter ${chapterNumber}: ${plotPoint.title}\n\n`
    
    // Generate scene description
    content += this.generateSceneDescription(plotPoint, setting, characters, options)
    content += '\n\n'
    
    // Generate narrative content
    content += this.generateNarrativeContent(plotPoint, characters, setting, options)
    
    // Add dialogue if requested
    if (options.includeDialogue && characters.length > 1) {
      content += '\n\n'
      content += this.generateDialogueSection(plotPoint, characters, options)
    }
    
    return content
  }

  private static generateSceneDescription(
    plotPoint: PlotPoint,
    setting: Setting | undefined,
    characters: Character[],
    options: GenerationOptions
  ): string {
    if (!setting) return ''
    
    const timeOfDay = setting.timeOfDay ? ` during ${setting.timeOfDay}` : ''
    const weather = setting.weather ? ` The weather is ${setting.weather.toLowerCase()}.` : ''
    const atmosphere = setting.atmosphere ? ` ${setting.atmosphere}.` : ''
    
    let description = `The scene takes place in ${setting.name}${timeOfDay}.${weather}${atmosphere}`
    
    if (characters.length > 0) {
      const characterNames = characters.map(c => c.name).join(', ')
      description += ` Present in this scene: ${characterNames}.`
    }
    
    return description
  }

  private static generateNarrativeContent(
    plotPoint: PlotPoint,
    characters: Character[],
    setting: Setting | undefined,
    options: GenerationOptions
  ): string {
    const protagonist = characters.find(c => c.role === 'protagonist') || characters[0]
    
    // Generate content based on plot point type
    switch (plotPoint.type) {
      case 'setup':
        return this.generateSetupContent(protagonist, setting, options)
      case 'inciting-incident':
        return this.generateIncitingIncidentContent(protagonist, plotPoint, options)
      case 'plot-point':
        return this.generatePlotPointContent(protagonist, characters, plotPoint, options)
      case 'midpoint':
        return this.generateMidpointContent(protagonist, characters, plotPoint, options)
      case 'crisis':
        return this.generateCrisisContent(protagonist, plotPoint, options)
      case 'climax':
        return this.generateClimaxContent(characters, plotPoint, options)
      case 'resolution':
        return this.generateResolutionContent(protagonist, plotPoint, options)
      default:
        return this.generateGenericContent(protagonist, plotPoint, options)
    }
  }

  private static generateSetupContent(
    protagonist: Character,
    setting: Setting | undefined,
    options: GenerationOptions
  ): string {
    const perspective = this.getPerspectivePrefix(protagonist, options)
    const settingDesc = setting ? ` in ${setting.name}` : ''
    
    return `${perspective} ${protagonist.name} goes about their daily routine${settingDesc}. ` +
           `${this.getCharacterDescription(protagonist)} ` +
           `Their main goal is ${protagonist.goals[0] || 'to find their purpose'}, ` +
           `though they struggle with ${protagonist.flaws[0] || 'self-doubt'}. ` +
           `Little do they know that their ordinary world is about to change forever.`
  }

  private static generateIncitingIncidentContent(
    protagonist: Character,
    plotPoint: PlotPoint,
    options: GenerationOptions
  ): string {
    const perspective = this.getPerspectivePrefix(protagonist, options)
    
    return `${perspective} ${protagonist.name} encounters an unexpected event that disrupts their normal life. ` +
           `${plotPoint.description} ` +
           `This moment forces them to confront their fears and consider a path they never imagined. ` +
           `The comfortable world they knew begins to crumble, and they must decide how to respond.`
  }

  private static generatePlotPointContent(
    protagonist: Character,
    characters: Character[],
    plotPoint: PlotPoint,
    options: GenerationOptions
  ): string {
    const perspective = this.getPerspectivePrefix(protagonist, options)
    const antagonist = characters.find(c => c.role === 'antagonist')
    
    let content = `${perspective} ${protagonist.name} faces a significant challenge. ${plotPoint.description} `
    
    if (antagonist) {
      content += `${antagonist.name} presents a formidable obstacle, testing ${protagonist.name}'s resolve. `
    }
    
    content += `This moment requires ${protagonist.name} to dig deeper and find strength they didn't know they possessed.`
    
    return content
  }

  private static generateMidpointContent(
    protagonist: Character,
    characters: Character[],
    plotPoint: PlotPoint,
    options: GenerationOptions
  ): string {
    const perspective = this.getPerspectivePrefix(protagonist, options)
    
    return `${perspective} ${protagonist.name} reaches a crucial turning point. ${plotPoint.description} ` +
           `Everything they thought they knew is called into question. ` +
           `The stakes have never been higher, and there's no going back to the way things were. ` +
           `${protagonist.name} must embrace their transformation or risk losing everything.`
  }

  private static generateCrisisContent(
    protagonist: Character,
    plotPoint: PlotPoint,
    options: GenerationOptions
  ): string {
    const perspective = this.getPerspectivePrefix(protagonist, options)
    
    return `${perspective} ${protagonist.name} faces their darkest hour. ${plotPoint.description} ` +
           `All hope seems lost, and their greatest fears are realized. ` +
           `${protagonist.flaws[0] ? `Their tendency toward ${protagonist.flaws[0]} threatens to overwhelm them. ` : ''}` +
           `In this moment of despair, ${protagonist.name} must find the inner strength to continue.`
  }

  private static generateClimaxContent(
    characters: Character[],
    plotPoint: PlotPoint,
    options: GenerationOptions
  ): string {
    const protagonist = characters.find(c => c.role === 'protagonist') || characters[0]
    const antagonist = characters.find(c => c.role === 'antagonist')
    const perspective = this.getPerspectivePrefix(protagonist, options)
    
    let content = `${perspective} the final confrontation arrives. ${plotPoint.description} `
    
    if (antagonist) {
      content += `${protagonist.name} and ${antagonist.name} face each other in the ultimate test. `
    }
    
    content += `Everything ${protagonist.name} has learned, every relationship they've built, ` +
               `every sacrifice they've made leads to this moment. ` +
               `The outcome will determine not just their fate, but the fate of everyone they care about.`
    
    return content
  }

  private static generateResolutionContent(
    protagonist: Character,
    plotPoint: PlotPoint,
    options: GenerationOptions
  ): string {
    const perspective = this.getPerspectivePrefix(protagonist, options)
    
    return `${perspective} the dust settles and a new equilibrium emerges. ${plotPoint.description} ` +
           `${protagonist.name} has been fundamentally changed by their journey. ` +
           `The goal they once sought - ${protagonist.goals[0] || 'their purpose'} - ` +
           `has evolved into something deeper and more meaningful. ` +
           `Though the adventure is over, the lessons learned will last a lifetime.`
  }

  private static generateGenericContent(
    protagonist: Character,
    plotPoint: PlotPoint,
    options: GenerationOptions
  ): string {
    const perspective = this.getPerspectivePrefix(protagonist, options)
    
    return `${perspective} ${protagonist.name} continues their journey. ${plotPoint.description} ` +
           `Each step forward brings new challenges and revelations. ` +
           `The path ahead remains uncertain, but their determination grows stronger.`
  }

  private static generateDialogueSection(
    plotPoint: PlotPoint,
    characters: Character[],
    options: GenerationOptions
  ): string {
    if (characters.length < 2) return ''
    
    const protagonist = characters.find(c => c.role === 'protagonist') || characters[0]
    const otherCharacter = characters.find(c => c.id !== protagonist.id) || characters[1]
    
    let dialogue = '---\n\n'
    dialogue += `"${this.generateCharacterLine(protagonist, plotPoint, 'opening')}" ${protagonist.name} said.\n\n`
    dialogue += `"${this.generateCharacterLine(otherCharacter, plotPoint, 'response')}" ${otherCharacter.name} replied.\n\n`
    dialogue += `"${this.generateCharacterLine(protagonist, plotPoint, 'reaction')}" ${protagonist.name} responded.\n\n`
    
    return dialogue
  }

  private static generateCharacterLine(
    character: Character,
    plotPoint: PlotPoint,
    lineType: 'opening' | 'response' | 'reaction'
  ): string {
    const traits = character.personalityTraits
    const goals = character.goals[0] || 'find the truth'
    const flaws = character.flaws[0] || 'doubt myself'
    
    const lines = {
      opening: [
        `I never expected things to turn out this way`,
        `We need to talk about what happened`,
        `There's something I need to tell you`,
        `I'm not sure I can handle this alone`,
      ],
      response: [
        `I understand how you feel`,
        `That's not what I expected to hear`,
        `We'll figure this out together`,
        `You're stronger than you think`,
      ],
      reaction: [
        `Thank you for believing in me`,
        `I hope you're right about this`,
        `I won't let you down`,
        `Let's do what needs to be done`,
      ],
    }
    
    // Modify based on character traits
    let selectedLine = lines[lineType][Math.floor(Math.random() * lines[lineType].length)]
    
    if (traits.includes('brave' as any) && lineType === 'opening') {
      selectedLine = `I'm ready to face whatever comes next`
    } else if (traits.includes('deceptive' as any) && lineType === 'response') {
      selectedLine = `Of course, you can trust me completely`
    }
    
    return selectedLine
  }

  private static getPerspectivePrefix(
    protagonist: Character,
    options: GenerationOptions
  ): string {
    switch (options.narrativePerspective) {
      case 'first':
        return 'I'
      case 'second':
        return 'You'
      case 'third-limited':
        return `${protagonist.name}`
      case 'third-omniscient':
        return 'In this moment,'
      default:
        return `${protagonist.name}`
    }
  }

  private static getCharacterDescription(character: Character): string {
    const traits = character.personalityTraits.slice(0, 2).join(' and ')
    const description = character.description || 'a person of mystery'
    
    return `${character.name} is ${description}, known for being ${traits}.`
  }

  static estimateReadingTime(content: string): number {
    const wordsPerMinute = 200
    const wordCount = content.split(/\s+/).length
    return Math.ceil(wordCount / wordsPerMinute)
  }

  static generateChapterTitles(plotPoints: PlotPoint[]): string[] {
    return plotPoints.map((point, index) => {
      const chapterNumber = index + 1
      return `Chapter ${chapterNumber}: ${point.title}`
    })
  }
}
