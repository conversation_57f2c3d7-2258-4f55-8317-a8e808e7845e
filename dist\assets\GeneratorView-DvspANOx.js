import{d as q,c as o,a as e,b,n as ue,t as d,o as a,_ as Y,F as U,r as B,e as L,f as O,g as $,h as _e,w as R,i as P,P as pe,j as Z,k as se,l as g,m as C,p as N,v as K,q as J,u as Q,s as ae,x as le,y as oe,z as ne,G as x,S as ee,A as ie,B as re,T as te,C as ce,D as de,E as me,H as he}from"./index-SVRTPCSQ.js";import{B as w}from"./BaseButton-BA0mRaoL.js";const ge={class:"progress-bar"},ve={class:"progress-bar__track"},ye={key:0,class:"progress-bar__percentage"},fe=q({__name:"ProgressBar",props:{progress:{},showPercentage:{type:Boolean,default:!0}},setup(j){return(c,y)=>(a(),o("div",ge,[e("div",ve,[e("div",{class:"progress-bar__fill",style:ue({width:`${c.progress}%`})},null,4)]),c.showPercentage?(a(),o("div",ye,d(Math.round(c.progress))+"% ",1)):b("",!0)]))}}),be=Y(fe,[["__scopeId","data-v-1636d37a"]]),$e={class:"step-indicator"},ke=["onClick"],Ce={class:"step-indicator__circle"},Se={key:0,class:"step-indicator__check"},Te={key:1,class:"step-indicator__number"},we={class:"step-indicator__label"},Ve={key:0,class:"step-indicator__connector"},Ae=q({__name:"StepIndicator",props:{steps:{},currentStepIndex:{}},emits:["step-click"],setup(j){return(c,y)=>(a(),o("div",$e,[(a(!0),o(U,null,B(c.steps,(l,m)=>(a(),o("div",{key:l.key,class:L(["step-indicator__step",{"step-indicator__step--active":m===c.currentStepIndex,"step-indicator__step--completed":m<c.currentStepIndex,"step-indicator__step--disabled":m>c.currentStepIndex}]),onClick:u=>c.$emit("step-click",l.key,m)},[e("div",Ce,[m<c.currentStepIndex?(a(),o("span",Se,"✓")):(a(),o("span",Te,d(m+1),1))]),e("div",we,d(l.label),1),m<c.steps.length-1?(a(),o("div",Ve)):b("",!0)],10,ke))),128))]))}}),Ue=Y(Ae,[["__scopeId","data-v-c411deaf"]]),Be={class:"base-input"},De=["for"],Ie={key:0,class:"base-input__required"},Fe={class:"base-input__wrapper"},Oe=["id","type","value","placeholder","disabled","required"],Ge={key:0,class:"base-input__suffix"},Pe={key:1,class:"base-input__message"},Ee={key:0,class:"base-input__error"},Ne={key:1,class:"base-input__hint"},Le=q({__name:"BaseInput",props:{modelValue:{},label:{},type:{default:"text"},placeholder:{},disabled:{type:Boolean,default:!1},required:{type:Boolean,default:!1},error:{},hint:{}},emits:["update:modelValue","blur","focus"],setup(j,{emit:c}){const y=O(()=>`input-${Math.random().toString(36).substr(2,9)}`);return(l,m)=>(a(),o("div",Be,[l.label?(a(),o("label",{key:0,for:y.value,class:"base-input__label"},[$(d(l.label)+" ",1),l.required?(a(),o("span",Ie,"*")):b("",!0)],8,De)):b("",!0),e("div",Fe,[e("input",{id:y.value,type:l.type,value:l.modelValue,placeholder:l.placeholder,disabled:l.disabled,required:l.required,class:L(["base-input__field",{"base-input__field--error":l.error,"base-input__field--disabled":l.disabled}]),onInput:m[0]||(m[0]=u=>l.$emit("update:modelValue",u.target.value)),onBlur:m[1]||(m[1]=u=>l.$emit("blur",u)),onFocus:m[2]||(m[2]=u=>l.$emit("focus",u))},null,42,Oe),l.$slots.suffix?(a(),o("div",Ge,[_e(l.$slots,"suffix",{},void 0)])):b("",!0)]),l.error||l.hint?(a(),o("div",Pe,[l.error?(a(),o("span",Ee,d(l.error),1)):l.hint?(a(),o("span",Ne,d(l.hint),1)):b("",!0)])):b("",!0)]))}}),H=Y(Le,[["__scopeId","data-v-8dec60e3"]]),je={class:"character-card__header"},Me={class:"character-card__info"},ze={class:"character-card__name"},Re={class:"character-card__meta"},qe={key:0,class:"character-card__age"},Ye={class:"character-card__actions"},We={key:0,class:"character-card__action",title:"Template"},He={class:"character-card__description"},Qe={key:0,class:"character-card__traits"},Ke={key:0,class:"character-card__trait character-card__trait--more"},Je={key:1,class:"character-card__goals"},Xe={class:"character-card__list"},Ze={key:0,class:"character-card__list-item character-card__list-item--more"},xe={key:2,class:"character-card__flaws"},et={class:"character-card__list"},tt={key:0,class:"character-card__list-item character-card__list-item--more"},st={key:3,class:"character-card__selected-indicator"},at=q({__name:"CharacterCard",props:{character:{},isSelected:{type:Boolean}},emits:["select","edit","duplicate","delete"],setup(j){function c(l){return l.charAt(0).toUpperCase()+l.slice(1)}function y(l){return l.split("-").map(m=>m.charAt(0).toUpperCase()+m.slice(1)).join(" ")}return(l,m)=>(a(),o("div",{class:L(["character-card",{"character-card--selected":l.isSelected,"character-card--template":l.character.isTemplate}]),onClick:m[3]||(m[3]=u=>l.$emit("select",l.character))},[e("div",je,[e("div",Me,[e("h3",ze,d(l.character.name),1),e("div",Re,[e("span",{class:L(["character-card__role",`character-card__role--${l.character.role}`])},d(c(l.character.role)),3),l.character.age?(a(),o("span",qe," Age "+d(l.character.age),1)):b("",!0)])]),e("div",Ye,[l.character.isTemplate?(a(),o("button",We," 📋 ")):b("",!0),e("button",{class:"character-card__action",title:"Edit",onClick:m[0]||(m[0]=R(u=>l.$emit("edit",l.character),["stop"]))}," ✏️ "),e("button",{class:"character-card__action",title:"Duplicate",onClick:m[1]||(m[1]=R(u=>l.$emit("duplicate",l.character.id),["stop"]))}," 📄 "),e("button",{class:"character-card__action character-card__action--danger",title:"Delete",onClick:m[2]||(m[2]=R(u=>l.$emit("delete",l.character.id),["stop"]))}," 🗑️ ")])]),e("p",He,d(l.character.description),1),l.character.personalityTraits.length?(a(),o("div",Qe,[(a(!0),o(U,null,B(l.character.personalityTraits.slice(0,3),u=>(a(),o("span",{key:u,class:"character-card__trait"},d(y(u)),1))),128)),l.character.personalityTraits.length>3?(a(),o("span",Ke," +"+d(l.character.personalityTraits.length-3)+" more ",1)):b("",!0)])):b("",!0),l.character.goals.length?(a(),o("div",Je,[m[4]||(m[4]=e("h4",{class:"character-card__section-title"},"Goals:",-1)),e("ul",Xe,[(a(!0),o(U,null,B(l.character.goals.slice(0,2),u=>(a(),o("li",{key:u,class:"character-card__list-item"},d(u),1))),128)),l.character.goals.length>2?(a(),o("li",Ze," +"+d(l.character.goals.length-2)+" more goals ",1)):b("",!0)])])):b("",!0),l.character.flaws.length?(a(),o("div",xe,[m[5]||(m[5]=e("h4",{class:"character-card__section-title"},"Flaws:",-1)),e("ul",et,[(a(!0),o(U,null,B(l.character.flaws.slice(0,2),u=>(a(),o("li",{key:u,class:"character-card__list-item"},d(u),1))),128)),l.character.flaws.length>2?(a(),o("li",tt," +"+d(l.character.flaws.length-2)+" more flaws ",1)):b("",!0)])])):b("",!0),l.isSelected?(a(),o("div",st," ✓ Selected ")):b("",!0)],2))}}),ot=Y(at,[["__scopeId","data-v-14b101ac"]]),nt={class:"character-builder"},it={class:"character-builder__header"},lt={class:"character-builder__title"},rt={class:"character-builder__section"},ct={class:"character-builder__row"},dt={class:"base-input"},ut={class:"base-input"},_t={class:"character-builder__section"},pt={class:"character-builder__traits"},mt=["value","checked","onChange"],ht={class:"character-builder__section"},gt={class:"character-builder__list"},vt={class:"character-builder__section"},yt={class:"character-builder__list"},ft={class:"character-builder__section"},bt={class:"base-input"},$t={class:"base-input"},kt={class:"character-builder__checkbox"},Ct={class:"character-builder__actions"},St=q({__name:"CharacterBuilder",props:{editingCharacter:{}},emits:["close","submit"],setup(j,{emit:c}){const y=j,l=c,m=P(!1),u=Object.values(pe),t=Z({name:"",age:"",description:"",personalityTraits:[],goals:[""],flaws:[""],backstory:"",appearance:"",role:"supporting",isTemplate:!1}),s=Z({name:"",age:""});se(()=>y.editingCharacter,i=>{i?(t.name=i.name,t.age=i.age?.toString()||"",t.description=i.description,t.personalityTraits=[...i.personalityTraits],t.goals=i.goals.length?[...i.goals]:[""],t.flaws=i.flaws.length?[...i.flaws]:[""],t.backstory=i.backstory||"",t.appearance=i.appearance||"",t.role=i.role,t.isTemplate=i.isTemplate):D()},{immediate:!0});function D(){t.name="",t.age="",t.description="",t.personalityTraits=[],t.goals=[""],t.flaws=[""],t.backstory="",t.appearance="",t.role="supporting",t.isTemplate=!1,s.name="",s.age=""}function M(i){const n=t.personalityTraits.indexOf(i);n>-1?t.personalityTraits.splice(n,1):t.personalityTraits.push(i)}function I(i){return i.split("-").map(n=>n.charAt(0).toUpperCase()+n.slice(1)).join(" ")}function G(){t.goals.push("")}function E(i,n){t.goals[i]=n}function z(i){t.goals.length>1&&t.goals.splice(i,1)}function T(){t.flaws.push("")}function p(i,n){t.flaws[i]=n}function S(i){t.flaws.length>1&&t.flaws.splice(i,1)}function k(){return s.name="",s.age="",t.name.trim()?t.age&&(isNaN(Number(t.age))||Number(t.age)<0)?(s.age="Age must be a valid number",!1):!0:(s.name="Character name is required",!1)}async function _(){if(k()){m.value=!0;try{const i={name:t.name.trim(),age:t.age?Number(t.age):void 0,description:t.description.trim(),personalityTraits:t.personalityTraits,goals:t.goals.filter(n=>n.trim()),flaws:t.flaws.filter(n=>n.trim()),backstory:t.backstory.trim()||void 0,appearance:t.appearance.trim()||void 0,role:t.role,isTemplate:t.isTemplate};l("submit",i)}finally{m.value=!1}}}return(i,n)=>(a(),o("div",nt,[e("div",it,[e("h2",lt,d(i.editingCharacter?"Edit Character":"Create New Character"),1),g(w,{variant:"ghost",size:"small",onClick:n[0]||(n[0]=v=>i.$emit("close"))},{default:C(()=>n[9]||(n[9]=[$(" ✕ ")])),_:1,__:[9]})]),e("form",{onSubmit:R(_,["prevent"]),class:"character-builder__form"},[e("div",rt,[n[13]||(n[13]=e("h3",{class:"character-builder__section-title"},"Basic Information",-1)),g(H,{modelValue:t.name,"onUpdate:modelValue":n[1]||(n[1]=v=>t.name=v),label:"Character Name",placeholder:"Enter character name",required:"",error:s.name},null,8,["modelValue","error"]),e("div",ct,[g(H,{modelValue:t.age,"onUpdate:modelValue":n[2]||(n[2]=v=>t.age=v),label:"Age",type:"number",placeholder:"25",error:s.age},null,8,["modelValue","error"]),e("div",dt,[n[11]||(n[11]=e("label",{class:"base-input__label"},"Role",-1)),N(e("select",{"onUpdate:modelValue":n[3]||(n[3]=v=>t.role=v),class:"character-builder__select"},n[10]||(n[10]=[e("option",{value:"protagonist"},"Protagonist",-1),e("option",{value:"antagonist"},"Antagonist",-1),e("option",{value:"supporting"},"Supporting",-1),e("option",{value:"minor"},"Minor",-1)]),512),[[K,t.role]])])]),e("div",ut,[n[12]||(n[12]=e("label",{class:"base-input__label"},"Description",-1)),N(e("textarea",{"onUpdate:modelValue":n[4]||(n[4]=v=>t.description=v),placeholder:"Describe your character...",class:"character-builder__textarea",rows:"3"},null,512),[[J,t.description]])])]),e("div",_t,[n[14]||(n[14]=e("h3",{class:"character-builder__section-title"},"Personality Traits",-1)),e("div",pt,[(a(!0),o(U,null,B(Q(u),v=>(a(),o("label",{key:v,class:"character-builder__trait"},[e("input",{type:"checkbox",value:v,checked:t.personalityTraits.includes(v),onChange:F=>M(v)},null,40,mt),e("span",null,d(I(v)),1)]))),128))])]),e("div",ht,[n[17]||(n[17]=e("h3",{class:"character-builder__section-title"},"Goals",-1)),e("div",gt,[(a(!0),o(U,null,B(t.goals,(v,F)=>(a(),o("div",{key:F,class:"character-builder__list-item"},[g(H,{"model-value":v,placeholder:"Enter a goal...","onUpdate:modelValue":W=>E(F,W)},null,8,["model-value","onUpdate:modelValue"]),g(w,{variant:"ghost",size:"small",onClick:W=>z(F)},{default:C(()=>n[15]||(n[15]=[$(" ✕ ")])),_:2,__:[15]},1032,["onClick"])]))),128)),g(w,{variant:"ghost",size:"small",onClick:G},{default:C(()=>n[16]||(n[16]=[$(" + Add Goal ")])),_:1,__:[16]})])]),e("div",vt,[n[20]||(n[20]=e("h3",{class:"character-builder__section-title"},"Flaws",-1)),e("div",yt,[(a(!0),o(U,null,B(t.flaws,(v,F)=>(a(),o("div",{key:F,class:"character-builder__list-item"},[g(H,{"model-value":v,placeholder:"Enter a flaw...","onUpdate:modelValue":W=>p(F,W)},null,8,["model-value","onUpdate:modelValue"]),g(w,{variant:"ghost",size:"small",onClick:W=>S(F)},{default:C(()=>n[18]||(n[18]=[$(" ✕ ")])),_:2,__:[18]},1032,["onClick"])]))),128)),g(w,{variant:"ghost",size:"small",onClick:T},{default:C(()=>n[19]||(n[19]=[$(" + Add Flaw ")])),_:1,__:[19]})])]),e("div",ft,[n[24]||(n[24]=e("h3",{class:"character-builder__section-title"},"Optional Details",-1)),e("div",bt,[n[21]||(n[21]=e("label",{class:"base-input__label"},"Backstory",-1)),N(e("textarea",{"onUpdate:modelValue":n[5]||(n[5]=v=>t.backstory=v),placeholder:"Character's background and history...",class:"character-builder__textarea",rows:"3"},null,512),[[J,t.backstory]])]),e("div",$t,[n[22]||(n[22]=e("label",{class:"base-input__label"},"Appearance",-1)),N(e("textarea",{"onUpdate:modelValue":n[6]||(n[6]=v=>t.appearance=v),placeholder:"Physical description...",class:"character-builder__textarea",rows:"2"},null,512),[[J,t.appearance]])]),e("div",kt,[e("label",null,[N(e("input",{type:"checkbox","onUpdate:modelValue":n[7]||(n[7]=v=>t.isTemplate=v)},null,512),[[ae,t.isTemplate]]),n[23]||(n[23]=e("span",null,"Save as template for future use",-1))])])]),e("div",Ct,[g(w,{variant:"secondary",onClick:n[8]||(n[8]=v=>i.$emit("close"))},{default:C(()=>n[25]||(n[25]=[$(" Cancel ")])),_:1,__:[25]}),g(w,{type:"submit",loading:m.value},{default:C(()=>[$(d(i.editingCharacter?"Update Character":"Create Character"),1)]),_:1},8,["loading"])])],32)]))}}),Tt=Y(St,[["__scopeId","data-v-3b9b7624"]]),wt={class:"character-selection"},Vt={class:"character-selection__header"},At={class:"character-selection__actions"},Ut={class:"character-selection__filters"},Bt={class:"character-selection__filter-tabs"},Dt=["onClick"],It={class:"character-selection__filter-count"},Ft={key:0,class:"character-selection__selected"},Ot={class:"character-selection__selected-title"},Gt={class:"character-selection__selected-list"},Pt={class:"character-selection__selected-name"},Et={class:"character-selection__selected-role"},Nt=["onClick"],Lt={class:"character-selection__grid"},jt={key:0,class:"character-selection__empty"},Mt={class:"character-selection__empty-text"},zt=q({__name:"CharacterSelection",setup(j){const c=le(),y=P(""),l=P("all"),m=P(!1),u=P(null),t=O(()=>[{key:"all",label:"All",count:c.characters.length},{key:"protagonists",label:"Protagonists",count:c.protagonists.length},{key:"antagonists",label:"Antagonists",count:c.antagonists.length},{key:"supporting",label:"Supporting",count:c.supportingCharacters.length},{key:"templates",label:"Templates",count:c.templates.length}]),s=O(()=>{let i=c.characters;return l.value==="protagonists"?i=c.protagonists:l.value==="antagonists"?i=c.antagonists:l.value==="supporting"?i=c.supportingCharacters:l.value==="templates"&&(i=c.templates),y.value&&(i=c.searchCharacters(y.value)),i}),D=O(()=>c.selectedCharacters);function M(i){return D.value.some(n=>n.id===i)}function I(i){M(i.id)?c.deselectCharacter(i.id):c.selectCharacter(i)}function G(i){c.deselectCharacter(i)}function E(){c.clearSelectedCharacters()}function z(i){u.value=i,m.value=!0}function T(i){c.duplicateCharacter(i)}function p(i){confirm("Are you sure you want to delete this character?")&&c.deleteCharacter(i)}function S(){c.generateRandomCharacter()}function k(){m.value=!1,u.value=null}function _(i){u.value?c.updateCharacter(u.value.id,i):c.addCharacter(i),k()}return oe(()=>{c.loadFromStorage()}),(i,n)=>(a(),o("div",wt,[e("div",Vt,[n[5]||(n[5]=e("div",{class:"character-selection__title-section"},[e("h2",{class:"character-selection__title"},"Choose Your Characters"),e("p",{class:"character-selection__subtitle"}," Select characters for your story or create new ones ")],-1)),e("div",At,[g(w,{variant:"ghost",size:"small",onClick:S},{default:C(()=>n[3]||(n[3]=[$(" 🎲 Random Character ")])),_:1,__:[3]}),g(w,{variant:"primary",onClick:n[0]||(n[0]=v=>m.value=!0)},{default:C(()=>n[4]||(n[4]=[$(" + Create Character ")])),_:1,__:[4]})])]),e("div",Ut,[g(H,{modelValue:y.value,"onUpdate:modelValue":n[1]||(n[1]=v=>y.value=v),placeholder:"Search characters...",class:"character-selection__search"},{suffix:C(()=>n[6]||(n[6]=[$(" 🔍 ")])),_:1},8,["modelValue"]),e("div",Bt,[(a(!0),o(U,null,B(t.value,v=>(a(),o("button",{key:v.key,class:L(["character-selection__filter-tab",{"character-selection__filter-tab--active":l.value===v.key}]),onClick:F=>l.value=v.key},[$(d(v.label)+" ",1),e("span",It,d(v.count),1)],10,Dt))),128))])]),D.value.length?(a(),o("div",Ft,[e("h3",Ot," Selected Characters ("+d(D.value.length)+") ",1),e("div",Gt,[(a(!0),o(U,null,B(D.value,v=>(a(),o("div",{key:v.id,class:"character-selection__selected-item"},[e("span",Pt,d(v.name),1),e("span",Et,d(v.role),1),e("button",{class:"character-selection__selected-remove",onClick:F=>G(v.id)}," ✕ ",8,Nt)]))),128))]),g(w,{variant:"ghost",size:"small",onClick:E},{default:C(()=>n[7]||(n[7]=[$(" Clear All ")])),_:1,__:[7]})])):b("",!0),e("div",Lt,[(a(!0),o(U,null,B(s.value,v=>(a(),ne(ot,{key:v.id,character:v,"is-selected":M(v.id),onSelect:I,onEdit:z,onDuplicate:T,onDelete:p},null,8,["character","is-selected"]))),128)),s.value.length===0?(a(),o("div",jt,[n[9]||(n[9]=e("div",{class:"character-selection__empty-icon"},"👤",-1)),n[10]||(n[10]=e("h3",{class:"character-selection__empty-title"},"No characters found",-1)),e("p",Mt,d(y.value?"Try adjusting your search terms":"Create your first character to get started"),1),g(w,{variant:"primary",onClick:n[2]||(n[2]=v=>m.value=!0)},{default:C(()=>n[8]||(n[8]=[$(" Create Character ")])),_:1,__:[8]})])):b("",!0)]),m.value?(a(),o("div",{key:1,class:"character-selection__modal",onClick:R(k,["self"])},[g(Tt,{"editing-character":u.value,onClose:k,onSubmit:_},null,8,["editing-character"])])):b("",!0)]))}}),Rt=Y(zt,[["__scopeId","data-v-6fc75cee"]]),qt={class:"setting-card__image"},Yt={class:"setting-card__genre-badge"},Wt={class:"setting-card__content"},Ht={class:"setting-card__header"},Qt={class:"setting-card__info"},Kt={class:"setting-card__name"},Jt={class:"setting-card__meta"},Xt={class:"setting-card__type"},Zt={key:0,class:"setting-card__time"},xt={class:"setting-card__actions"},es={key:0,class:"setting-card__action",title:"Template"},ts={class:"setting-card__description"},ss={class:"setting-card__atmosphere"},as={key:0,class:"setting-card__locations"},os={class:"setting-card__location-tags"},ns={key:0,class:"setting-card__location-tag setting-card__location-tag--more"},is={class:"setting-card__details"},ls={key:0,class:"setting-card__detail"},rs={class:"setting-card__detail-value"},cs={key:1,class:"setting-card__detail"},ds={class:"setting-card__detail-value"},us={key:0,class:"setting-card__selected-indicator"},_s=q({__name:"SettingCard",props:{setting:{},isSelected:{type:Boolean}},emits:["select","edit","duplicate","delete"],setup(j){function c(t){return{urban:"🏙️",rural:"🌾",wilderness:"🌲",underground:"🕳️",space:"🚀",underwater:"🌊","magical-realm":"✨","dystopian-city":"🏭","small-town":"🏘️",castle:"🏰",spaceship:"🛸",desert:"🏜️",forest:"🌳",mountain:"⛰️",island:"🏝️"}[t]||"🌍"}function y(t){return t.split("-").map(s=>s.charAt(0).toUpperCase()+s.slice(1)).join(" ")}function l(t){return t.split("-").map(s=>s.charAt(0).toUpperCase()+s.slice(1)).join(" ")}function m(t){return t.charAt(0).toUpperCase()+t.slice(1)}function u(t){return t.charAt(0).toUpperCase()+t.slice(1)}return(t,s)=>(a(),o("div",{class:L(["setting-card",{"setting-card--selected":t.isSelected,"setting-card--template":t.setting.isTemplate}]),onClick:s[3]||(s[3]=D=>t.$emit("select",t.setting))},[e("div",qt,[e("div",{class:L(`setting-card__image-placeholder setting-card__image-placeholder--${t.setting.type}`)},d(c(t.setting.type)),3),e("div",Yt,d(y(t.setting.genre)),1)]),e("div",Wt,[e("div",Ht,[e("div",Qt,[e("h3",Kt,d(t.setting.name),1),e("div",Jt,[e("span",Xt,d(l(t.setting.type)),1),t.setting.timeOfDay?(a(),o("span",Zt,d(m(t.setting.timeOfDay)),1)):b("",!0)])]),e("div",xt,[t.setting.isTemplate?(a(),o("button",es," 📋 ")):b("",!0),e("button",{class:"setting-card__action",title:"Edit",onClick:s[0]||(s[0]=R(D=>t.$emit("edit",t.setting),["stop"]))}," ✏️ "),e("button",{class:"setting-card__action",title:"Duplicate",onClick:s[1]||(s[1]=R(D=>t.$emit("duplicate",t.setting.id),["stop"]))}," 📄 "),e("button",{class:"setting-card__action setting-card__action--danger",title:"Delete",onClick:s[2]||(s[2]=R(D=>t.$emit("delete",t.setting.id),["stop"]))}," 🗑️ ")])]),e("p",ts,d(t.setting.description),1),e("div",ss,[s[4]||(s[4]=e("strong",null,"Atmosphere:",-1)),$(" "+d(t.setting.atmosphere),1)]),t.setting.keyLocations.length?(a(),o("div",as,[s[5]||(s[5]=e("h4",{class:"setting-card__section-title"},"Key Locations:",-1)),e("div",os,[(a(!0),o(U,null,B(t.setting.keyLocations.slice(0,3),D=>(a(),o("span",{key:D,class:"setting-card__location-tag"},d(D),1))),128)),t.setting.keyLocations.length>3?(a(),o("span",ns," +"+d(t.setting.keyLocations.length-3)+" more ",1)):b("",!0)])])):b("",!0),e("div",is,[t.setting.weather?(a(),o("div",ls,[s[6]||(s[6]=e("span",{class:"setting-card__detail-label"},"Weather:",-1)),e("span",rs,d(t.setting.weather),1)])):b("",!0),t.setting.season?(a(),o("div",cs,[s[7]||(s[7]=e("span",{class:"setting-card__detail-label"},"Season:",-1)),e("span",ds,d(u(t.setting.season)),1)])):b("",!0)])]),t.isSelected?(a(),o("div",us," ✓ Selected ")):b("",!0)],2))}}),ps=Y(_s,[["__scopeId","data-v-7846e921"]]),ms={class:"setting-builder"},hs={class:"setting-builder__header"},gs={class:"setting-builder__title"},vs={class:"setting-builder__section"},ys={class:"setting-builder__row"},fs={class:"base-input"},bs=["value"],$s={class:"base-input"},ks=["value"],Cs={class:"base-input"},Ss={class:"base-input"},Ts={class:"setting-builder__section"},ws={class:"setting-builder__list"},Vs={class:"setting-builder__section"},As={class:"setting-builder__row"},Us={class:"base-input"},Bs={class:"base-input"},Ds={class:"setting-builder__checkbox"},Is={class:"setting-builder__actions"},Fs=q({__name:"SettingBuilder",props:{editingSetting:{}},emits:["close","submit"],setup(j,{emit:c}){const y=j,l=c,m=P(!1),u=Object.values(x),t=Object.values(ee),s=Z({name:"",genre:x.FANTASY,type:ee.FOREST,description:"",atmosphere:"",keyLocations:[""],timeOfDay:"",weather:"",season:"",isTemplate:!1}),D=Z({name:""});se(()=>y.editingSetting,k=>{k?(s.name=k.name,s.genre=k.genre,s.type=k.type,s.description=k.description,s.atmosphere=k.atmosphere,s.keyLocations=k.keyLocations.length?[...k.keyLocations]:[""],s.timeOfDay=k.timeOfDay||"",s.weather=k.weather||"",s.season=k.season||"",s.isTemplate=k.isTemplate):M()},{immediate:!0});function M(){s.name="",s.genre=x.FANTASY,s.type=ee.FOREST,s.description="",s.atmosphere="",s.keyLocations=[""],s.timeOfDay="",s.weather="",s.season="",s.isTemplate=!1,D.name=""}function I(k){return k.split("-").map(_=>_.charAt(0).toUpperCase()+_.slice(1)).join(" ")}function G(k){return k.split("-").map(_=>_.charAt(0).toUpperCase()+_.slice(1)).join(" ")}function E(){s.keyLocations.push("")}function z(k,_){s.keyLocations[k]=_}function T(k){s.keyLocations.length>1&&s.keyLocations.splice(k,1)}function p(){return D.name="",s.name.trim()?!0:(D.name="Setting name is required",!1)}async function S(){if(p()){m.value=!0;try{const k={name:s.name.trim(),genre:s.genre,type:s.type,description:s.description.trim(),atmosphere:s.atmosphere.trim(),keyLocations:s.keyLocations.filter(_=>_.trim()),timeOfDay:s.timeOfDay||void 0,weather:s.weather.trim()||void 0,season:s.season||void 0,isTemplate:s.isTemplate};l("submit",k)}finally{m.value=!1}}}return(k,_)=>(a(),o("div",ms,[e("div",hs,[e("h2",gs,d(k.editingSetting?"Edit Setting":"Create New Setting"),1),g(w,{variant:"ghost",size:"small",onClick:_[0]||(_[0]=i=>k.$emit("close"))},{default:C(()=>_[11]||(_[11]=[$(" ✕ ")])),_:1,__:[11]})]),e("form",{onSubmit:R(S,["prevent"]),class:"setting-builder__form"},[e("div",vs,[_[16]||(_[16]=e("h3",{class:"setting-builder__section-title"},"Basic Information",-1)),g(H,{modelValue:s.name,"onUpdate:modelValue":_[1]||(_[1]=i=>s.name=i),label:"Setting Name",placeholder:"Enter setting name",required:"",error:D.name},null,8,["modelValue","error"]),e("div",ys,[e("div",fs,[_[12]||(_[12]=e("label",{class:"base-input__label"},"Genre",-1)),N(e("select",{"onUpdate:modelValue":_[2]||(_[2]=i=>s.genre=i),class:"setting-builder__select"},[(a(!0),o(U,null,B(Q(u),i=>(a(),o("option",{key:i,value:i},d(I(i)),9,bs))),128))],512),[[K,s.genre]])]),e("div",$s,[_[13]||(_[13]=e("label",{class:"base-input__label"},"Type",-1)),N(e("select",{"onUpdate:modelValue":_[3]||(_[3]=i=>s.type=i),class:"setting-builder__select"},[(a(!0),o(U,null,B(Q(t),i=>(a(),o("option",{key:i,value:i},d(G(i)),9,ks))),128))],512),[[K,s.type]])])]),e("div",Cs,[_[14]||(_[14]=e("label",{class:"base-input__label"},"Description",-1)),N(e("textarea",{"onUpdate:modelValue":_[4]||(_[4]=i=>s.description=i),placeholder:"Describe your setting...",class:"setting-builder__textarea",rows:"3"},null,512),[[J,s.description]])]),e("div",Ss,[_[15]||(_[15]=e("label",{class:"base-input__label"},"Atmosphere",-1)),N(e("textarea",{"onUpdate:modelValue":_[5]||(_[5]=i=>s.atmosphere=i),placeholder:"Describe the mood and feeling of this place...",class:"setting-builder__textarea",rows:"2"},null,512),[[J,s.atmosphere]])])]),e("div",Ts,[_[19]||(_[19]=e("h3",{class:"setting-builder__section-title"},"Key Locations",-1)),e("div",ws,[(a(!0),o(U,null,B(s.keyLocations,(i,n)=>(a(),o("div",{key:n,class:"setting-builder__list-item"},[g(H,{"model-value":i,placeholder:"Enter a location...","onUpdate:modelValue":v=>z(n,v)},null,8,["model-value","onUpdate:modelValue"]),g(w,{variant:"ghost",size:"small",onClick:v=>T(n)},{default:C(()=>_[17]||(_[17]=[$(" ✕ ")])),_:2,__:[17]},1032,["onClick"])]))),128)),g(w,{variant:"ghost",size:"small",onClick:E},{default:C(()=>_[18]||(_[18]=[$(" + Add Location ")])),_:1,__:[18]})])]),e("div",Vs,[_[25]||(_[25]=e("h3",{class:"setting-builder__section-title"},"Environmental Details",-1)),e("div",As,[e("div",Us,[_[21]||(_[21]=e("label",{class:"base-input__label"},"Time of Day",-1)),N(e("select",{"onUpdate:modelValue":_[6]||(_[6]=i=>s.timeOfDay=i),class:"setting-builder__select"},_[20]||(_[20]=[ie('<option value="" data-v-c57cbcd3>Not specified</option><option value="dawn" data-v-c57cbcd3>Dawn</option><option value="morning" data-v-c57cbcd3>Morning</option><option value="noon" data-v-c57cbcd3>Noon</option><option value="afternoon" data-v-c57cbcd3>Afternoon</option><option value="evening" data-v-c57cbcd3>Evening</option><option value="night" data-v-c57cbcd3>Night</option><option value="midnight" data-v-c57cbcd3>Midnight</option>',8)]),512),[[K,s.timeOfDay]])]),e("div",Bs,[_[23]||(_[23]=e("label",{class:"base-input__label"},"Season",-1)),N(e("select",{"onUpdate:modelValue":_[7]||(_[7]=i=>s.season=i),class:"setting-builder__select"},_[22]||(_[22]=[ie('<option value="" data-v-c57cbcd3>Not specified</option><option value="spring" data-v-c57cbcd3>Spring</option><option value="summer" data-v-c57cbcd3>Summer</option><option value="autumn" data-v-c57cbcd3>Autumn</option><option value="winter" data-v-c57cbcd3>Winter</option>',5)]),512),[[K,s.season]])])]),g(H,{modelValue:s.weather,"onUpdate:modelValue":_[8]||(_[8]=i=>s.weather=i),label:"Weather",placeholder:"e.g., Sunny, Rainy, Stormy, Misty"},null,8,["modelValue"]),e("div",Ds,[e("label",null,[N(e("input",{type:"checkbox","onUpdate:modelValue":_[9]||(_[9]=i=>s.isTemplate=i)},null,512),[[ae,s.isTemplate]]),_[24]||(_[24]=e("span",null,"Save as template for future use",-1))])])]),e("div",Is,[g(w,{variant:"secondary",onClick:_[10]||(_[10]=i=>k.$emit("close"))},{default:C(()=>_[26]||(_[26]=[$(" Cancel ")])),_:1,__:[26]}),g(w,{type:"submit",loading:m.value},{default:C(()=>[$(d(k.editingSetting?"Update Setting":"Create Setting"),1)]),_:1},8,["loading"])])],32)]))}}),Os=Y(Fs,[["__scopeId","data-v-c57cbcd3"]]),Gs={class:"setting-selection"},Ps={class:"setting-selection__header"},Es={class:"setting-selection__actions"},Ns={class:"setting-selection__filters"},Ls={class:"setting-selection__filter-tabs"},js=["onClick"],Ms={class:"setting-selection__filter-count"},zs={class:"setting-selection__genre-filters"},Rs=["onClick"],qs={key:0,class:"setting-selection__selected"},Ys={class:"setting-selection__selected-title"},Ws={class:"setting-selection__selected-list"},Hs={class:"setting-selection__selected-name"},Qs={class:"setting-selection__selected-type"},Ks=["onClick"],Js={class:"setting-selection__grid"},Xs={key:0,class:"setting-selection__empty"},Zs={class:"setting-selection__empty-text"},xs=q({__name:"SettingSelection",setup(j){const c=re(),y=P(""),l=P("all"),m=P(null),u=P(!1),t=P(null),s=Object.values(x),D=O(()=>[{key:"all",label:"All",count:c.settings.length},{key:"templates",label:"Templates",count:c.templates.length}]),M=O(()=>{let V=c.settings;return l.value==="templates"&&(V=c.templates),m.value&&(V=V.filter(A=>A.genre===m.value)),y.value&&(V=c.searchSettings(y.value)),V}),I=O(()=>c.selectedSettings);function G(V){return I.value.some(A=>A.id===V)}function E(V){G(V.id)?c.deselectSetting(V.id):c.selectSetting(V)}function z(V){c.deselectSetting(V)}function T(){c.clearSelectedSettings()}function p(V){t.value=V,u.value=!0}function S(V){c.duplicateSetting(V)}function k(V){confirm("Are you sure you want to delete this setting?")&&c.deleteSetting(V)}function _(){c.generateRandomSetting()}function i(V){m.value=m.value===V?null:V}function n(V){return V.split("-").map(A=>A.charAt(0).toUpperCase()+A.slice(1)).join(" ")}function v(V){return V.split("-").map(A=>A.charAt(0).toUpperCase()+A.slice(1)).join(" ")}function F(){u.value=!1,t.value=null}function W(V){t.value?c.updateSetting(t.value.id,V):c.addSetting(V),F()}return oe(()=>{c.loadFromStorage()}),(V,A)=>(a(),o("div",Gs,[e("div",Ps,[A[5]||(A[5]=e("div",{class:"setting-selection__title-section"},[e("h2",{class:"setting-selection__title"},"Choose Your Settings"),e("p",{class:"setting-selection__subtitle"}," Select the worlds and locations where your story takes place ")],-1)),e("div",Es,[g(w,{variant:"ghost",size:"small",onClick:_},{default:C(()=>A[3]||(A[3]=[$(" 🎲 Random Setting ")])),_:1,__:[3]}),g(w,{variant:"primary",onClick:A[0]||(A[0]=h=>u.value=!0)},{default:C(()=>A[4]||(A[4]=[$(" + Create Setting ")])),_:1,__:[4]})])]),e("div",Ns,[g(H,{modelValue:y.value,"onUpdate:modelValue":A[1]||(A[1]=h=>y.value=h),placeholder:"Search settings...",class:"setting-selection__search"},{suffix:C(()=>A[6]||(A[6]=[$(" 🔍 ")])),_:1},8,["modelValue"]),e("div",Ls,[(a(!0),o(U,null,B(D.value,h=>(a(),o("button",{key:h.key,class:L(["setting-selection__filter-tab",{"setting-selection__filter-tab--active":l.value===h.key}]),onClick:r=>l.value=h.key},[$(d(h.label)+" ",1),e("span",Ms,d(h.count),1)],10,js))),128))]),e("div",zs,[(a(!0),o(U,null,B(Q(s),h=>(a(),o("button",{key:h,class:L(["setting-selection__genre-filter",{"setting-selection__genre-filter--active":m.value===h}]),onClick:r=>i(h)},d(n(h)),11,Rs))),128))])]),I.value.length?(a(),o("div",qs,[e("h3",Ys," Selected Settings ("+d(I.value.length)+") ",1),e("div",Ws,[(a(!0),o(U,null,B(I.value,h=>(a(),o("div",{key:h.id,class:"setting-selection__selected-item"},[e("span",Hs,d(h.name),1),e("span",Qs,d(v(h.type)),1),e("button",{class:"setting-selection__selected-remove",onClick:r=>z(h.id)}," ✕ ",8,Ks)]))),128))]),g(w,{variant:"ghost",size:"small",onClick:T},{default:C(()=>A[7]||(A[7]=[$(" Clear All ")])),_:1,__:[7]})])):b("",!0),e("div",Js,[(a(!0),o(U,null,B(M.value,h=>(a(),ne(ps,{key:h.id,setting:h,"is-selected":G(h.id),onSelect:E,onEdit:p,onDuplicate:S,onDelete:k},null,8,["setting","is-selected"]))),128)),M.value.length===0?(a(),o("div",Xs,[A[9]||(A[9]=e("div",{class:"setting-selection__empty-icon"},"🌍",-1)),A[10]||(A[10]=e("h3",{class:"setting-selection__empty-title"},"No settings found",-1)),e("p",Zs,d(y.value?"Try adjusting your search terms":"Create your first setting to get started"),1),g(w,{variant:"primary",onClick:A[2]||(A[2]=h=>u.value=!0)},{default:C(()=>A[8]||(A[8]=[$(" Create Setting ")])),_:1,__:[8]})])):b("",!0)]),u.value?(a(),o("div",{key:1,class:"setting-selection__modal",onClick:R(F,["self"])},[g(Os,{"editing-setting":t.value,onClose:F,onSubmit:W},null,8,["editing-setting"])])):b("",!0)]))}}),ea=Y(xs,[["__scopeId","data-v-41d09dc7"]]),ta={class:"theme-card__header"},sa={class:"theme-card__info"},aa={class:"theme-card__primary"},oa={class:"theme-card__meta"},na={class:"theme-card__actions"},ia={key:0,class:"theme-card__action",title:"Template"},la={class:"theme-card__description"},ra={key:0,class:"theme-card__secondary"},ca={class:"theme-card__secondary-themes"},da={class:"theme-card__icon"},ua={key:1,class:"theme-card__selected-indicator"},_a=q({__name:"ThemeCard",props:{theme:{},isSelected:{type:Boolean}},emits:["select","edit","duplicate","delete"],setup(j){function c(u){return u.split("-").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" ")}function y(u){return u.charAt(0).toUpperCase()+u.slice(1)}function l(u){return u.split("-").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" ")}function m(u){return{betrayal:"🗡️",friendship:"🤝",love:"❤️",revenge:"⚔️",redemption:"🕊️",sacrifice:"⚖️",power:"👑",freedom:"🦅",justice:"⚖️",survival:"🛡️",discovery:"🔍",transformation:"🦋","time-travel":"⏰",family:"👨‍👩‍👧‍👦",identity:"🪞","coming-of-age":"🌱"}[u]||"✨"}return(u,t)=>(a(),o("div",{class:L(["theme-card",{"theme-card--selected":u.isSelected,"theme-card--template":u.theme.isTemplate,[`theme-card--${u.theme.mood}`]:!0}]),onClick:t[3]||(t[3]=s=>u.$emit("select",u.theme))},[e("div",ta,[e("div",sa,[e("h3",aa,d(c(u.theme.primary)),1),e("div",oa,[e("span",{class:L(`theme-card__mood theme-card__mood--${u.theme.mood}`)},d(y(u.theme.mood)),3),e("span",{class:L(`theme-card__conflict theme-card__conflict--${u.theme.conflictType}`)},d(l(u.theme.conflictType)),3)])]),e("div",na,[u.theme.isTemplate?(a(),o("button",ia," 📋 ")):b("",!0),e("button",{class:"theme-card__action",title:"Edit",onClick:t[0]||(t[0]=R(s=>u.$emit("edit",u.theme),["stop"]))}," ✏️ "),e("button",{class:"theme-card__action",title:"Duplicate",onClick:t[1]||(t[1]=R(s=>u.$emit("duplicate",u.theme.id),["stop"]))}," 📄 "),e("button",{class:"theme-card__action theme-card__action--danger",title:"Delete",onClick:t[2]||(t[2]=R(s=>u.$emit("delete",u.theme.id),["stop"]))}," 🗑️ ")])]),e("p",la,d(u.theme.description),1),u.theme.secondary.length?(a(),o("div",ra,[t[4]||(t[4]=e("h4",{class:"theme-card__section-title"},"Secondary Themes:",-1)),e("div",ca,[(a(!0),o(U,null,B(u.theme.secondary,s=>(a(),o("span",{key:s,class:"theme-card__secondary-theme"},d(c(s)),1))),128))])])):b("",!0),e("div",da,d(m(u.theme.primary)),1),u.isSelected?(a(),o("div",ua," ✓ Selected ")):b("",!0)],2))}}),pa=Y(_a,[["__scopeId","data-v-840e7c35"]]),ma={class:"theme-builder"},ha={class:"theme-builder__header"},ga={class:"theme-builder__title"},va={class:"theme-builder__section"},ya={class:"base-input"},fa=["value"],ba={class:"base-input"},$a={class:"theme-builder__section"},ka={class:"theme-builder__theme-grid"},Ca=["value","checked","onChange"],Sa={class:"theme-builder__theme-label"},Ta={class:"theme-builder__section"},wa={class:"theme-builder__row"},Va={class:"base-input"},Aa={class:"base-input"},Ua={class:"theme-builder__checkbox"},Ba={key:0,class:"theme-builder__section"},Da={class:"theme-builder__suggestions"},Ia=["onClick"],Fa={class:"theme-builder__suggestion-header"},Oa={class:"theme-builder__suggestion-secondary"},Ga={class:"theme-builder__suggestion-description"},Pa={class:"theme-builder__actions"},Ea=q({__name:"ThemeBuilder",props:{editingTheme:{}},emits:["close","submit"],setup(j,{emit:c}){const y=j,l=c,m=P(!1),u=Object.values(te),t=ce,s=Z({primary:te.REDEMPTION,secondary:[],description:"",conflictType:"both",mood:"mixed",isTemplate:!1}),D=O(()=>u.filter(T=>T!==s.primary));se(()=>y.editingTheme,T=>{T?(s.primary=T.primary,s.secondary=[...T.secondary],s.description=T.description,s.conflictType=T.conflictType,s.mood=T.mood,s.isTemplate=T.isTemplate):M()},{immediate:!0});function M(){s.primary=te.REDEMPTION,s.secondary=[],s.description="",s.conflictType="both",s.mood="mixed",s.isTemplate=!1}function I(T){return T.split("-").map(p=>p.charAt(0).toUpperCase()+p.slice(1)).join(" ")}function G(T){const p=s.secondary.indexOf(T);p>-1?s.secondary.splice(p,1):s.secondary.push(T)}function E(T){s.primary=T.primary,s.secondary=[...T.secondary],s.description=T.description}async function z(){m.value=!0;try{const T={primary:s.primary,secondary:[...s.secondary],description:s.description.trim()||`A story exploring ${I(s.primary)}${s.secondary.length?` with elements of ${s.secondary.map(I).join(", ")}`:""}`,conflictType:s.conflictType,mood:s.mood,isTemplate:s.isTemplate};l("submit",T)}finally{m.value=!1}}return(T,p)=>(a(),o("div",ma,[e("div",ha,[e("h2",ga,d(T.editingTheme?"Edit Theme":"Create New Theme"),1),g(w,{variant:"ghost",size:"small",onClick:p[0]||(p[0]=S=>T.$emit("close"))},{default:C(()=>p[7]||(p[7]=[$(" ✕ ")])),_:1,__:[7]})]),e("form",{onSubmit:R(z,["prevent"]),class:"theme-builder__form"},[e("div",va,[p[10]||(p[10]=e("h3",{class:"theme-builder__section-title"},"Primary Theme",-1)),e("div",ya,[p[8]||(p[8]=e("label",{class:"base-input__label"},"Main Theme",-1)),N(e("select",{"onUpdate:modelValue":p[1]||(p[1]=S=>s.primary=S),class:"theme-builder__select"},[(a(!0),o(U,null,B(Q(u),S=>(a(),o("option",{key:S,value:S},d(I(S)),9,fa))),128))],512),[[K,s.primary]])]),e("div",ba,[p[9]||(p[9]=e("label",{class:"base-input__label"},"Description",-1)),N(e("textarea",{"onUpdate:modelValue":p[2]||(p[2]=S=>s.description=S),placeholder:"Describe how this theme will be explored in your story...",class:"theme-builder__textarea",rows:"3"},null,512),[[J,s.description]])])]),e("div",$a,[p[11]||(p[11]=e("h3",{class:"theme-builder__section-title"},"Secondary Themes",-1)),p[12]||(p[12]=e("p",{class:"theme-builder__section-description"}," Choose additional themes that complement your primary theme ",-1)),e("div",ka,[(a(!0),o(U,null,B(D.value,S=>(a(),o("label",{key:S,class:"theme-builder__theme-option"},[e("input",{type:"checkbox",value:S,checked:s.secondary.includes(S),onChange:k=>G(S)},null,40,Ca),e("span",Sa,d(I(S)),1)]))),128))])]),e("div",Ta,[p[18]||(p[18]=e("h3",{class:"theme-builder__section-title"},"Theme Properties",-1)),e("div",wa,[e("div",Va,[p[14]||(p[14]=e("label",{class:"base-input__label"},"Conflict Type",-1)),N(e("select",{"onUpdate:modelValue":p[3]||(p[3]=S=>s.conflictType=S),class:"theme-builder__select"},p[13]||(p[13]=[e("option",{value:"internal"},"Internal - Character vs Self",-1),e("option",{value:"external"},"External - Character vs World",-1),e("option",{value:"both"},"Both - Internal and External",-1)]),512),[[K,s.conflictType]])]),e("div",Aa,[p[16]||(p[16]=e("label",{class:"base-input__label"},"Mood",-1)),N(e("select",{"onUpdate:modelValue":p[4]||(p[4]=S=>s.mood=S),class:"theme-builder__select"},p[15]||(p[15]=[e("option",{value:"light"},"Light - Hopeful and uplifting",-1),e("option",{value:"dark"},"Dark - Serious and somber",-1),e("option",{value:"neutral"},"Neutral - Balanced tone",-1),e("option",{value:"mixed"},"Mixed - Varies throughout",-1)]),512),[[K,s.mood]])])]),e("div",Ua,[e("label",null,[N(e("input",{type:"checkbox","onUpdate:modelValue":p[5]||(p[5]=S=>s.isTemplate=S)},null,512),[[ae,s.isTemplate]]),p[17]||(p[17]=e("span",null,"Save as template for future use",-1))])])]),Q(t).length?(a(),o("div",Ba,[p[19]||(p[19]=e("h3",{class:"theme-builder__section-title"},"Suggested Combinations",-1)),p[20]||(p[20]=e("p",{class:"theme-builder__section-description"}," Popular theme combinations that work well together ",-1)),e("div",Da,[(a(!0),o(U,null,B(Q(t),S=>(a(),o("div",{key:S.primary,class:"theme-builder__suggestion",onClick:k=>E(S)},[e("div",Fa,[e("strong",null,d(I(S.primary)),1),e("span",Oa," + "+d(S.secondary.map(I).join(", ")),1)]),e("p",Ga,d(S.description),1)],8,Ia))),128))])])):b("",!0),e("div",Pa,[g(w,{variant:"secondary",onClick:p[6]||(p[6]=S=>T.$emit("close"))},{default:C(()=>p[21]||(p[21]=[$(" Cancel ")])),_:1,__:[21]}),g(w,{type:"submit",loading:m.value},{default:C(()=>[$(d(T.editingTheme?"Update Theme":"Create Theme"),1)]),_:1},8,["loading"])])],32)]))}}),Na=Y(Ea,[["__scopeId","data-v-2d71b8f0"]]),La={class:"theme-selection"},ja={class:"theme-selection__header"},Ma={class:"theme-selection__actions"},za={class:"theme-selection__filters"},Ra={class:"theme-selection__filter-tabs"},qa=["onClick"],Ya={class:"theme-selection__filter-count"},Wa={class:"theme-selection__mood-filters"},Ha=["onClick"],Qa={key:0,class:"theme-selection__suggestions"},Ka={class:"theme-selection__suggestion-cards"},Ja=["onClick"],Xa={class:"theme-selection__suggestion-header"},Za={class:"theme-selection__suggestion-description"},xa={key:1,class:"theme-selection__selected"},eo={class:"theme-selection__selected-title"},to={class:"theme-selection__selected-list"},so={class:"theme-selection__selected-primary"},ao={key:0,class:"theme-selection__selected-secondary"},oo=["onClick"],no={class:"theme-selection__grid"},io={key:0,class:"theme-selection__empty"},lo={class:"theme-selection__empty-text"},ro=q({__name:"ThemeSelection",setup(j){const c=de(),y=P(""),l=P("all"),m=P(null),u=P(!1),t=P(null),s=["light","dark","neutral","mixed"],D=ce,M=O(()=>[{key:"all",label:"All",count:c.themes.length},{key:"templates",label:"Templates",count:c.templates.length},{key:"internal",label:"Internal Conflict",count:c.themesByConflictType.internal?.length||0},{key:"external",label:"External Conflict",count:c.themesByConflictType.external?.length||0}]),I=O(()=>{let h=c.themes;return l.value==="templates"?h=c.templates:l.value==="internal"?h=c.getThemesByConflictType("internal"):l.value==="external"&&(h=c.getThemesByConflictType("external")),m.value&&(h=h.filter(r=>r.mood===m.value)),y.value&&(h=c.searchThemes(y.value)),h}),G=O(()=>c.selectedThemes);function E(h){return G.value.some(r=>r.id===h)}function z(h){E(h.id)?c.deselectTheme(h.id):c.selectTheme(h)}function T(h){c.deselectTheme(h)}function p(){c.clearSelectedThemes()}function S(h){t.value=h,u.value=!0}function k(h){c.duplicateTheme(h)}function _(h){confirm("Are you sure you want to delete this theme?")&&c.deleteTheme(h)}function i(){c.generateRandomTheme()}function n(h){const r=c.createThemeFromSuggestion(h);r&&c.selectTheme(r)}function v(h){m.value=m.value===h?null:h}function F(h){return h.split("-").map(r=>r.charAt(0).toUpperCase()+r.slice(1)).join(" ")}function W(h){return h.charAt(0).toUpperCase()+h.slice(1)}function V(){u.value=!1,t.value=null}function A(h){t.value?c.updateTheme(t.value.id,h):c.addTheme(h),V()}return oe(()=>{c.loadFromStorage()}),(h,r)=>(a(),o("div",La,[e("div",ja,[r[5]||(r[5]=e("div",{class:"theme-selection__title-section"},[e("h2",{class:"theme-selection__title"},"Choose Your Themes"),e("p",{class:"theme-selection__subtitle"}," Select the central themes and conflicts that will drive your story ")],-1)),e("div",Ma,[g(w,{variant:"ghost",size:"small",onClick:i},{default:C(()=>r[3]||(r[3]=[$(" 🎲 Random Theme ")])),_:1,__:[3]}),g(w,{variant:"primary",onClick:r[0]||(r[0]=f=>u.value=!0)},{default:C(()=>r[4]||(r[4]=[$(" + Create Theme ")])),_:1,__:[4]})])]),e("div",za,[g(H,{modelValue:y.value,"onUpdate:modelValue":r[1]||(r[1]=f=>y.value=f),placeholder:"Search themes...",class:"theme-selection__search"},{suffix:C(()=>r[6]||(r[6]=[$(" 🔍 ")])),_:1},8,["modelValue"]),e("div",Ra,[(a(!0),o(U,null,B(M.value,f=>(a(),o("button",{key:f.key,class:L(["theme-selection__filter-tab",{"theme-selection__filter-tab--active":l.value===f.key}]),onClick:X=>l.value=f.key},[$(d(f.label)+" ",1),e("span",Ya,d(f.count),1)],10,qa))),128))]),e("div",Wa,[(a(),o(U,null,B(s,f=>e("button",{key:f,class:L(["theme-selection__mood-filter",`theme-selection__mood-filter--${f}`,{"theme-selection__mood-filter--active":m.value===f}]),onClick:X=>v(f)},d(W(f)),11,Ha)),64))])]),Q(D).length&&!y.value?(a(),o("div",Qa,[r[9]||(r[9]=e("h3",{class:"theme-selection__suggestions-title"},"Popular Combinations",-1)),e("div",Ka,[(a(!0),o(U,null,B(Q(D).slice(0,4),f=>(a(),o("div",{key:f.primary,class:"theme-selection__suggestion-card",onClick:X=>n(f)},[e("div",Xa,[e("strong",null,d(F(f.primary)),1),r[7]||(r[7]=e("span",{class:"theme-selection__suggestion-plus"},"+",-1)),e("span",null,d(f.secondary.map(F).join(", ")),1)]),e("p",Za,d(f.description),1),g(w,{variant:"ghost",size:"small"},{default:C(()=>r[8]||(r[8]=[$(" Use This Combination ")])),_:1,__:[8]})],8,Ja))),128))])])):b("",!0),G.value.length?(a(),o("div",xa,[e("h3",eo," Selected Themes ("+d(G.value.length)+") ",1),e("div",to,[(a(!0),o(U,null,B(G.value,f=>(a(),o("div",{key:f.id,class:"theme-selection__selected-item"},[e("span",so,d(F(f.primary)),1),f.secondary.length?(a(),o("span",ao," + "+d(f.secondary.length)+" more ",1)):b("",!0),e("span",{class:L(`theme-selection__selected-mood theme-selection__selected-mood--${f.mood}`)},d(W(f.mood)),3),e("button",{class:"theme-selection__selected-remove",onClick:X=>T(f.id)}," ✕ ",8,oo)]))),128))]),g(w,{variant:"ghost",size:"small",onClick:p},{default:C(()=>r[10]||(r[10]=[$(" Clear All ")])),_:1,__:[10]})])):b("",!0),e("div",no,[(a(!0),o(U,null,B(I.value,f=>(a(),ne(pa,{key:f.id,theme:f,"is-selected":E(f.id),onSelect:z,onEdit:S,onDuplicate:k,onDelete:_},null,8,["theme","is-selected"]))),128)),I.value.length===0?(a(),o("div",io,[r[12]||(r[12]=e("div",{class:"theme-selection__empty-icon"},"🎭",-1)),r[13]||(r[13]=e("h3",{class:"theme-selection__empty-title"},"No themes found",-1)),e("p",lo,d(y.value?"Try adjusting your search terms":"Create your first theme to get started"),1),g(w,{variant:"primary",onClick:r[2]||(r[2]=f=>u.value=!0)},{default:C(()=>r[11]||(r[11]=[$(" Create Theme ")])),_:1,__:[11]})])):b("",!0)]),u.value?(a(),o("div",{key:2,class:"theme-selection__modal",onClick:R(V,["self"])},[g(Na,{"editing-theme":t.value,onClose:V,onSubmit:A},null,8,["editing-theme"])])):b("",!0)]))}}),co=Y(ro,[["__scopeId","data-v-d6c306ac"]]),uo={class:"story-generator"},_o={class:"story-generator__content"},po={key:0,class:"story-generator__step"},mo={class:"story-generator__step-actions"},ho={key:1,class:"story-generator__step"},go={class:"story-generator__step-actions"},vo={key:2,class:"story-generator__step"},yo={class:"story-generator__step-actions"},fo={key:3,class:"story-generator__step"},bo={class:"story-generator__generation"},$o={class:"story-generator__options"},ko={class:"story-generator__option-group"},Co={class:"story-generator__option-buttons"},So=["onClick"],To={class:"story-generator__option-label"},wo={class:"story-generator__option-description"},Vo={class:"story-generator__option-group"},Ao={class:"story-generator__option-buttons"},Uo=["onClick"],Bo={class:"story-generator__option-label"},Do={class:"story-generator__option-description"},Io={class:"story-generator__option-group"},Fo={class:"story-generator__checkboxes"},Oo={class:"story-generator__checkbox"},Go=["checked"],Po={class:"story-generator__checkbox"},Eo=["checked"],No={class:"story-generator__generation-actions"},Lo={key:0,class:"story-generator__progress"},jo={class:"story-generator__progress-text"},Mo={key:4,class:"story-generator__step"},zo={class:"story-generator__complete"},Ro={key:0,class:"story-generator__story-preview"},qo={class:"story-generator__story-title"},Yo={class:"story-generator__story-summary"},Wo={class:"story-generator__story-stats"},Ho={class:"story-generator__stat"},Qo={class:"story-generator__stat-value"},Ko={class:"story-generator__stat"},Jo={class:"story-generator__stat-value"},Xo={class:"story-generator__stat"},Zo={class:"story-generator__stat-value"},xo={class:"story-generator__stat"},en={class:"story-generator__stat-value"},tn={class:"story-generator__complete-actions"},sn=q({__name:"StoryGenerator",setup(j){const c=he(),y=me(),l=le(),m=re(),u=de(),t=P(""),s=[{key:"characters",label:"Characters"},{key:"settings",label:"Settings"},{key:"themes",label:"Themes"},{key:"generation",label:"Generate"},{key:"complete",label:"Complete"}],D=[{value:"short",label:"Short",description:"~2,000 words, 10 min read"},{value:"medium",label:"Medium",description:"~5,000 words, 25 min read"},{value:"long",label:"Long",description:"~10,000 words, 50 min read"}],M=[{value:"first",label:"First Person",description:"I, me, my"},{value:"third-limited",label:"Third Person Limited",description:"He, she, they (one viewpoint)"},{value:"third-omniscient",label:"Third Person Omniscient",description:"All-knowing narrator"}],I=O(()=>y.generatorState.currentStep),G=O(()=>y.currentStepIndex),E=O(()=>y.generatorState.generationOptions),z=O(()=>y.generatorState.isGenerating),T=O(()=>y.generatorState.progress),p=O(()=>y.currentStory),S=O(()=>l.selectedCharacters.length>0),k=O(()=>m.selectedSettings.length>0),_=O(()=>u.selectedThemes.length>0);function i(h,r){r<=G.value&&y.setCurrentStep(h)}function n(){I.value==="themes"&&(y.generatorState.selectedCharacters=[...l.selectedCharacters],y.generatorState.selectedSettings=[...m.selectedSettings],y.generatorState.selectedThemes=[...u.selectedThemes]),y.nextStep()}function v(){y.previousStep()}function F(h,r){y.updateGenerationOptions({[h]:r})}async function W(){t.value="Analyzing your selections...";try{await y.generateStory()?t.value="Story generated successfully!":t.value="Failed to generate story. Please try again."}catch(h){console.error("Story generation failed:",h),t.value="An error occurred during generation."}}function V(){y.resetGenerator(),l.clearSelectedCharacters(),m.clearSelectedSettings(),u.clearSelectedThemes()}function A(){p.value&&c.push(`/story/${p.value.id}`)}return(h,r)=>(a(),o("div",uo,[r[22]||(r[22]=e("div",{class:"story-generator__header"},[e("h1",{class:"story-generator__title"},"AI Story Builder"),e("p",{class:"story-generator__subtitle"}," Create compelling stories by selecting characters, settings, and themes ")],-1)),g(Ue,{steps:s,"current-step-index":G.value,onStepClick:i},null,8,["current-step-index"]),e("div",_o,[I.value==="characters"?(a(),o("div",po,[g(Rt),e("div",mo,[g(w,{variant:"primary",disabled:!S.value,onClick:n},{default:C(()=>r[2]||(r[2]=[$(" Continue to Settings ")])),_:1,__:[2]},8,["disabled"])])])):b("",!0),I.value==="settings"?(a(),o("div",ho,[g(ea),e("div",go,[g(w,{variant:"secondary",onClick:v},{default:C(()=>r[3]||(r[3]=[$(" Back to Characters ")])),_:1,__:[3]}),g(w,{variant:"primary",disabled:!k.value,onClick:n},{default:C(()=>r[4]||(r[4]=[$(" Continue to Themes ")])),_:1,__:[4]},8,["disabled"])])])):b("",!0),I.value==="themes"?(a(),o("div",vo,[g(co),e("div",yo,[g(w,{variant:"secondary",onClick:v},{default:C(()=>r[5]||(r[5]=[$(" Back to Settings ")])),_:1,__:[5]}),g(w,{variant:"primary",disabled:!_.value,onClick:n},{default:C(()=>r[6]||(r[6]=[$(" Generate Story ")])),_:1,__:[6]},8,["disabled"])])])):b("",!0),I.value==="generation"?(a(),o("div",fo,[e("div",bo,[r[14]||(r[14]=e("div",{class:"story-generator__generation-header"},[e("h2",{class:"story-generator__generation-title"},"Story Generation Options"),e("p",{class:"story-generator__generation-subtitle"}," Customize how your story will be generated ")],-1)),e("div",$o,[e("div",ko,[r[7]||(r[7]=e("h3",{class:"story-generator__option-title"},"Story Length",-1)),e("div",Co,[(a(),o(U,null,B(D,f=>e("button",{key:f.value,class:L(["story-generator__option-button",{"story-generator__option-button--active":E.value.length===f.value}]),onClick:X=>F("length",f.value)},[e("div",To,d(f.label),1),e("div",wo,d(f.description),1)],10,So)),64))])]),e("div",Vo,[r[8]||(r[8]=e("h3",{class:"story-generator__option-title"},"Narrative Perspective",-1)),e("div",Ao,[(a(),o(U,null,B(M,f=>e("button",{key:f.value,class:L(["story-generator__option-button",{"story-generator__option-button--active":E.value.narrativePerspective===f.value}]),onClick:X=>F("narrativePerspective",f.value)},[e("div",Bo,d(f.label),1),e("div",Do,d(f.description),1)],10,Uo)),64))])]),e("div",Io,[r[11]||(r[11]=e("h3",{class:"story-generator__option-title"},"Additional Options",-1)),e("div",Fo,[e("label",Oo,[e("input",{type:"checkbox",checked:E.value.includeDialogue,onChange:r[0]||(r[0]=f=>F("includeDialogue",f.target.checked))},null,40,Go),r[9]||(r[9]=e("span",null,"Include dialogue between characters",-1))]),e("label",Po,[e("input",{type:"checkbox",checked:E.value.useAI,onChange:r[1]||(r[1]=f=>F("useAI",f.target.checked))},null,40,Eo),r[10]||(r[10]=e("span",null,"Use AI assistance for enhanced content (coming soon)",-1))])])])]),e("div",No,[g(w,{variant:"secondary",onClick:v},{default:C(()=>r[12]||(r[12]=[$(" Back to Themes ")])),_:1,__:[12]}),g(w,{variant:"primary",loading:z.value,onClick:W},{default:C(()=>[$(d(z.value?"Generating Story...":"Generate My Story"),1)]),_:1},8,["loading"])]),z.value?(a(),o("div",Lo,[r[13]||(r[13]=e("h3",{class:"story-generator__progress-title"},"Generating Your Story",-1)),g(be,{progress:T.value},null,8,["progress"]),e("p",jo,d(t.value),1)])):b("",!0)])])):b("",!0),I.value==="complete"?(a(),o("div",Mo,[e("div",zo,[r[21]||(r[21]=e("div",{class:"story-generator__complete-header"},[e("div",{class:"story-generator__complete-icon"},"🎉"),e("h2",{class:"story-generator__complete-title"},"Story Generated Successfully!"),e("p",{class:"story-generator__complete-subtitle"}," Your story outline has been created and is ready for editing ")],-1)),p.value?(a(),o("div",Ro,[e("h3",qo,d(p.value.outline.title),1),e("p",Yo,d(p.value.outline.summary),1),e("div",Wo,[e("div",Ho,[r[15]||(r[15]=e("span",{class:"story-generator__stat-label"},"Characters:",-1)),e("span",Qo,d(p.value.outline.characters.length),1)]),e("div",Ko,[r[16]||(r[16]=e("span",{class:"story-generator__stat-label"},"Settings:",-1)),e("span",Jo,d(p.value.outline.settings.length),1)]),e("div",Xo,[r[17]||(r[17]=e("span",{class:"story-generator__stat-label"},"Plot Points:",-1)),e("span",Zo,d(p.value.outline.plotPoints.length),1)]),e("div",xo,[r[18]||(r[18]=e("span",{class:"story-generator__stat-label"},"Est. Reading Time:",-1)),e("span",en,d(p.value.outline.estimatedReadingTime)+" min",1)])])])):b("",!0),e("div",tn,[g(w,{variant:"secondary",onClick:V},{default:C(()=>r[19]||(r[19]=[$(" Create Another Story ")])),_:1,__:[19]}),g(w,{variant:"primary",onClick:A},{default:C(()=>r[20]||(r[20]=[$(" View & Edit Story ")])),_:1,__:[20]})])])])):b("",!0)])]))}}),an=Y(sn,[["__scopeId","data-v-1ca8f477"]]),on={class:"generator-view"},nn=q({__name:"GeneratorView",setup(j){return(c,y)=>(a(),o("div",on,[g(an)]))}}),cn=Y(nn,[["__scopeId","data-v-2313f457"]]);export{cn as default};
