import{d as C,E as b,f as p,I as $,y as B,c as a,a as t,l,m as _,J as x,t as e,F as m,r as w,g as d,o as n,_ as T}from"./index-SVRTPCSQ.js";import{B as u}from"./BaseButton-BA0mRaoL.js";const L={class:"story-view"},R={key:0,class:"story-view__content"},A={class:"story-view__header"},D={class:"story-view__actions"},F={class:"story-view__story"},V={class:"story-view__title"},N={class:"story-view__meta"},z={class:"story-view__meta-item"},E={class:"story-view__meta-value"},I={class:"story-view__meta-item"},M={class:"story-view__meta-value"},U={class:"story-view__meta-item"},j={class:"story-view__meta-value"},G={class:"story-view__meta-item"},H={class:"story-view__meta-value"},J={class:"story-view__summary"},P={class:"story-view__characters"},W={class:"story-view__character-list"},q={class:"story-view__character-role"},K={class:"story-view__character-description"},O={class:"story-view__settings"},Q={class:"story-view__setting-list"},X={class:"story-view__setting-type"},Y={class:"story-view__setting-description"},Z={class:"story-view__content-section"},tt=["innerHTML"],st={key:1,class:"story-view__not-found"},et=C({__name:"StoryView",setup(ot){const h=$(),v=b(),o=p(()=>{const r=h.params.id;return v.getStoryById(r)}),g=p(()=>o.value?o.value.content.replace(/^# (.+)$/gm,"<h1>$1</h1>").replace(/^## (.+)$/gm,"<h2>$1</h2>").replace(/^### (.+)$/gm,"<h3>$1</h3>").replace(/\*(.+?)\*/g,"<em>$1</em>").replace(/\*\*(.+?)\*\*/g,"<strong>$1</strong>").replace(/\n\n/g,"</p><p>").replace(/^(.+)$/gm,"<p>$1</p>").replace(/<p><h/g,"<h").replace(/<\/h([1-6])><\/p>/g,"</h$1>"):"");function c(r){return r.split("-").map(s=>s.charAt(0).toUpperCase()+s.slice(1)).join(" ")}function f(r){return r.charAt(0).toUpperCase()+r.slice(1)}function S(r){return r.split("-").map(s=>s.charAt(0).toUpperCase()+s.slice(1)).join(" ")}function k(r){return new Date(r).toLocaleDateString()}return B(()=>{v.loadFromStorage()}),(r,s)=>{const y=x("router-link");return n(),a("div",L,[o.value?(n(),a("div",R,[t("div",A,[l(y,{to:"/library",class:"story-view__back"},{default:_(()=>s[0]||(s[0]=[d(" ← Back to Library ")])),_:1,__:[0]}),t("div",D,[l(u,{variant:"ghost",size:"small"},{default:_(()=>s[1]||(s[1]=[d(" 🔊 Read Aloud ")])),_:1,__:[1]}),l(u,{variant:"ghost",size:"small"},{default:_(()=>s[2]||(s[2]=[d(" 📄 Export PDF ")])),_:1,__:[2]}),l(u,{variant:"ghost",size:"small"},{default:_(()=>s[3]||(s[3]=[d(" 🔗 Share ")])),_:1,__:[3]})])]),t("div",F,[t("h1",V,e(o.value.outline.title),1),t("div",N,[t("div",z,[s[4]||(s[4]=t("span",{class:"story-view__meta-label"},"Genre:",-1)),t("span",E,e(c(o.value.outline.genre)),1)]),t("div",I,[s[5]||(s[5]=t("span",{class:"story-view__meta-label"},"Reading Time:",-1)),t("span",M,e(o.value.outline.estimatedReadingTime)+" minutes",1)]),t("div",U,[s[6]||(s[6]=t("span",{class:"story-view__meta-label"},"Word Count:",-1)),t("span",j,e(o.value.metadata.wordCount)+" words",1)]),t("div",G,[s[7]||(s[7]=t("span",{class:"story-view__meta-label"},"Created:",-1)),t("span",H,e(k(o.value.createdAt)),1)])]),t("div",J,[s[8]||(s[8]=t("h2",null,"Summary",-1)),t("p",null,e(o.value.outline.summary),1)]),t("div",P,[s[9]||(s[9]=t("h2",null,"Characters",-1)),t("div",W,[(n(!0),a(m,null,w(o.value.outline.characters,i=>(n(),a("div",{key:i.id,class:"story-view__character"},[t("h3",null,e(i.name),1),t("p",q,e(f(i.role)),1),t("p",K,e(i.description),1)]))),128))])]),t("div",O,[s[10]||(s[10]=t("h2",null,"Settings",-1)),t("div",Q,[(n(!0),a(m,null,w(o.value.outline.settings,i=>(n(),a("div",{key:i.id,class:"story-view__setting"},[t("h3",null,e(i.name),1),t("p",X,e(S(i.type))+" • "+e(c(i.genre)),1),t("p",Y,e(i.description),1)]))),128))])]),t("div",Z,[s[11]||(s[11]=t("h2",null,"Story Content",-1)),t("div",{class:"story-view__story-content",innerHTML:g.value},null,8,tt)])])])):(n(),a("div",st,[s[13]||(s[13]=t("h1",null,"Story Not Found",-1)),s[14]||(s[14]=t("p",null,"The story you're looking for doesn't exist or has been deleted.",-1)),l(y,{to:"/library",class:"story-view__back-link"},{default:_(()=>s[12]||(s[12]=[d(" Return to Library ")])),_:1,__:[12]})]))])}}}),at=T(et,[["__scopeId","data-v-6034e2ae"]]);export{at as default};
