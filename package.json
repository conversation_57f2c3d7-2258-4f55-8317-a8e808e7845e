{"name": "ai-story-builder", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@vueuse/core": "^13.5.0", "gsap": "^3.13.0", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-vue": "^10.3.0", "npm-run-all2": "^8.0.4", "prettier": "^3.6.2", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}