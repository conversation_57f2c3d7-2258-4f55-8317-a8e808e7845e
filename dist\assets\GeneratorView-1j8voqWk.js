import{d as W,c as o,a as e,b as $,n as ve,t as u,o as a,_ as Y,F as P,r as B,e as F,f as G,g as h,h as ge,w as K,i as D,P as he,j as te,k as le,l as m,m as C,p as R,v as Z,q as x,u as J,s as ie,x as ce,y as ne,z as re,G as se,S as ae,A as de,B as ue,T as oe,C as _e,D as pe,E as ye,H as fe,I as be,J as $e}from"./index-Ck4PQcL2.js";import{B as T}from"./BaseButton-CZ0pK2wn.js";const ke={class:"progress-bar"},Ce={class:"progress-bar__track"},Se={key:0,class:"progress-bar__percentage"},Te=W({__name:"ProgressBar",props:{progress:{},showPercentage:{type:Boolean,default:!0}},setup(j){return(d,y)=>(a(),o("div",ke,[e("div",Ce,[e("div",{class:"progress-bar__fill",style:ve({width:`${d.progress}%`})},null,4)]),d.showPercentage?(a(),o("div",Se,u(Math.round(d.progress))+"% ",1)):$("",!0)]))}}),we=Y(Te,[["__scopeId","data-v-1636d37a"]]),Ae={class:"step-indicator"},Ie=["onClick"],Ve={class:"step-indicator__circle"},Ue={key:0,class:"step-indicator__check"},Pe={key:1,class:"step-indicator__number"},Be={class:"step-indicator__label"},De={key:0,class:"step-indicator__connector"},Le=W({__name:"StepIndicator",props:{steps:{},currentStepIndex:{}},emits:["step-click"],setup(j){return(d,y)=>(a(),o("div",Ae,[(a(!0),o(P,null,B(d.steps,(n,_)=>(a(),o("div",{key:n.key,class:F(["step-indicator__step",{"step-indicator__step--active":_===d.currentStepIndex,"step-indicator__step--completed":_<d.currentStepIndex,"step-indicator__step--disabled":_>d.currentStepIndex}]),onClick:c=>d.$emit("step-click",n.key,_)},[e("div",Ve,[_<d.currentStepIndex?(a(),o("span",Ue,"✓")):(a(),o("span",Pe,u(_+1),1))]),e("div",Be,u(n.label),1),_<d.steps.length-1?(a(),o("div",De)):$("",!0)],10,Ie))),128))]))}}),Oe=Y(Le,[["__scopeId","data-v-c411deaf"]]),Me={class:"base-input"},Ge=["for"],Fe={key:0,class:"base-input__required"},Ne={class:"base-input__wrapper"},Ee=["id","type","value","placeholder","disabled","required"],Re={key:0,class:"base-input__suffix"},je={key:1,class:"base-input__message"},ze={key:0,class:"base-input__error"},qe={key:1,class:"base-input__hint"},Ke=W({__name:"BaseInput",props:{modelValue:{},label:{},type:{default:"text"},placeholder:{},disabled:{type:Boolean,default:!1},required:{type:Boolean,default:!1},error:{},hint:{}},emits:["update:modelValue","blur","focus"],setup(j,{emit:d}){const y=G(()=>`input-${Math.random().toString(36).substr(2,9)}`);return(n,_)=>(a(),o("div",Me,[n.label?(a(),o("label",{key:0,for:y.value,class:"base-input__label"},[h(u(n.label)+" ",1),n.required?(a(),o("span",Fe,"*")):$("",!0)],8,Ge)):$("",!0),e("div",Ne,[e("input",{id:y.value,type:n.type,value:n.modelValue,placeholder:n.placeholder,disabled:n.disabled,required:n.required,class:F(["base-input__field",{"base-input__field--error":n.error,"base-input__field--disabled":n.disabled}]),onInput:_[0]||(_[0]=c=>n.$emit("update:modelValue",c.target.value)),onBlur:_[1]||(_[1]=c=>n.$emit("blur",c)),onFocus:_[2]||(_[2]=c=>n.$emit("focus",c))},null,42,Ee),n.$slots.suffix?(a(),o("div",Re,[ge(n.$slots,"suffix",{},void 0)])):$("",!0)]),n.error||n.hint?(a(),o("div",je,[n.error?(a(),o("span",ze,u(n.error),1)):n.hint?(a(),o("span",qe,u(n.hint),1)):$("",!0)])):$("",!0)]))}}),Q=Y(Ke,[["__scopeId","data-v-8dec60e3"]]),We={class:"character-card__header"},Ye={class:"character-card__info"},He={class:"character-card__name"},Qe={class:"character-card__meta"},Je={key:0,class:"character-card__age"},Xe={class:"character-card__actions"},Ze={key:0,class:"character-card__action",title:"Template"},xe={class:"character-card__description"},et={key:0,class:"character-card__traits"},tt={key:0,class:"character-card__trait character-card__trait--more"},st={key:1,class:"character-card__goals"},at={class:"character-card__list"},ot={key:0,class:"character-card__list-item character-card__list-item--more"},lt={key:2,class:"character-card__flaws"},it={class:"character-card__list"},nt={key:0,class:"character-card__list-item character-card__list-item--more"},rt={key:3,class:"character-card__selected-indicator"},dt=W({__name:"CharacterCard",props:{character:{},isSelected:{type:Boolean}},emits:["select","edit","duplicate","delete"],setup(j){function d(n){return n.charAt(0).toUpperCase()+n.slice(1)}function y(n){return n.split("-").map(_=>_.charAt(0).toUpperCase()+_.slice(1)).join(" ")}return(n,_)=>(a(),o("div",{class:F(["character-card",{"character-card--selected":n.isSelected,"character-card--template":n.character.isTemplate}]),onClick:_[3]||(_[3]=c=>n.$emit("select",n.character))},[e("div",We,[e("div",Ye,[e("h3",He,u(n.character.name),1),e("div",Qe,[e("span",{class:F(["character-card__role",`character-card__role--${n.character.role}`])},u(d(n.character.role)),3),n.character.age?(a(),o("span",Je," Age "+u(n.character.age),1)):$("",!0)])]),e("div",Xe,[n.character.isTemplate?(a(),o("button",Ze," 📋 ")):$("",!0),e("button",{class:"character-card__action",title:"Edit",onClick:_[0]||(_[0]=K(c=>n.$emit("edit",n.character),["stop"]))}," ✏️ "),e("button",{class:"character-card__action",title:"Duplicate",onClick:_[1]||(_[1]=K(c=>n.$emit("duplicate",n.character.id),["stop"]))}," 📄 "),e("button",{class:"character-card__action character-card__action--danger",title:"Delete",onClick:_[2]||(_[2]=K(c=>n.$emit("delete",n.character.id),["stop"]))}," 🗑️ ")])]),e("p",xe,u(n.character.description),1),n.character.personalityTraits.length?(a(),o("div",et,[(a(!0),o(P,null,B(n.character.personalityTraits.slice(0,3),c=>(a(),o("span",{key:c,class:"character-card__trait"},u(y(c)),1))),128)),n.character.personalityTraits.length>3?(a(),o("span",tt," +"+u(n.character.personalityTraits.length-3)+" more ",1)):$("",!0)])):$("",!0),n.character.goals.length?(a(),o("div",st,[_[4]||(_[4]=e("h4",{class:"character-card__section-title"},"Goals:",-1)),e("ul",at,[(a(!0),o(P,null,B(n.character.goals.slice(0,2),c=>(a(),o("li",{key:c,class:"character-card__list-item"},u(c),1))),128)),n.character.goals.length>2?(a(),o("li",ot," +"+u(n.character.goals.length-2)+" more goals ",1)):$("",!0)])])):$("",!0),n.character.flaws.length?(a(),o("div",lt,[_[5]||(_[5]=e("h4",{class:"character-card__section-title"},"Flaws:",-1)),e("ul",it,[(a(!0),o(P,null,B(n.character.flaws.slice(0,2),c=>(a(),o("li",{key:c,class:"character-card__list-item"},u(c),1))),128)),n.character.flaws.length>2?(a(),o("li",nt," +"+u(n.character.flaws.length-2)+" more flaws ",1)):$("",!0)])])):$("",!0),n.isSelected?(a(),o("div",rt," ✓ Selected ")):$("",!0)],2))}}),ct=Y(dt,[["__scopeId","data-v-14b101ac"]]),ut={class:"character-builder"},_t={class:"character-builder__header"},pt={class:"character-builder__title"},mt={class:"character-builder__section"},vt={class:"character-builder__row"},gt={class:"base-input"},ht={class:"base-input"},yt={class:"character-builder__section"},ft={class:"character-builder__traits"},bt=["value","checked","onChange"],$t={class:"character-builder__section"},kt={class:"character-builder__list"},Ct={class:"character-builder__section"},St={class:"character-builder__list"},Tt={class:"character-builder__section"},wt={class:"base-input"},At={class:"base-input"},It={class:"character-builder__checkbox"},Vt={class:"character-builder__actions"},Ut=W({__name:"CharacterBuilder",props:{editingCharacter:{}},emits:["close","submit"],setup(j,{emit:d}){const y=j,n=d,_=D(!1),c=Object.values(he),t=te({name:"",age:"",description:"",personalityTraits:[],goals:[""],flaws:[""],backstory:"",appearance:"",role:"supporting",isTemplate:!1}),s=te({name:"",age:""});le(()=>y.editingCharacter,i=>{i?(t.name=i.name,t.age=i.age?.toString()||"",t.description=i.description,t.personalityTraits=[...i.personalityTraits],t.goals=i.goals.length?[...i.goals]:[""],t.flaws=i.flaws.length?[...i.flaws]:[""],t.backstory=i.backstory||"",t.appearance=i.appearance||"",t.role=i.role,t.isTemplate=i.isTemplate):I()},{immediate:!0});function I(){t.name="",t.age="",t.description="",t.personalityTraits=[],t.goals=[""],t.flaws=[""],t.backstory="",t.appearance="",t.role="supporting",t.isTemplate=!1,s.name="",s.age=""}function N(i){const r=t.personalityTraits.indexOf(i);r>-1?t.personalityTraits.splice(r,1):t.personalityTraits.push(i)}function L(i){return i.split("-").map(r=>r.charAt(0).toUpperCase()+r.slice(1)).join(" ")}function E(){t.goals.push("")}function O(i,r){t.goals[i]=r}function z(i){t.goals.length>1&&t.goals.splice(i,1)}function k(){t.flaws.push("")}function p(i,r){t.flaws[i]=r}function w(i){t.flaws.length>1&&t.flaws.splice(i,1)}function v(){return s.name="",s.age="",t.name.trim()?t.age&&(isNaN(Number(t.age))||Number(t.age)<0)?(s.age="Age must be a valid number",!1):!0:(s.name="Character name is required",!1)}async function l(){if(v()){_.value=!0;try{const i={name:t.name.trim(),age:t.age?Number(t.age):void 0,description:t.description.trim(),personalityTraits:t.personalityTraits,goals:t.goals.filter(r=>r.trim()),flaws:t.flaws.filter(r=>r.trim()),backstory:t.backstory.trim()||void 0,appearance:t.appearance.trim()||void 0,role:t.role,isTemplate:t.isTemplate};n("submit",i)}finally{_.value=!1}}}return(i,r)=>(a(),o("div",ut,[e("div",_t,[e("h2",pt,u(i.editingCharacter?"Edit Character":"Create New Character"),1),m(T,{variant:"ghost",size:"small",onClick:r[0]||(r[0]=b=>i.$emit("close"))},{default:C(()=>r[9]||(r[9]=[h(" ✕ ")])),_:1,__:[9]})]),e("form",{onSubmit:K(l,["prevent"]),class:"character-builder__form"},[e("div",mt,[r[13]||(r[13]=e("h3",{class:"character-builder__section-title"},"Basic Information",-1)),m(Q,{modelValue:t.name,"onUpdate:modelValue":r[1]||(r[1]=b=>t.name=b),label:"Character Name",placeholder:"Enter character name",required:"",error:s.name},null,8,["modelValue","error"]),e("div",vt,[m(Q,{modelValue:t.age,"onUpdate:modelValue":r[2]||(r[2]=b=>t.age=b),label:"Age",type:"number",placeholder:"25",error:s.age},null,8,["modelValue","error"]),e("div",gt,[r[11]||(r[11]=e("label",{class:"base-input__label"},"Role",-1)),R(e("select",{"onUpdate:modelValue":r[3]||(r[3]=b=>t.role=b),class:"character-builder__select"},r[10]||(r[10]=[e("option",{value:"protagonist"},"Protagonist",-1),e("option",{value:"antagonist"},"Antagonist",-1),e("option",{value:"supporting"},"Supporting",-1),e("option",{value:"minor"},"Minor",-1)]),512),[[Z,t.role]])])]),e("div",ht,[r[12]||(r[12]=e("label",{class:"base-input__label"},"Description",-1)),R(e("textarea",{"onUpdate:modelValue":r[4]||(r[4]=b=>t.description=b),placeholder:"Describe your character...",class:"character-builder__textarea",rows:"3"},null,512),[[x,t.description]])])]),e("div",yt,[r[14]||(r[14]=e("h3",{class:"character-builder__section-title"},"Personality Traits",-1)),e("div",ft,[(a(!0),o(P,null,B(J(c),b=>(a(),o("label",{key:b,class:"character-builder__trait"},[e("input",{type:"checkbox",value:b,checked:t.personalityTraits.includes(b),onChange:M=>N(b)},null,40,bt),e("span",null,u(L(b)),1)]))),128))])]),e("div",$t,[r[17]||(r[17]=e("h3",{class:"character-builder__section-title"},"Goals",-1)),e("div",kt,[(a(!0),o(P,null,B(t.goals,(b,M)=>(a(),o("div",{key:M,class:"character-builder__list-item"},[m(Q,{"model-value":b,placeholder:"Enter a goal...","onUpdate:modelValue":H=>O(M,H)},null,8,["model-value","onUpdate:modelValue"]),m(T,{variant:"ghost",size:"small",onClick:H=>z(M)},{default:C(()=>r[15]||(r[15]=[h(" ✕ ")])),_:2,__:[15]},1032,["onClick"])]))),128)),m(T,{variant:"ghost",size:"small",onClick:E},{default:C(()=>r[16]||(r[16]=[h(" + Add Goal ")])),_:1,__:[16]})])]),e("div",Ct,[r[20]||(r[20]=e("h3",{class:"character-builder__section-title"},"Flaws",-1)),e("div",St,[(a(!0),o(P,null,B(t.flaws,(b,M)=>(a(),o("div",{key:M,class:"character-builder__list-item"},[m(Q,{"model-value":b,placeholder:"Enter a flaw...","onUpdate:modelValue":H=>p(M,H)},null,8,["model-value","onUpdate:modelValue"]),m(T,{variant:"ghost",size:"small",onClick:H=>w(M)},{default:C(()=>r[18]||(r[18]=[h(" ✕ ")])),_:2,__:[18]},1032,["onClick"])]))),128)),m(T,{variant:"ghost",size:"small",onClick:k},{default:C(()=>r[19]||(r[19]=[h(" + Add Flaw ")])),_:1,__:[19]})])]),e("div",Tt,[r[24]||(r[24]=e("h3",{class:"character-builder__section-title"},"Optional Details",-1)),e("div",wt,[r[21]||(r[21]=e("label",{class:"base-input__label"},"Backstory",-1)),R(e("textarea",{"onUpdate:modelValue":r[5]||(r[5]=b=>t.backstory=b),placeholder:"Character's background and history...",class:"character-builder__textarea",rows:"3"},null,512),[[x,t.backstory]])]),e("div",At,[r[22]||(r[22]=e("label",{class:"base-input__label"},"Appearance",-1)),R(e("textarea",{"onUpdate:modelValue":r[6]||(r[6]=b=>t.appearance=b),placeholder:"Physical description...",class:"character-builder__textarea",rows:"2"},null,512),[[x,t.appearance]])]),e("div",It,[e("label",null,[R(e("input",{type:"checkbox","onUpdate:modelValue":r[7]||(r[7]=b=>t.isTemplate=b)},null,512),[[ie,t.isTemplate]]),r[23]||(r[23]=e("span",null,"Save as template for future use",-1))])])]),e("div",Vt,[m(T,{variant:"secondary",onClick:r[8]||(r[8]=b=>i.$emit("close"))},{default:C(()=>r[25]||(r[25]=[h(" Cancel ")])),_:1,__:[25]}),m(T,{type:"submit",loading:_.value},{default:C(()=>[h(u(i.editingCharacter?"Update Character":"Create Character"),1)]),_:1},8,["loading"])])],32)]))}}),Pt=Y(Ut,[["__scopeId","data-v-3b9b7624"]]),Bt={class:"character-selection"},Dt={class:"character-selection__header"},Lt={class:"character-selection__actions"},Ot={class:"character-selection__filters"},Mt={class:"character-selection__filter-tabs"},Gt=["onClick"],Ft={class:"character-selection__filter-count"},Nt={key:0,class:"character-selection__selected"},Et={class:"character-selection__selected-title"},Rt={class:"character-selection__selected-list"},jt={class:"character-selection__selected-name"},zt={class:"character-selection__selected-role"},qt=["onClick"],Kt={class:"character-selection__grid"},Wt={key:0,class:"character-selection__empty"},Yt={class:"character-selection__empty-text"},Ht=W({__name:"CharacterSelection",setup(j){const d=ce(),y=D(""),n=D("all"),_=D(!1),c=D(null),t=G(()=>[{key:"all",label:"All",count:d.characters.length},{key:"protagonists",label:"Protagonists",count:d.protagonists.length},{key:"antagonists",label:"Antagonists",count:d.antagonists.length},{key:"supporting",label:"Supporting",count:d.supportingCharacters.length},{key:"templates",label:"Templates",count:d.templates.length}]),s=G(()=>{let i=d.characters;return n.value==="protagonists"?i=d.protagonists:n.value==="antagonists"?i=d.antagonists:n.value==="supporting"?i=d.supportingCharacters:n.value==="templates"&&(i=d.templates),y.value&&(i=d.searchCharacters(y.value)),i}),I=G(()=>d.selectedCharacters);function N(i){return I.value.some(r=>r.id===i)}function L(i){N(i.id)?d.deselectCharacter(i.id):d.selectCharacter(i)}function E(i){d.deselectCharacter(i)}function O(){d.clearSelectedCharacters()}function z(i){c.value=i,_.value=!0}function k(i){d.duplicateCharacter(i)}function p(i){confirm("Are you sure you want to delete this character?")&&d.deleteCharacter(i)}function w(){d.generateRandomCharacter()}function v(){_.value=!1,c.value=null}function l(i){c.value?d.updateCharacter(c.value.id,i):d.addCharacter(i),v()}return ne(()=>{d.loadFromStorage()}),(i,r)=>(a(),o("div",Bt,[e("div",Dt,[r[5]||(r[5]=e("div",{class:"character-selection__title-section"},[e("h2",{class:"character-selection__title"},"Choose Your Characters"),e("p",{class:"character-selection__subtitle"}," Select characters for your story or create new ones ")],-1)),e("div",Lt,[m(T,{variant:"ghost",size:"small",onClick:w},{default:C(()=>r[3]||(r[3]=[h(" 🎲 Random Character ")])),_:1,__:[3]}),m(T,{variant:"primary",onClick:r[0]||(r[0]=b=>_.value=!0)},{default:C(()=>r[4]||(r[4]=[h(" + Create Character ")])),_:1,__:[4]})])]),e("div",Ot,[m(Q,{modelValue:y.value,"onUpdate:modelValue":r[1]||(r[1]=b=>y.value=b),placeholder:"Search characters...",class:"character-selection__search"},{suffix:C(()=>r[6]||(r[6]=[h(" 🔍 ")])),_:1},8,["modelValue"]),e("div",Mt,[(a(!0),o(P,null,B(t.value,b=>(a(),o("button",{key:b.key,class:F(["character-selection__filter-tab",{"character-selection__filter-tab--active":n.value===b.key}]),onClick:M=>n.value=b.key},[h(u(b.label)+" ",1),e("span",Ft,u(b.count),1)],10,Gt))),128))])]),I.value.length?(a(),o("div",Nt,[e("h3",Et," Selected Characters ("+u(I.value.length)+") ",1),e("div",Rt,[(a(!0),o(P,null,B(I.value,b=>(a(),o("div",{key:b.id,class:"character-selection__selected-item"},[e("span",jt,u(b.name),1),e("span",zt,u(b.role),1),e("button",{class:"character-selection__selected-remove",onClick:M=>E(b.id)}," ✕ ",8,qt)]))),128))]),m(T,{variant:"ghost",size:"small",onClick:O},{default:C(()=>r[7]||(r[7]=[h(" Clear All ")])),_:1,__:[7]})])):$("",!0),e("div",Kt,[(a(!0),o(P,null,B(s.value,b=>(a(),re(ct,{key:b.id,character:b,"is-selected":N(b.id),onSelect:L,onEdit:z,onDuplicate:k,onDelete:p},null,8,["character","is-selected"]))),128)),s.value.length===0?(a(),o("div",Wt,[r[9]||(r[9]=e("div",{class:"character-selection__empty-icon"},"👤",-1)),r[10]||(r[10]=e("h3",{class:"character-selection__empty-title"},"No characters found",-1)),e("p",Yt,u(y.value?"Try adjusting your search terms":"Create your first character to get started"),1),m(T,{variant:"primary",onClick:r[2]||(r[2]=b=>_.value=!0)},{default:C(()=>r[8]||(r[8]=[h(" Create Character ")])),_:1,__:[8]})])):$("",!0)]),_.value?(a(),o("div",{key:1,class:"character-selection__modal",onClick:K(v,["self"])},[m(Pt,{"editing-character":c.value,onClose:v,onSubmit:l},null,8,["editing-character"])])):$("",!0)]))}}),Qt=Y(Ht,[["__scopeId","data-v-6fc75cee"]]),Jt={class:"setting-card__image"},Xt={class:"setting-card__genre-badge"},Zt={class:"setting-card__content"},xt={class:"setting-card__header"},es={class:"setting-card__info"},ts={class:"setting-card__name"},ss={class:"setting-card__meta"},as={class:"setting-card__type"},os={key:0,class:"setting-card__time"},ls={class:"setting-card__actions"},is={key:0,class:"setting-card__action",title:"Template"},ns={class:"setting-card__description"},rs={class:"setting-card__atmosphere"},ds={key:0,class:"setting-card__locations"},cs={class:"setting-card__location-tags"},us={key:0,class:"setting-card__location-tag setting-card__location-tag--more"},_s={class:"setting-card__details"},ps={key:0,class:"setting-card__detail"},ms={class:"setting-card__detail-value"},vs={key:1,class:"setting-card__detail"},gs={class:"setting-card__detail-value"},hs={key:0,class:"setting-card__selected-indicator"},ys=W({__name:"SettingCard",props:{setting:{},isSelected:{type:Boolean}},emits:["select","edit","duplicate","delete"],setup(j){function d(t){return{urban:"🏙️",rural:"🌾",wilderness:"🌲",underground:"🕳️",space:"🚀",underwater:"🌊","magical-realm":"✨","dystopian-city":"🏭","small-town":"🏘️",castle:"🏰",spaceship:"🛸",desert:"🏜️",forest:"🌳",mountain:"⛰️",island:"🏝️"}[t]||"🌍"}function y(t){return t.split("-").map(s=>s.charAt(0).toUpperCase()+s.slice(1)).join(" ")}function n(t){return t.split("-").map(s=>s.charAt(0).toUpperCase()+s.slice(1)).join(" ")}function _(t){return t.charAt(0).toUpperCase()+t.slice(1)}function c(t){return t.charAt(0).toUpperCase()+t.slice(1)}return(t,s)=>(a(),o("div",{class:F(["setting-card",{"setting-card--selected":t.isSelected,"setting-card--template":t.setting.isTemplate}]),onClick:s[3]||(s[3]=I=>t.$emit("select",t.setting))},[e("div",Jt,[e("div",{class:F(`setting-card__image-placeholder setting-card__image-placeholder--${t.setting.type}`)},u(d(t.setting.type)),3),e("div",Xt,u(y(t.setting.genre)),1)]),e("div",Zt,[e("div",xt,[e("div",es,[e("h3",ts,u(t.setting.name),1),e("div",ss,[e("span",as,u(n(t.setting.type)),1),t.setting.timeOfDay?(a(),o("span",os,u(_(t.setting.timeOfDay)),1)):$("",!0)])]),e("div",ls,[t.setting.isTemplate?(a(),o("button",is," 📋 ")):$("",!0),e("button",{class:"setting-card__action",title:"Edit",onClick:s[0]||(s[0]=K(I=>t.$emit("edit",t.setting),["stop"]))}," ✏️ "),e("button",{class:"setting-card__action",title:"Duplicate",onClick:s[1]||(s[1]=K(I=>t.$emit("duplicate",t.setting.id),["stop"]))}," 📄 "),e("button",{class:"setting-card__action setting-card__action--danger",title:"Delete",onClick:s[2]||(s[2]=K(I=>t.$emit("delete",t.setting.id),["stop"]))}," 🗑️ ")])]),e("p",ns,u(t.setting.description),1),e("div",rs,[s[4]||(s[4]=e("strong",null,"Atmosphere:",-1)),h(" "+u(t.setting.atmosphere),1)]),t.setting.keyLocations.length?(a(),o("div",ds,[s[5]||(s[5]=e("h4",{class:"setting-card__section-title"},"Key Locations:",-1)),e("div",cs,[(a(!0),o(P,null,B(t.setting.keyLocations.slice(0,3),I=>(a(),o("span",{key:I,class:"setting-card__location-tag"},u(I),1))),128)),t.setting.keyLocations.length>3?(a(),o("span",us," +"+u(t.setting.keyLocations.length-3)+" more ",1)):$("",!0)])])):$("",!0),e("div",_s,[t.setting.weather?(a(),o("div",ps,[s[6]||(s[6]=e("span",{class:"setting-card__detail-label"},"Weather:",-1)),e("span",ms,u(t.setting.weather),1)])):$("",!0),t.setting.season?(a(),o("div",vs,[s[7]||(s[7]=e("span",{class:"setting-card__detail-label"},"Season:",-1)),e("span",gs,u(c(t.setting.season)),1)])):$("",!0)])]),t.isSelected?(a(),o("div",hs," ✓ Selected ")):$("",!0)],2))}}),fs=Y(ys,[["__scopeId","data-v-7846e921"]]),bs={class:"setting-builder"},$s={class:"setting-builder__header"},ks={class:"setting-builder__title"},Cs={class:"setting-builder__section"},Ss={class:"setting-builder__row"},Ts={class:"base-input"},ws=["value"],As={class:"base-input"},Is=["value"],Vs={class:"base-input"},Us={class:"base-input"},Ps={class:"setting-builder__section"},Bs={class:"setting-builder__list"},Ds={class:"setting-builder__section"},Ls={class:"setting-builder__row"},Os={class:"base-input"},Ms={class:"base-input"},Gs={class:"setting-builder__checkbox"},Fs={class:"setting-builder__actions"},Ns=W({__name:"SettingBuilder",props:{editingSetting:{}},emits:["close","submit"],setup(j,{emit:d}){const y=j,n=d,_=D(!1),c=Object.values(se),t=Object.values(ae),s=te({name:"",genre:se.FANTASY,type:ae.FOREST,description:"",atmosphere:"",keyLocations:[""],timeOfDay:"",weather:"",season:"",isTemplate:!1}),I=te({name:""});le(()=>y.editingSetting,v=>{v?(s.name=v.name,s.genre=v.genre,s.type=v.type,s.description=v.description,s.atmosphere=v.atmosphere,s.keyLocations=v.keyLocations.length?[...v.keyLocations]:[""],s.timeOfDay=v.timeOfDay||"",s.weather=v.weather||"",s.season=v.season||"",s.isTemplate=v.isTemplate):N()},{immediate:!0});function N(){s.name="",s.genre=se.FANTASY,s.type=ae.FOREST,s.description="",s.atmosphere="",s.keyLocations=[""],s.timeOfDay="",s.weather="",s.season="",s.isTemplate=!1,I.name=""}function L(v){return v.split("-").map(l=>l.charAt(0).toUpperCase()+l.slice(1)).join(" ")}function E(v){return v.split("-").map(l=>l.charAt(0).toUpperCase()+l.slice(1)).join(" ")}function O(){s.keyLocations.push("")}function z(v,l){s.keyLocations[v]=l}function k(v){s.keyLocations.length>1&&s.keyLocations.splice(v,1)}function p(){return I.name="",s.name.trim()?!0:(I.name="Setting name is required",!1)}async function w(){if(p()){_.value=!0;try{const v={name:s.name.trim(),genre:s.genre,type:s.type,description:s.description.trim(),atmosphere:s.atmosphere.trim(),keyLocations:s.keyLocations.filter(l=>l.trim()),timeOfDay:s.timeOfDay||void 0,weather:s.weather.trim()||void 0,season:s.season||void 0,isTemplate:s.isTemplate};n("submit",v)}finally{_.value=!1}}}return(v,l)=>(a(),o("div",bs,[e("div",$s,[e("h2",ks,u(v.editingSetting?"Edit Setting":"Create New Setting"),1),m(T,{variant:"ghost",size:"small",onClick:l[0]||(l[0]=i=>v.$emit("close"))},{default:C(()=>l[11]||(l[11]=[h(" ✕ ")])),_:1,__:[11]})]),e("form",{onSubmit:K(w,["prevent"]),class:"setting-builder__form"},[e("div",Cs,[l[16]||(l[16]=e("h3",{class:"setting-builder__section-title"},"Basic Information",-1)),m(Q,{modelValue:s.name,"onUpdate:modelValue":l[1]||(l[1]=i=>s.name=i),label:"Setting Name",placeholder:"Enter setting name",required:"",error:I.name},null,8,["modelValue","error"]),e("div",Ss,[e("div",Ts,[l[12]||(l[12]=e("label",{class:"base-input__label"},"Genre",-1)),R(e("select",{"onUpdate:modelValue":l[2]||(l[2]=i=>s.genre=i),class:"setting-builder__select"},[(a(!0),o(P,null,B(J(c),i=>(a(),o("option",{key:i,value:i},u(L(i)),9,ws))),128))],512),[[Z,s.genre]])]),e("div",As,[l[13]||(l[13]=e("label",{class:"base-input__label"},"Type",-1)),R(e("select",{"onUpdate:modelValue":l[3]||(l[3]=i=>s.type=i),class:"setting-builder__select"},[(a(!0),o(P,null,B(J(t),i=>(a(),o("option",{key:i,value:i},u(E(i)),9,Is))),128))],512),[[Z,s.type]])])]),e("div",Vs,[l[14]||(l[14]=e("label",{class:"base-input__label"},"Description",-1)),R(e("textarea",{"onUpdate:modelValue":l[4]||(l[4]=i=>s.description=i),placeholder:"Describe your setting...",class:"setting-builder__textarea",rows:"3"},null,512),[[x,s.description]])]),e("div",Us,[l[15]||(l[15]=e("label",{class:"base-input__label"},"Atmosphere",-1)),R(e("textarea",{"onUpdate:modelValue":l[5]||(l[5]=i=>s.atmosphere=i),placeholder:"Describe the mood and feeling of this place...",class:"setting-builder__textarea",rows:"2"},null,512),[[x,s.atmosphere]])])]),e("div",Ps,[l[19]||(l[19]=e("h3",{class:"setting-builder__section-title"},"Key Locations",-1)),e("div",Bs,[(a(!0),o(P,null,B(s.keyLocations,(i,r)=>(a(),o("div",{key:r,class:"setting-builder__list-item"},[m(Q,{"model-value":i,placeholder:"Enter a location...","onUpdate:modelValue":b=>z(r,b)},null,8,["model-value","onUpdate:modelValue"]),m(T,{variant:"ghost",size:"small",onClick:b=>k(r)},{default:C(()=>l[17]||(l[17]=[h(" ✕ ")])),_:2,__:[17]},1032,["onClick"])]))),128)),m(T,{variant:"ghost",size:"small",onClick:O},{default:C(()=>l[18]||(l[18]=[h(" + Add Location ")])),_:1,__:[18]})])]),e("div",Ds,[l[25]||(l[25]=e("h3",{class:"setting-builder__section-title"},"Environmental Details",-1)),e("div",Ls,[e("div",Os,[l[21]||(l[21]=e("label",{class:"base-input__label"},"Time of Day",-1)),R(e("select",{"onUpdate:modelValue":l[6]||(l[6]=i=>s.timeOfDay=i),class:"setting-builder__select"},l[20]||(l[20]=[de('<option value="" data-v-c57cbcd3>Not specified</option><option value="dawn" data-v-c57cbcd3>Dawn</option><option value="morning" data-v-c57cbcd3>Morning</option><option value="noon" data-v-c57cbcd3>Noon</option><option value="afternoon" data-v-c57cbcd3>Afternoon</option><option value="evening" data-v-c57cbcd3>Evening</option><option value="night" data-v-c57cbcd3>Night</option><option value="midnight" data-v-c57cbcd3>Midnight</option>',8)]),512),[[Z,s.timeOfDay]])]),e("div",Ms,[l[23]||(l[23]=e("label",{class:"base-input__label"},"Season",-1)),R(e("select",{"onUpdate:modelValue":l[7]||(l[7]=i=>s.season=i),class:"setting-builder__select"},l[22]||(l[22]=[de('<option value="" data-v-c57cbcd3>Not specified</option><option value="spring" data-v-c57cbcd3>Spring</option><option value="summer" data-v-c57cbcd3>Summer</option><option value="autumn" data-v-c57cbcd3>Autumn</option><option value="winter" data-v-c57cbcd3>Winter</option>',5)]),512),[[Z,s.season]])])]),m(Q,{modelValue:s.weather,"onUpdate:modelValue":l[8]||(l[8]=i=>s.weather=i),label:"Weather",placeholder:"e.g., Sunny, Rainy, Stormy, Misty"},null,8,["modelValue"]),e("div",Gs,[e("label",null,[R(e("input",{type:"checkbox","onUpdate:modelValue":l[9]||(l[9]=i=>s.isTemplate=i)},null,512),[[ie,s.isTemplate]]),l[24]||(l[24]=e("span",null,"Save as template for future use",-1))])])]),e("div",Fs,[m(T,{variant:"secondary",onClick:l[10]||(l[10]=i=>v.$emit("close"))},{default:C(()=>l[26]||(l[26]=[h(" Cancel ")])),_:1,__:[26]}),m(T,{type:"submit",loading:_.value},{default:C(()=>[h(u(v.editingSetting?"Update Setting":"Create Setting"),1)]),_:1},8,["loading"])])],32)]))}}),Es=Y(Ns,[["__scopeId","data-v-c57cbcd3"]]),Rs={class:"setting-selection"},js={class:"setting-selection__header"},zs={class:"setting-selection__actions"},qs={class:"setting-selection__filters"},Ks={class:"setting-selection__filter-tabs"},Ws=["onClick"],Ys={class:"setting-selection__filter-count"},Hs={class:"setting-selection__genre-filters"},Qs=["onClick"],Js={key:0,class:"setting-selection__selected"},Xs={class:"setting-selection__selected-title"},Zs={class:"setting-selection__selected-list"},xs={class:"setting-selection__selected-name"},ea={class:"setting-selection__selected-type"},ta=["onClick"],sa={class:"setting-selection__grid"},aa={key:0,class:"setting-selection__empty"},oa={class:"setting-selection__empty-text"},la=W({__name:"SettingSelection",setup(j){const d=ue(),y=D(""),n=D("all"),_=D(null),c=D(!1),t=D(null),s=Object.values(se),I=G(()=>[{key:"all",label:"All",count:d.settings.length},{key:"templates",label:"Templates",count:d.templates.length}]),N=G(()=>{let A=d.settings;return n.value==="templates"&&(A=d.templates),_.value&&(A=A.filter(V=>V.genre===_.value)),y.value&&(A=d.searchSettings(y.value)),A}),L=G(()=>d.selectedSettings);function E(A){return L.value.some(V=>V.id===A)}function O(A){E(A.id)?d.deselectSetting(A.id):d.selectSetting(A)}function z(A){d.deselectSetting(A)}function k(){d.clearSelectedSettings()}function p(A){t.value=A,c.value=!0}function w(A){d.duplicateSetting(A)}function v(A){confirm("Are you sure you want to delete this setting?")&&d.deleteSetting(A)}function l(){d.generateRandomSetting()}function i(A){_.value=_.value===A?null:A}function r(A){return A.split("-").map(V=>V.charAt(0).toUpperCase()+V.slice(1)).join(" ")}function b(A){return A.split("-").map(V=>V.charAt(0).toUpperCase()+V.slice(1)).join(" ")}function M(){c.value=!1,t.value=null}function H(A){t.value?d.updateSetting(t.value.id,A):d.addSetting(A),M()}return ne(()=>{d.loadFromStorage()}),(A,V)=>(a(),o("div",Rs,[e("div",js,[V[5]||(V[5]=e("div",{class:"setting-selection__title-section"},[e("h2",{class:"setting-selection__title"},"Choose Your Settings"),e("p",{class:"setting-selection__subtitle"}," Select the worlds and locations where your story takes place ")],-1)),e("div",zs,[m(T,{variant:"ghost",size:"small",onClick:l},{default:C(()=>V[3]||(V[3]=[h(" 🎲 Random Setting ")])),_:1,__:[3]}),m(T,{variant:"primary",onClick:V[0]||(V[0]=g=>c.value=!0)},{default:C(()=>V[4]||(V[4]=[h(" + Create Setting ")])),_:1,__:[4]})])]),e("div",qs,[m(Q,{modelValue:y.value,"onUpdate:modelValue":V[1]||(V[1]=g=>y.value=g),placeholder:"Search settings...",class:"setting-selection__search"},{suffix:C(()=>V[6]||(V[6]=[h(" 🔍 ")])),_:1},8,["modelValue"]),e("div",Ks,[(a(!0),o(P,null,B(I.value,g=>(a(),o("button",{key:g.key,class:F(["setting-selection__filter-tab",{"setting-selection__filter-tab--active":n.value===g.key}]),onClick:S=>n.value=g.key},[h(u(g.label)+" ",1),e("span",Ys,u(g.count),1)],10,Ws))),128))]),e("div",Hs,[(a(!0),o(P,null,B(J(s),g=>(a(),o("button",{key:g,class:F(["setting-selection__genre-filter",{"setting-selection__genre-filter--active":_.value===g}]),onClick:S=>i(g)},u(r(g)),11,Qs))),128))])]),L.value.length?(a(),o("div",Js,[e("h3",Xs," Selected Settings ("+u(L.value.length)+") ",1),e("div",Zs,[(a(!0),o(P,null,B(L.value,g=>(a(),o("div",{key:g.id,class:"setting-selection__selected-item"},[e("span",xs,u(g.name),1),e("span",ea,u(b(g.type)),1),e("button",{class:"setting-selection__selected-remove",onClick:S=>z(g.id)}," ✕ ",8,ta)]))),128))]),m(T,{variant:"ghost",size:"small",onClick:k},{default:C(()=>V[7]||(V[7]=[h(" Clear All ")])),_:1,__:[7]})])):$("",!0),e("div",sa,[(a(!0),o(P,null,B(N.value,g=>(a(),re(fs,{key:g.id,setting:g,"is-selected":E(g.id),onSelect:O,onEdit:p,onDuplicate:w,onDelete:v},null,8,["setting","is-selected"]))),128)),N.value.length===0?(a(),o("div",aa,[V[9]||(V[9]=e("div",{class:"setting-selection__empty-icon"},"🌍",-1)),V[10]||(V[10]=e("h3",{class:"setting-selection__empty-title"},"No settings found",-1)),e("p",oa,u(y.value?"Try adjusting your search terms":"Create your first setting to get started"),1),m(T,{variant:"primary",onClick:V[2]||(V[2]=g=>c.value=!0)},{default:C(()=>V[8]||(V[8]=[h(" Create Setting ")])),_:1,__:[8]})])):$("",!0)]),c.value?(a(),o("div",{key:1,class:"setting-selection__modal",onClick:K(M,["self"])},[m(Es,{"editing-setting":t.value,onClose:M,onSubmit:H},null,8,["editing-setting"])])):$("",!0)]))}}),ia=Y(la,[["__scopeId","data-v-41d09dc7"]]),na={class:"theme-card__header"},ra={class:"theme-card__info"},da={class:"theme-card__primary"},ca={class:"theme-card__meta"},ua={class:"theme-card__actions"},_a={key:0,class:"theme-card__action",title:"Template"},pa={class:"theme-card__description"},ma={key:0,class:"theme-card__secondary"},va={class:"theme-card__secondary-themes"},ga={class:"theme-card__icon"},ha={key:1,class:"theme-card__selected-indicator"},ya=W({__name:"ThemeCard",props:{theme:{},isSelected:{type:Boolean}},emits:["select","edit","duplicate","delete"],setup(j){function d(c){return c.split("-").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" ")}function y(c){return c.charAt(0).toUpperCase()+c.slice(1)}function n(c){return c.split("-").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" ")}function _(c){return{betrayal:"🗡️",friendship:"🤝",love:"❤️",revenge:"⚔️",redemption:"🕊️",sacrifice:"⚖️",power:"👑",freedom:"🦅",justice:"⚖️",survival:"🛡️",discovery:"🔍",transformation:"🦋","time-travel":"⏰",family:"👨‍👩‍👧‍👦",identity:"🪞","coming-of-age":"🌱"}[c]||"✨"}return(c,t)=>(a(),o("div",{class:F(["theme-card",{"theme-card--selected":c.isSelected,"theme-card--template":c.theme.isTemplate,[`theme-card--${c.theme.mood}`]:!0}]),onClick:t[3]||(t[3]=s=>c.$emit("select",c.theme))},[e("div",na,[e("div",ra,[e("h3",da,u(d(c.theme.primary)),1),e("div",ca,[e("span",{class:F(`theme-card__mood theme-card__mood--${c.theme.mood}`)},u(y(c.theme.mood)),3),e("span",{class:F(`theme-card__conflict theme-card__conflict--${c.theme.conflictType}`)},u(n(c.theme.conflictType)),3)])]),e("div",ua,[c.theme.isTemplate?(a(),o("button",_a," 📋 ")):$("",!0),e("button",{class:"theme-card__action",title:"Edit",onClick:t[0]||(t[0]=K(s=>c.$emit("edit",c.theme),["stop"]))}," ✏️ "),e("button",{class:"theme-card__action",title:"Duplicate",onClick:t[1]||(t[1]=K(s=>c.$emit("duplicate",c.theme.id),["stop"]))}," 📄 "),e("button",{class:"theme-card__action theme-card__action--danger",title:"Delete",onClick:t[2]||(t[2]=K(s=>c.$emit("delete",c.theme.id),["stop"]))}," 🗑️ ")])]),e("p",pa,u(c.theme.description),1),c.theme.secondary.length?(a(),o("div",ma,[t[4]||(t[4]=e("h4",{class:"theme-card__section-title"},"Secondary Themes:",-1)),e("div",va,[(a(!0),o(P,null,B(c.theme.secondary,s=>(a(),o("span",{key:s,class:"theme-card__secondary-theme"},u(d(s)),1))),128))])])):$("",!0),e("div",ga,u(_(c.theme.primary)),1),c.isSelected?(a(),o("div",ha," ✓ Selected ")):$("",!0)],2))}}),fa=Y(ya,[["__scopeId","data-v-840e7c35"]]),ba={class:"theme-builder"},$a={class:"theme-builder__header"},ka={class:"theme-builder__title"},Ca={class:"theme-builder__section"},Sa={class:"base-input"},Ta=["value"],wa={class:"base-input"},Aa={class:"theme-builder__section"},Ia={class:"theme-builder__theme-grid"},Va=["value","checked","onChange"],Ua={class:"theme-builder__theme-label"},Pa={class:"theme-builder__section"},Ba={class:"theme-builder__row"},Da={class:"base-input"},La={class:"base-input"},Oa={class:"theme-builder__checkbox"},Ma={key:0,class:"theme-builder__section"},Ga={class:"theme-builder__suggestions"},Fa=["onClick"],Na={class:"theme-builder__suggestion-header"},Ea={class:"theme-builder__suggestion-secondary"},Ra={class:"theme-builder__suggestion-description"},ja={class:"theme-builder__actions"},za=W({__name:"ThemeBuilder",props:{editingTheme:{}},emits:["close","submit"],setup(j,{emit:d}){const y=j,n=d,_=D(!1),c=Object.values(oe),t=_e,s=te({primary:oe.REDEMPTION,secondary:[],description:"",conflictType:"both",mood:"mixed",isTemplate:!1}),I=G(()=>c.filter(k=>k!==s.primary));le(()=>y.editingTheme,k=>{k?(s.primary=k.primary,s.secondary=[...k.secondary],s.description=k.description,s.conflictType=k.conflictType,s.mood=k.mood,s.isTemplate=k.isTemplate):N()},{immediate:!0});function N(){s.primary=oe.REDEMPTION,s.secondary=[],s.description="",s.conflictType="both",s.mood="mixed",s.isTemplate=!1}function L(k){return k.split("-").map(p=>p.charAt(0).toUpperCase()+p.slice(1)).join(" ")}function E(k){const p=s.secondary.indexOf(k);p>-1?s.secondary.splice(p,1):s.secondary.push(k)}function O(k){s.primary=k.primary,s.secondary=[...k.secondary],s.description=k.description}async function z(){_.value=!0;try{const k={primary:s.primary,secondary:[...s.secondary],description:s.description.trim()||`A story exploring ${L(s.primary)}${s.secondary.length?` with elements of ${s.secondary.map(L).join(", ")}`:""}`,conflictType:s.conflictType,mood:s.mood,isTemplate:s.isTemplate};n("submit",k)}finally{_.value=!1}}return(k,p)=>(a(),o("div",ba,[e("div",$a,[e("h2",ka,u(k.editingTheme?"Edit Theme":"Create New Theme"),1),m(T,{variant:"ghost",size:"small",onClick:p[0]||(p[0]=w=>k.$emit("close"))},{default:C(()=>p[7]||(p[7]=[h(" ✕ ")])),_:1,__:[7]})]),e("form",{onSubmit:K(z,["prevent"]),class:"theme-builder__form"},[e("div",Ca,[p[10]||(p[10]=e("h3",{class:"theme-builder__section-title"},"Primary Theme",-1)),e("div",Sa,[p[8]||(p[8]=e("label",{class:"base-input__label"},"Main Theme",-1)),R(e("select",{"onUpdate:modelValue":p[1]||(p[1]=w=>s.primary=w),class:"theme-builder__select"},[(a(!0),o(P,null,B(J(c),w=>(a(),o("option",{key:w,value:w},u(L(w)),9,Ta))),128))],512),[[Z,s.primary]])]),e("div",wa,[p[9]||(p[9]=e("label",{class:"base-input__label"},"Description",-1)),R(e("textarea",{"onUpdate:modelValue":p[2]||(p[2]=w=>s.description=w),placeholder:"Describe how this theme will be explored in your story...",class:"theme-builder__textarea",rows:"3"},null,512),[[x,s.description]])])]),e("div",Aa,[p[11]||(p[11]=e("h3",{class:"theme-builder__section-title"},"Secondary Themes",-1)),p[12]||(p[12]=e("p",{class:"theme-builder__section-description"}," Choose additional themes that complement your primary theme ",-1)),e("div",Ia,[(a(!0),o(P,null,B(I.value,w=>(a(),o("label",{key:w,class:"theme-builder__theme-option"},[e("input",{type:"checkbox",value:w,checked:s.secondary.includes(w),onChange:v=>E(w)},null,40,Va),e("span",Ua,u(L(w)),1)]))),128))])]),e("div",Pa,[p[18]||(p[18]=e("h3",{class:"theme-builder__section-title"},"Theme Properties",-1)),e("div",Ba,[e("div",Da,[p[14]||(p[14]=e("label",{class:"base-input__label"},"Conflict Type",-1)),R(e("select",{"onUpdate:modelValue":p[3]||(p[3]=w=>s.conflictType=w),class:"theme-builder__select"},p[13]||(p[13]=[e("option",{value:"internal"},"Internal - Character vs Self",-1),e("option",{value:"external"},"External - Character vs World",-1),e("option",{value:"both"},"Both - Internal and External",-1)]),512),[[Z,s.conflictType]])]),e("div",La,[p[16]||(p[16]=e("label",{class:"base-input__label"},"Mood",-1)),R(e("select",{"onUpdate:modelValue":p[4]||(p[4]=w=>s.mood=w),class:"theme-builder__select"},p[15]||(p[15]=[e("option",{value:"light"},"Light - Hopeful and uplifting",-1),e("option",{value:"dark"},"Dark - Serious and somber",-1),e("option",{value:"neutral"},"Neutral - Balanced tone",-1),e("option",{value:"mixed"},"Mixed - Varies throughout",-1)]),512),[[Z,s.mood]])])]),e("div",Oa,[e("label",null,[R(e("input",{type:"checkbox","onUpdate:modelValue":p[5]||(p[5]=w=>s.isTemplate=w)},null,512),[[ie,s.isTemplate]]),p[17]||(p[17]=e("span",null,"Save as template for future use",-1))])])]),J(t).length?(a(),o("div",Ma,[p[19]||(p[19]=e("h3",{class:"theme-builder__section-title"},"Suggested Combinations",-1)),p[20]||(p[20]=e("p",{class:"theme-builder__section-description"}," Popular theme combinations that work well together ",-1)),e("div",Ga,[(a(!0),o(P,null,B(J(t),w=>(a(),o("div",{key:w.primary,class:"theme-builder__suggestion",onClick:v=>O(w)},[e("div",Na,[e("strong",null,u(L(w.primary)),1),e("span",Ea," + "+u(w.secondary.map(L).join(", ")),1)]),e("p",Ra,u(w.description),1)],8,Fa))),128))])])):$("",!0),e("div",ja,[m(T,{variant:"secondary",onClick:p[6]||(p[6]=w=>k.$emit("close"))},{default:C(()=>p[21]||(p[21]=[h(" Cancel ")])),_:1,__:[21]}),m(T,{type:"submit",loading:_.value},{default:C(()=>[h(u(k.editingTheme?"Update Theme":"Create Theme"),1)]),_:1},8,["loading"])])],32)]))}}),qa=Y(za,[["__scopeId","data-v-2d71b8f0"]]),Ka={class:"theme-selection"},Wa={class:"theme-selection__header"},Ya={class:"theme-selection__actions"},Ha={class:"theme-selection__filters"},Qa={class:"theme-selection__filter-tabs"},Ja=["onClick"],Xa={class:"theme-selection__filter-count"},Za={class:"theme-selection__mood-filters"},xa=["onClick"],eo={key:0,class:"theme-selection__suggestions"},to={class:"theme-selection__suggestion-cards"},so=["onClick"],ao={class:"theme-selection__suggestion-header"},oo={class:"theme-selection__suggestion-description"},lo={key:1,class:"theme-selection__selected"},io={class:"theme-selection__selected-title"},no={class:"theme-selection__selected-list"},ro={class:"theme-selection__selected-primary"},co={key:0,class:"theme-selection__selected-secondary"},uo=["onClick"],_o={class:"theme-selection__grid"},po={key:0,class:"theme-selection__empty"},mo={class:"theme-selection__empty-text"},vo=W({__name:"ThemeSelection",setup(j){const d=pe(),y=D(""),n=D("all"),_=D(null),c=D(!1),t=D(null),s=["light","dark","neutral","mixed"],I=_e,N=G(()=>[{key:"all",label:"All",count:d.themes.length},{key:"templates",label:"Templates",count:d.templates.length},{key:"internal",label:"Internal Conflict",count:d.themesByConflictType.internal?.length||0},{key:"external",label:"External Conflict",count:d.themesByConflictType.external?.length||0}]),L=G(()=>{let g=d.themes;return n.value==="templates"?g=d.templates:n.value==="internal"?g=d.getThemesByConflictType("internal"):n.value==="external"&&(g=d.getThemesByConflictType("external")),_.value&&(g=g.filter(S=>S.mood===_.value)),y.value&&(g=d.searchThemes(y.value)),g}),E=G(()=>d.selectedThemes);function O(g){return E.value.some(S=>S.id===g)}function z(g){O(g.id)?d.deselectTheme(g.id):d.selectTheme(g)}function k(g){d.deselectTheme(g)}function p(){d.clearSelectedThemes()}function w(g){t.value=g,c.value=!0}function v(g){d.duplicateTheme(g)}function l(g){confirm("Are you sure you want to delete this theme?")&&d.deleteTheme(g)}function i(){d.generateRandomTheme()}function r(g){const S=d.createThemeFromSuggestion(g);S&&d.selectTheme(S)}function b(g){_.value=_.value===g?null:g}function M(g){return g.split("-").map(S=>S.charAt(0).toUpperCase()+S.slice(1)).join(" ")}function H(g){return g.charAt(0).toUpperCase()+g.slice(1)}function A(){c.value=!1,t.value=null}function V(g){t.value?d.updateTheme(t.value.id,g):d.addTheme(g),A()}return ne(()=>{d.loadFromStorage()}),(g,S)=>(a(),o("div",Ka,[e("div",Wa,[S[5]||(S[5]=e("div",{class:"theme-selection__title-section"},[e("h2",{class:"theme-selection__title"},"Choose Your Themes"),e("p",{class:"theme-selection__subtitle"}," Select the central themes and conflicts that will drive your story ")],-1)),e("div",Ya,[m(T,{variant:"ghost",size:"small",onClick:i},{default:C(()=>S[3]||(S[3]=[h(" 🎲 Random Theme ")])),_:1,__:[3]}),m(T,{variant:"primary",onClick:S[0]||(S[0]=U=>c.value=!0)},{default:C(()=>S[4]||(S[4]=[h(" + Create Theme ")])),_:1,__:[4]})])]),e("div",Ha,[m(Q,{modelValue:y.value,"onUpdate:modelValue":S[1]||(S[1]=U=>y.value=U),placeholder:"Search themes...",class:"theme-selection__search"},{suffix:C(()=>S[6]||(S[6]=[h(" 🔍 ")])),_:1},8,["modelValue"]),e("div",Qa,[(a(!0),o(P,null,B(N.value,U=>(a(),o("button",{key:U.key,class:F(["theme-selection__filter-tab",{"theme-selection__filter-tab--active":n.value===U.key}]),onClick:ee=>n.value=U.key},[h(u(U.label)+" ",1),e("span",Xa,u(U.count),1)],10,Ja))),128))]),e("div",Za,[(a(),o(P,null,B(s,U=>e("button",{key:U,class:F(["theme-selection__mood-filter",`theme-selection__mood-filter--${U}`,{"theme-selection__mood-filter--active":_.value===U}]),onClick:ee=>b(U)},u(H(U)),11,xa)),64))])]),J(I).length&&!y.value?(a(),o("div",eo,[S[9]||(S[9]=e("h3",{class:"theme-selection__suggestions-title"},"Popular Combinations",-1)),e("div",to,[(a(!0),o(P,null,B(J(I).slice(0,4),U=>(a(),o("div",{key:U.primary,class:"theme-selection__suggestion-card",onClick:ee=>r(U)},[e("div",ao,[e("strong",null,u(M(U.primary)),1),S[7]||(S[7]=e("span",{class:"theme-selection__suggestion-plus"},"+",-1)),e("span",null,u(U.secondary.map(M).join(", ")),1)]),e("p",oo,u(U.description),1),m(T,{variant:"ghost",size:"small"},{default:C(()=>S[8]||(S[8]=[h(" Use This Combination ")])),_:1,__:[8]})],8,so))),128))])])):$("",!0),E.value.length?(a(),o("div",lo,[e("h3",io," Selected Themes ("+u(E.value.length)+") ",1),e("div",no,[(a(!0),o(P,null,B(E.value,U=>(a(),o("div",{key:U.id,class:"theme-selection__selected-item"},[e("span",ro,u(M(U.primary)),1),U.secondary.length?(a(),o("span",co," + "+u(U.secondary.length)+" more ",1)):$("",!0),e("span",{class:F(`theme-selection__selected-mood theme-selection__selected-mood--${U.mood}`)},u(H(U.mood)),3),e("button",{class:"theme-selection__selected-remove",onClick:ee=>k(U.id)}," ✕ ",8,uo)]))),128))]),m(T,{variant:"ghost",size:"small",onClick:p},{default:C(()=>S[10]||(S[10]=[h(" Clear All ")])),_:1,__:[10]})])):$("",!0),e("div",_o,[(a(!0),o(P,null,B(L.value,U=>(a(),re(fa,{key:U.id,theme:U,"is-selected":O(U.id),onSelect:z,onEdit:w,onDuplicate:v,onDelete:l},null,8,["theme","is-selected"]))),128)),L.value.length===0?(a(),o("div",po,[S[12]||(S[12]=e("div",{class:"theme-selection__empty-icon"},"🎭",-1)),S[13]||(S[13]=e("h3",{class:"theme-selection__empty-title"},"No themes found",-1)),e("p",mo,u(y.value?"Try adjusting your search terms":"Create your first theme to get started"),1),m(T,{variant:"primary",onClick:S[2]||(S[2]=U=>c.value=!0)},{default:C(()=>S[11]||(S[11]=[h(" Create Theme ")])),_:1,__:[11]})])):$("",!0)]),c.value?(a(),o("div",{key:2,class:"theme-selection__modal",onClick:K(A,["self"])},[m(qa,{"editing-theme":t.value,onClose:A,onSubmit:V},null,8,["editing-theme"])])):$("",!0)]))}}),go=Y(vo,[["__scopeId","data-v-d6c306ac"]]),ho={class:"ai-config-modal"},yo={class:"ai-config-modal__header"},fo={class:"ai-config-modal__content"},bo={class:"ai-config-modal__providers"},$o={class:"ai-config-modal__provider-options"},ko=["value"],Co={class:"ai-config-modal__provider-content"},So={class:"ai-config-modal__provider-header"},To={class:"ai-config-modal__provider-name"},wo={class:"ai-config-modal__provider-description"},Ao={class:"ai-config-modal__provider-features"},Io={key:0,class:"ai-config-modal__api-config"},Vo={class:"ai-config-modal__api-help"},Uo={key:0,class:"ai-config-modal__help-content"},Po={key:1,class:"ai-config-modal__help-content"},Bo={key:1,class:"ai-config-modal__local-config"},Do={class:"ai-config-modal__test"},Lo={class:"ai-config-modal__actions"},Oo=W({__name:"AIConfigModal",emits:["close","save"],setup(j,{emit:d}){const y=d,n=D("openai"),_=D(""),c=D(""),t=D("http://localhost:11434"),s=D(!1),I=D(null),N=D({apiKey:""}),L=[{id:"openai",name:"OpenAI",tier:"premium",description:"High-quality story generation with GPT-4. Requires API key and credits.",features:["GPT-4 Turbo","Excellent creativity","Fast generation"]},{id:"anthropic",name:"Anthropic Claude",tier:"premium",description:"Sophisticated storytelling with Claude. Requires API key and credits.",features:["Claude 3 Sonnet","Great dialogue","Nuanced characters"]},{id:"local",name:"Local Model",tier:"free",description:"Use your own local LLM. Free but requires technical setup.",features:["Privacy focused","No API costs","Offline capable"]}],E=G(()=>n.value==="local"?t.value.trim()!==""&&c.value.trim()!=="":_.value.trim()!==""),O=G(()=>n.value==="local"?t.value.trim()!==""&&c.value.trim()!=="":_.value.trim()!==""&&!N.value.apiKey);function z(v){return{openai:"OpenAI",anthropic:"Anthropic",local:"Local Model"}[v]||v}function k(v){return{openai:"gpt-4",anthropic:"claude-3-sonnet-20240229",local:"llama2"}[v]||""}async function p(){s.value=!0,I.value=null,N.value.apiKey="";try{const v={provider:n.value,apiKey:n.value!=="local"?_.value:void 0,model:c.value||k(n.value),baseURL:n.value==="local"?t.value:void 0},l=new ye(v),i="Write a single sentence about a brave knight.";n.value==="openai"?await l.generateWithOpenAI(i):n.value==="anthropic"?await l.generateWithAnthropic(i):await l.generateWithLocalModel(i),I.value={type:"success",message:"✅ Connection successful! AI generation is ready to use."}}catch(v){I.value={type:"error",message:`❌ Connection failed: ${v.message}`},v.message.includes("API key")&&(N.value.apiKey="Invalid API key")}finally{s.value=!1}}function w(){if(!O.value)return;const v={provider:n.value,apiKey:n.value!=="local"?_.value:void 0,model:c.value||k(n.value),baseURL:n.value==="local"?t.value:void 0};localStorage.setItem("aiStoryConfig",JSON.stringify(v)),y("save",v)}return(v,l)=>(a(),o("div",ho,[e("div",yo,[l[8]||(l[8]=e("h2",{class:"ai-config-modal__title"},"AI Story Generation Setup",-1)),m(T,{variant:"ghost",size:"small",onClick:l[0]||(l[0]=i=>v.$emit("close"))},{default:C(()=>l[7]||(l[7]=[h(" ✕ ")])),_:1,__:[7]})]),e("div",fo,[l[16]||(l[16]=e("p",{class:"ai-config-modal__description"}," Enable AI-powered story generation for richer, more detailed narratives. Choose your preferred AI provider and configure your settings. ",-1)),e("div",bo,[l[9]||(l[9]=e("h3",{class:"ai-config-modal__section-title"},"Choose AI Provider",-1)),e("div",$o,[(a(),o(P,null,B(L,i=>e("label",{key:i.id,class:F(["ai-config-modal__provider",{"ai-config-modal__provider--selected":n.value===i.id}])},[R(e("input",{type:"radio",value:i.id,"onUpdate:modelValue":l[1]||(l[1]=r=>n.value=r),class:"ai-config-modal__provider-radio"},null,8,ko),[[fe,n.value]]),e("div",Co,[e("div",So,[e("h4",To,u(i.name),1),e("span",{class:F(["ai-config-modal__provider-badge",`ai-config-modal__provider-badge--${i.tier}`])},u(i.tier),3)]),e("p",wo,u(i.description),1),e("div",Ao,[(a(!0),o(P,null,B(i.features,r=>(a(),o("span",{key:r,class:"ai-config-modal__provider-feature"},u(r),1))),128))])])],2)),64))])]),n.value!=="local"?(a(),o("div",Io,[l[13]||(l[13]=e("h3",{class:"ai-config-modal__section-title"},"API Configuration",-1)),m(Q,{modelValue:_.value,"onUpdate:modelValue":l[2]||(l[2]=i=>_.value=i),label:"API Key",type:"password",placeholder:`Enter your ${z(n.value)} API key`,error:N.value.apiKey},null,8,["modelValue","placeholder","error"]),m(Q,{modelValue:c.value,"onUpdate:modelValue":l[3]||(l[3]=i=>c.value=i),label:"Model (Optional)",placeholder:k(n.value),hint:"Leave empty to use the default model"},null,8,["modelValue","placeholder"]),e("div",Vo,[l[12]||(l[12]=e("h4",null,"How to get your API key:",-1)),n.value==="openai"?(a(),o("div",Uo,l[10]||(l[10]=[e("p",null,[h("1. Go to "),e("a",{href:"https://platform.openai.com/api-keys",target:"_blank"},"OpenAI API Keys")],-1),e("p",null,'2. Click "Create new secret key"',-1),e("p",null,"3. Copy the key and paste it above",-1),e("p",{class:"ai-config-modal__warning"},"⚠️ Keep your API key secure and never share it publicly",-1)]))):n.value==="anthropic"?(a(),o("div",Po,l[11]||(l[11]=[e("p",null,[h("1. Go to "),e("a",{href:"https://console.anthropic.com/",target:"_blank"},"Anthropic Console")],-1),e("p",null,"2. Navigate to API Keys section",-1),e("p",null,"3. Create a new API key",-1),e("p",null,"4. Copy the key and paste it above",-1),e("p",{class:"ai-config-modal__warning"},"⚠️ Keep your API key secure and never share it publicly",-1)]))):$("",!0)])])):(a(),o("div",Bo,[l[14]||(l[14]=e("h3",{class:"ai-config-modal__section-title"},"Local Model Configuration",-1)),m(Q,{modelValue:t.value,"onUpdate:modelValue":l[4]||(l[4]=i=>t.value=i),label:"Base URL",placeholder:"http://localhost:11434",hint:"URL of your local LLM server (e.g., Ollama)"},null,8,["modelValue"]),m(Q,{modelValue:c.value,"onUpdate:modelValue":l[5]||(l[5]=i=>c.value=i),label:"Model Name",placeholder:"llama2",hint:"Name of the local model to use"},null,8,["modelValue"]),l[15]||(l[15]=e("div",{class:"ai-config-modal__local-help"},[e("h4",null,"Setting up a local model:"),e("div",{class:"ai-config-modal__help-content"},[e("p",null,[h("1. Install "),e("a",{href:"https://ollama.ai/",target:"_blank"},"Ollama"),h(" or another local LLM server")]),e("p",null,[h("2. Download a model: "),e("code",null,"ollama pull llama2")]),e("p",null,[h("3. Start the server: "),e("code",null,"ollama serve")]),e("p",null,"4. The default URL should work if running locally")])],-1))])),e("div",Do,[m(T,{variant:"secondary",loading:s.value,disabled:!E.value,onClick:p},{default:C(()=>[h(u(s.value?"Testing...":"Test Connection"),1)]),_:1},8,["loading","disabled"]),I.value?(a(),o("div",{key:0,class:F(["ai-config-modal__test-result",`ai-config-modal__test-result--${I.value.type}`])},u(I.value.message),3)):$("",!0)])]),e("div",Lo,[m(T,{variant:"secondary",onClick:l[6]||(l[6]=i=>v.$emit("close"))},{default:C(()=>l[17]||(l[17]=[h(" Cancel ")])),_:1,__:[17]}),m(T,{variant:"primary",disabled:!O.value,onClick:w},{default:C(()=>l[18]||(l[18]=[h(" Save Configuration ")])),_:1,__:[18]},8,["disabled"])])]))}}),Mo=Y(Oo,[["__scopeId","data-v-b65fb1d9"]]),Go={class:"story-generator"},Fo={class:"story-generator__content"},No={key:0,class:"story-generator__step"},Eo={class:"story-generator__step-actions"},Ro={key:1,class:"story-generator__step"},jo={class:"story-generator__step-actions"},zo={key:2,class:"story-generator__step"},qo={class:"story-generator__step-actions"},Ko={key:3,class:"story-generator__step"},Wo={class:"story-generator__generation"},Yo={class:"story-generator__options"},Ho={class:"story-generator__option-group"},Qo={class:"story-generator__option-buttons"},Jo=["onClick"],Xo={class:"story-generator__option-label"},Zo={class:"story-generator__option-description"},xo={class:"story-generator__option-group"},el={class:"story-generator__option-buttons"},tl=["onClick"],sl={class:"story-generator__option-label"},al={class:"story-generator__option-description"},ol={class:"story-generator__option-group"},ll={class:"story-generator__checkboxes"},il={class:"story-generator__checkbox"},nl=["checked"],rl={class:"story-generator__checkbox"},dl=["checked"],cl={key:0,class:"story-generator__ai-config"},ul={class:"story-generator__generation-actions"},_l={key:0,class:"story-generator__progress"},pl={class:"story-generator__progress-text"},ml={key:4,class:"story-generator__step"},vl={class:"story-generator__complete"},gl={key:0,class:"story-generator__story-preview"},hl={class:"story-generator__story-title"},yl={class:"story-generator__story-summary"},fl={class:"story-generator__story-stats"},bl={class:"story-generator__stat"},$l={class:"story-generator__stat-value"},kl={class:"story-generator__stat"},Cl={class:"story-generator__stat-value"},Sl={class:"story-generator__stat"},Tl={class:"story-generator__stat-value"},wl={class:"story-generator__stat"},Al={class:"story-generator__stat-value"},Il={class:"story-generator__complete-actions"},Vl=W({__name:"StoryGenerator",setup(j){const d=$e(),y=be(),n=ce(),_=ue(),c=pe(),t=D(""),s=D(!1),I=D(null),N=[{key:"characters",label:"Characters"},{key:"settings",label:"Settings"},{key:"themes",label:"Themes"},{key:"generation",label:"Generate"},{key:"complete",label:"Complete"}],L=[{value:"short",label:"Short",description:"~2,000 words, 10 min read"},{value:"medium",label:"Medium",description:"~5,000 words, 25 min read"},{value:"long",label:"Long",description:"~10,000 words, 50 min read"}],E=[{value:"first",label:"First Person",description:"I, me, my"},{value:"third-limited",label:"Third Person Limited",description:"He, she, they (one viewpoint)"},{value:"third-omniscient",label:"Third Person Omniscient",description:"All-knowing narrator"}],O=G(()=>y.generatorState.currentStep),z=G(()=>y.currentStepIndex),k=G(()=>y.generatorState.generationOptions),p=G(()=>y.generatorState.isGenerating),w=G(()=>y.generatorState.progress),v=G(()=>y.currentStory),l=G(()=>n.selectedCharacters.length>0),i=G(()=>_.selectedSettings.length>0),r=G(()=>c.selectedThemes.length>0);function b(X,f){f<=z.value&&y.setCurrentStep(X)}function M(){O.value==="themes"&&(y.generatorState.selectedCharacters=[...n.selectedCharacters],y.generatorState.selectedSettings=[..._.selectedSettings],y.generatorState.selectedThemes=[...c.selectedThemes]),y.nextStep()}function H(){y.previousStep()}function A(X,f){y.updateGenerationOptions({[X]:f})}async function V(){t.value="Analyzing your selections...";try{await y.generateStory()?t.value="Story generated successfully!":t.value="Failed to generate story. Please try again."}catch(X){console.error("Story generation failed:",X),t.value="An error occurred during generation."}}function g(){y.resetGenerator(),n.clearSelectedCharacters(),_.clearSelectedSettings(),c.clearSelectedThemes()}function S(){v.value&&d.push(`/story/${v.value.id}`)}function U(){s.value=!1}function ee(X){I.value={type:"success",message:"✅ AI configuration saved successfully"},s.value=!1,setTimeout(()=>{I.value=null},3e3)}return(X,f)=>(a(),o("div",Go,[f[24]||(f[24]=e("div",{class:"story-generator__header"},[e("h1",{class:"story-generator__title"},"AI Story Builder"),e("p",{class:"story-generator__subtitle"}," Create compelling stories by selecting characters, settings, and themes ")],-1)),m(Oe,{steps:N,"current-step-index":z.value,onStepClick:b},null,8,["current-step-index"]),e("div",Fo,[O.value==="characters"?(a(),o("div",No,[m(Qt),e("div",Eo,[m(T,{variant:"primary",disabled:!l.value,onClick:M},{default:C(()=>f[3]||(f[3]=[h(" Continue to Settings ")])),_:1,__:[3]},8,["disabled"])])])):$("",!0),O.value==="settings"?(a(),o("div",Ro,[m(ia),e("div",jo,[m(T,{variant:"secondary",onClick:H},{default:C(()=>f[4]||(f[4]=[h(" Back to Characters ")])),_:1,__:[4]}),m(T,{variant:"primary",disabled:!i.value,onClick:M},{default:C(()=>f[5]||(f[5]=[h(" Continue to Themes ")])),_:1,__:[5]},8,["disabled"])])])):$("",!0),O.value==="themes"?(a(),o("div",zo,[m(go),e("div",qo,[m(T,{variant:"secondary",onClick:H},{default:C(()=>f[6]||(f[6]=[h(" Back to Settings ")])),_:1,__:[6]}),m(T,{variant:"primary",disabled:!r.value,onClick:M},{default:C(()=>f[7]||(f[7]=[h(" Generate Story ")])),_:1,__:[7]},8,["disabled"])])])):$("",!0),O.value==="generation"?(a(),o("div",Ko,[e("div",Wo,[f[16]||(f[16]=e("div",{class:"story-generator__generation-header"},[e("h2",{class:"story-generator__generation-title"},"Story Generation Options"),e("p",{class:"story-generator__generation-subtitle"}," Customize how your story will be generated ")],-1)),e("div",Yo,[e("div",Ho,[f[8]||(f[8]=e("h3",{class:"story-generator__option-title"},"Story Length",-1)),e("div",Qo,[(a(),o(P,null,B(L,q=>e("button",{key:q.value,class:F(["story-generator__option-button",{"story-generator__option-button--active":k.value.length===q.value}]),onClick:me=>A("length",q.value)},[e("div",Xo,u(q.label),1),e("div",Zo,u(q.description),1)],10,Jo)),64))])]),e("div",xo,[f[9]||(f[9]=e("h3",{class:"story-generator__option-title"},"Narrative Perspective",-1)),e("div",el,[(a(),o(P,null,B(E,q=>e("button",{key:q.value,class:F(["story-generator__option-button",{"story-generator__option-button--active":k.value.narrativePerspective===q.value}]),onClick:me=>A("narrativePerspective",q.value)},[e("div",sl,u(q.label),1),e("div",al,u(q.description),1)],10,tl)),64))])]),e("div",ol,[f[13]||(f[13]=e("h3",{class:"story-generator__option-title"},"Additional Options",-1)),e("div",ll,[e("label",il,[e("input",{type:"checkbox",checked:k.value.includeDialogue,onChange:f[0]||(f[0]=q=>A("includeDialogue",q.target.checked))},null,40,nl),f[10]||(f[10]=e("span",null,"Include dialogue between characters",-1))]),e("label",rl,[e("input",{type:"checkbox",checked:k.value.useAI,onChange:f[1]||(f[1]=q=>A("useAI",q.target.checked))},null,40,dl),f[11]||(f[11]=e("span",null,"Use AI assistance for enhanced content",-1))]),k.value.useAI?(a(),o("div",cl,[m(T,{variant:"ghost",size:"small",onClick:f[2]||(f[2]=q=>s.value=!0)},{default:C(()=>f[12]||(f[12]=[h(" ⚙️ Configure AI Settings ")])),_:1,__:[12]}),I.value?(a(),o("span",{key:0,class:F(["story-generator__ai-status",`story-generator__ai-status--${I.value.type}`])},u(I.value.message),3)):$("",!0)])):$("",!0)])])]),e("div",ul,[m(T,{variant:"secondary",onClick:H},{default:C(()=>f[14]||(f[14]=[h(" Back to Themes ")])),_:1,__:[14]}),m(T,{variant:"primary",loading:p.value,onClick:V},{default:C(()=>[h(u(p.value?"Generating Story...":"Generate My Story"),1)]),_:1},8,["loading"])]),p.value?(a(),o("div",_l,[f[15]||(f[15]=e("h3",{class:"story-generator__progress-title"},"Generating Your Story",-1)),m(we,{progress:w.value},null,8,["progress"]),e("p",pl,u(t.value),1)])):$("",!0)])])):$("",!0),O.value==="complete"?(a(),o("div",ml,[e("div",vl,[f[23]||(f[23]=e("div",{class:"story-generator__complete-header"},[e("div",{class:"story-generator__complete-icon"},"🎉"),e("h2",{class:"story-generator__complete-title"},"Story Generated Successfully!"),e("p",{class:"story-generator__complete-subtitle"}," Your story outline has been created and is ready for editing ")],-1)),v.value?(a(),o("div",gl,[e("h3",hl,u(v.value.outline.title),1),e("p",yl,u(v.value.outline.summary),1),e("div",fl,[e("div",bl,[f[17]||(f[17]=e("span",{class:"story-generator__stat-label"},"Characters:",-1)),e("span",$l,u(v.value.outline.characters.length),1)]),e("div",kl,[f[18]||(f[18]=e("span",{class:"story-generator__stat-label"},"Settings:",-1)),e("span",Cl,u(v.value.outline.settings.length),1)]),e("div",Sl,[f[19]||(f[19]=e("span",{class:"story-generator__stat-label"},"Plot Points:",-1)),e("span",Tl,u(v.value.outline.plotPoints.length),1)]),e("div",wl,[f[20]||(f[20]=e("span",{class:"story-generator__stat-label"},"Est. Reading Time:",-1)),e("span",Al,u(v.value.outline.estimatedReadingTime)+" min",1)])])])):$("",!0),e("div",Il,[m(T,{variant:"secondary",onClick:g},{default:C(()=>f[21]||(f[21]=[h(" Create Another Story ")])),_:1,__:[21]}),m(T,{variant:"primary",onClick:S},{default:C(()=>f[22]||(f[22]=[h(" View & Edit Story ")])),_:1,__:[22]})])])])):$("",!0)]),s.value?(a(),o("div",{key:0,class:"story-generator__modal",onClick:K(U,["self"])},[m(Mo,{onClose:U,onSave:ee})])):$("",!0)]))}}),Ul=Y(Vl,[["__scopeId","data-v-7a21babc"]]),Pl={class:"generator-view"},Bl=W({__name:"GeneratorView",setup(j){return(d,y)=>(a(),o("div",Pl,[m(Ul)]))}}),Ol=Y(Bl,[["__scopeId","data-v-2313f457"]]);export{Ol as default};
