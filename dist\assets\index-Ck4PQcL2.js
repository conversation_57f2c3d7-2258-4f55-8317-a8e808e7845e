const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/GeneratorView-1j8voqWk.js","assets/BaseButton-CZ0pK2wn.js","assets/BaseButton-ClCmFRRR.css","assets/GeneratorView-DI1ogm4J.css","assets/StoryView-ZzP6SpX3.js","assets/StoryView-4gIrAH9x.css","assets/LibraryView-CJ89Wqyo.js","assets/LibraryView-goZ2bvE9.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function $r(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ce={},Nt=[],Ze=()=>{},xi=()=>!1,kn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Mr=e=>e.startsWith("onUpdate:"),Ae=Object.assign,Dr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Oi=Object.prototype.hasOwnProperty,ie=(e,t)=>Oi.call(e,t),G=Array.isArray,Lt=e=>yn(e)==="[object Map]",Wt=e=>yn(e)==="[object Set]",rs=e=>yn(e)==="[object Date]",ee=e=>typeof e=="function",_e=e=>typeof e=="string",Be=e=>typeof e=="symbol",he=e=>e!==null&&typeof e=="object",no=e=>(he(e)||ee(e))&&ee(e.then)&&ee(e.catch),ro=Object.prototype.toString,yn=e=>ro.call(e),Pi=e=>yn(e).slice(8,-1),so=e=>yn(e)==="[object Object]",Nr=e=>_e(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Qt=$r(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),jn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},$i=/-(\w)/g,He=jn(e=>e.replace($i,(t,n)=>n?n.toUpperCase():"")),Mi=/\B([A-Z])/g,It=jn(e=>e.replace(Mi,"-$1").toLowerCase()),Hn=jn(e=>e.charAt(0).toUpperCase()+e.slice(1)),er=jn(e=>e?`on${Hn(e)}`:""),mt=(e,t)=>!Object.is(e,t),Tn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},pr=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},On=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let ss;const Vn=()=>ss||(ss=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Lr(e){if(G(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=_e(r)?Fi(r):Lr(r);if(s)for(const o in s)t[o]=s[o]}return t}else if(_e(e)||he(e))return e}const Di=/;(?![^(]*\))/g,Ni=/:([^]+)/,Li=/\/\*[^]*?\*\//g;function Fi(e){const t={};return e.replace(Li,"").split(Di).forEach(n=>{if(n){const r=n.split(Ni);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Fr(e){let t="";if(_e(e))t=e;else if(G(e))for(let n=0;n<e.length;n++){const r=Fr(e[n]);r&&(t+=r+" ")}else if(he(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const ki="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",ji=$r(ki);function oo(e){return!!e||e===""}function Hi(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=Rt(e[r],t[r]);return n}function Rt(e,t){if(e===t)return!0;let n=rs(e),r=rs(t);if(n||r)return n&&r?e.getTime()===t.getTime():!1;if(n=Be(e),r=Be(t),n||r)return e===t;if(n=G(e),r=G(t),n||r)return n&&r?Hi(e,t):!1;if(n=he(e),r=he(t),n||r){if(!n||!r)return!1;const s=Object.keys(e).length,o=Object.keys(t).length;if(s!==o)return!1;for(const i in e){const a=e.hasOwnProperty(i),l=t.hasOwnProperty(i);if(a&&!l||!a&&l||!Rt(e[i],t[i]))return!1}}return String(e)===String(t)}function kr(e,t){return e.findIndex(n=>Rt(n,t))}const io=e=>!!(e&&e.__v_isRef===!0),Jt=e=>_e(e)?e:e==null?"":G(e)||he(e)&&(e.toString===ro||!ee(e.toString))?io(e)?Jt(e.value):JSON.stringify(e,ao,2):String(e),ao=(e,t)=>io(t)?ao(e,t.value):Lt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,s],o)=>(n[tr(r,o)+" =>"]=s,n),{})}:Wt(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>tr(n))}:Be(t)?tr(t):he(t)&&!G(t)&&!so(t)?String(t):t,tr=(e,t="")=>{var n;return Be(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ee;class lo{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ee,!t&&Ee&&(this.index=(Ee.scopes||(Ee.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Ee;try{return Ee=this,t()}finally{Ee=n}}}on(){++this._on===1&&(this.prevScope=Ee,Ee=this)}off(){this._on>0&&--this._on===0&&(Ee=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(this.effects.length=0,n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0}}}function co(e){return new lo(e)}function uo(){return Ee}function Vi(e,t=!1){Ee&&Ee.cleanups.push(e)}let de;const nr=new WeakSet;class fo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ee&&Ee.active&&Ee.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,nr.has(this)&&(nr.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||po(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,os(this),go(this);const t=de,n=Ve;de=this,Ve=!0;try{return this.fn()}finally{mo(this),de=t,Ve=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Vr(t);this.deps=this.depsTail=void 0,os(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?nr.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){gr(this)&&this.run()}get dirty(){return gr(this)}}let ho=0,Xt,Zt;function po(e,t=!1){if(e.flags|=8,t){e.next=Zt,Zt=e;return}e.next=Xt,Xt=e}function jr(){ho++}function Hr(){if(--ho>0)return;if(Zt){let t=Zt;for(Zt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Xt;){let t=Xt;for(Xt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function go(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function mo(e){let t,n=e.depsTail,r=n;for(;r;){const s=r.prevDep;r.version===-1?(r===n&&(n=s),Vr(r),Bi(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=s}e.deps=t,e.depsTail=n}function gr(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(yo(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function yo(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===ln)||(e.globalVersion=ln,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!gr(e))))return;e.flags|=2;const t=e.dep,n=de,r=Ve;de=e,Ve=!0;try{go(e);const s=e.fn(e._value);(t.version===0||mt(s,e._value))&&(e.flags|=128,e._value=s,t.version++)}catch(s){throw t.version++,s}finally{de=n,Ve=r,mo(e),e.flags&=-3}}function Vr(e,t=!1){const{dep:n,prevSub:r,nextSub:s}=e;if(r&&(r.nextSub=s,e.prevSub=void 0),s&&(s.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Vr(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Bi(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ve=!0;const vo=[];function at(){vo.push(Ve),Ve=!1}function lt(){const e=vo.pop();Ve=e===void 0?!0:e}function os(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=de;de=void 0;try{t()}finally{de=n}}}let ln=0;class Ui{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Br{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!de||!Ve||de===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==de)n=this.activeLink=new Ui(de,this),de.deps?(n.prevDep=de.depsTail,de.depsTail.nextDep=n,de.depsTail=n):de.deps=de.depsTail=n,_o(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=de.depsTail,n.nextDep=void 0,de.depsTail.nextDep=n,de.depsTail=n,de.deps===n&&(de.deps=r)}return n}trigger(t){this.version++,ln++,this.notify(t)}notify(t){jr();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Hr()}}}function _o(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)_o(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Pn=new WeakMap,Tt=Symbol(""),mr=Symbol(""),cn=Symbol("");function Te(e,t,n){if(Ve&&de){let r=Pn.get(e);r||Pn.set(e,r=new Map);let s=r.get(n);s||(r.set(n,s=new Br),s.map=r,s.key=n),s.track()}}function st(e,t,n,r,s,o){const i=Pn.get(e);if(!i){ln++;return}const a=l=>{l&&l.trigger()};if(jr(),t==="clear")i.forEach(a);else{const l=G(e),f=l&&Nr(n);if(l&&n==="length"){const c=Number(r);i.forEach((h,p)=>{(p==="length"||p===cn||!Be(p)&&p>=c)&&a(h)})}else switch((n!==void 0||i.has(void 0))&&a(i.get(n)),f&&a(i.get(cn)),t){case"add":l?f&&a(i.get("length")):(a(i.get(Tt)),Lt(e)&&a(i.get(mr)));break;case"delete":l||(a(i.get(Tt)),Lt(e)&&a(i.get(mr)));break;case"set":Lt(e)&&a(i.get(Tt));break}}Hr()}function Wi(e,t){const n=Pn.get(e);return n&&n.get(t)}function Pt(e){const t=re(e);return t===e?t:(Te(t,"iterate",cn),Fe(e)?t:t.map(Se))}function Bn(e){return Te(e=re(e),"iterate",cn),e}const Ki={__proto__:null,[Symbol.iterator](){return rr(this,Symbol.iterator,Se)},concat(...e){return Pt(this).concat(...e.map(t=>G(t)?Pt(t):t))},entries(){return rr(this,"entries",e=>(e[1]=Se(e[1]),e))},every(e,t){return tt(this,"every",e,t,void 0,arguments)},filter(e,t){return tt(this,"filter",e,t,n=>n.map(Se),arguments)},find(e,t){return tt(this,"find",e,t,Se,arguments)},findIndex(e,t){return tt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return tt(this,"findLast",e,t,Se,arguments)},findLastIndex(e,t){return tt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return tt(this,"forEach",e,t,void 0,arguments)},includes(...e){return sr(this,"includes",e)},indexOf(...e){return sr(this,"indexOf",e)},join(e){return Pt(this).join(e)},lastIndexOf(...e){return sr(this,"lastIndexOf",e)},map(e,t){return tt(this,"map",e,t,void 0,arguments)},pop(){return Yt(this,"pop")},push(...e){return Yt(this,"push",e)},reduce(e,...t){return is(this,"reduce",e,t)},reduceRight(e,...t){return is(this,"reduceRight",e,t)},shift(){return Yt(this,"shift")},some(e,t){return tt(this,"some",e,t,void 0,arguments)},splice(...e){return Yt(this,"splice",e)},toReversed(){return Pt(this).toReversed()},toSorted(e){return Pt(this).toSorted(e)},toSpliced(...e){return Pt(this).toSpliced(...e)},unshift(...e){return Yt(this,"unshift",e)},values(){return rr(this,"values",Se)}};function rr(e,t,n){const r=Bn(e),s=r[t]();return r!==e&&!Fe(e)&&(s._next=s.next,s.next=()=>{const o=s._next();return o.value&&(o.value=n(o.value)),o}),s}const Yi=Array.prototype;function tt(e,t,n,r,s,o){const i=Bn(e),a=i!==e&&!Fe(e),l=i[t];if(l!==Yi[t]){const h=l.apply(e,o);return a?Se(h):h}let f=n;i!==e&&(a?f=function(h,p){return n.call(this,Se(h),p,e)}:n.length>2&&(f=function(h,p){return n.call(this,h,p,e)}));const c=l.call(i,f,r);return a&&s?s(c):c}function is(e,t,n,r){const s=Bn(e);let o=n;return s!==e&&(Fe(e)?n.length>3&&(o=function(i,a,l){return n.call(this,i,a,l,e)}):o=function(i,a,l){return n.call(this,i,Se(a),l,e)}),s[t](o,...r)}function sr(e,t,n){const r=re(e);Te(r,"iterate",cn);const s=r[t](...n);return(s===-1||s===!1)&&Kr(n[0])?(n[0]=re(n[0]),r[t](...n)):s}function Yt(e,t,n=[]){at(),jr();const r=re(e)[t].apply(e,n);return Hr(),lt(),r}const Gi=$r("__proto__,__v_isRef,__isVue"),bo=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Be));function qi(e){Be(e)||(e=String(e));const t=re(this);return Te(t,"has",e),t.hasOwnProperty(e)}class wo{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){if(n==="__v_skip")return t.__v_skip;const s=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!s;if(n==="__v_isReadonly")return s;if(n==="__v_isShallow")return o;if(n==="__v_raw")return r===(s?o?sa:To:o?Eo:Co).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=G(t);if(!s){let l;if(i&&(l=Ki[n]))return l;if(n==="hasOwnProperty")return qi}const a=Reflect.get(t,n,ve(t)?t:r);return(Be(n)?bo.has(n):Gi(n))||(s||Te(t,"get",n),o)?a:ve(a)?i&&Nr(n)?a:a.value:he(a)?s?Ro(a):vn(a):a}}class So extends wo{constructor(t=!1){super(!1,t)}set(t,n,r,s){let o=t[n];if(!this._isShallow){const l=vt(o);if(!Fe(r)&&!vt(r)&&(o=re(o),r=re(r)),!G(t)&&ve(o)&&!ve(r))return l?!1:(o.value=r,!0)}const i=G(t)&&Nr(n)?Number(n)<t.length:ie(t,n),a=Reflect.set(t,n,r,ve(t)?t:s);return t===re(s)&&(i?mt(r,o)&&st(t,"set",n,r):st(t,"add",n,r)),a}deleteProperty(t,n){const r=ie(t,n);t[n];const s=Reflect.deleteProperty(t,n);return s&&r&&st(t,"delete",n,void 0),s}has(t,n){const r=Reflect.has(t,n);return(!Be(n)||!bo.has(n))&&Te(t,"has",n),r}ownKeys(t){return Te(t,"iterate",G(t)?"length":Tt),Reflect.ownKeys(t)}}class Ji extends wo{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const zi=new So,Qi=new Ji,Xi=new So(!0);const yr=e=>e,Sn=e=>Reflect.getPrototypeOf(e);function Zi(e,t,n){return function(...r){const s=this.__v_raw,o=re(s),i=Lt(o),a=e==="entries"||e===Symbol.iterator&&i,l=e==="keys"&&i,f=s[e](...r),c=n?yr:t?$n:Se;return!t&&Te(o,"iterate",l?mr:Tt),{next(){const{value:h,done:p}=f.next();return p?{value:h,done:p}:{value:a?[c(h[0]),c(h[1])]:c(h),done:p}},[Symbol.iterator](){return this}}}}function Cn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ea(e,t){const n={get(s){const o=this.__v_raw,i=re(o),a=re(s);e||(mt(s,a)&&Te(i,"get",s),Te(i,"get",a));const{has:l}=Sn(i),f=t?yr:e?$n:Se;if(l.call(i,s))return f(o.get(s));if(l.call(i,a))return f(o.get(a));o!==i&&o.get(s)},get size(){const s=this.__v_raw;return!e&&Te(re(s),"iterate",Tt),Reflect.get(s,"size",s)},has(s){const o=this.__v_raw,i=re(o),a=re(s);return e||(mt(s,a)&&Te(i,"has",s),Te(i,"has",a)),s===a?o.has(s):o.has(s)||o.has(a)},forEach(s,o){const i=this,a=i.__v_raw,l=re(a),f=t?yr:e?$n:Se;return!e&&Te(l,"iterate",Tt),a.forEach((c,h)=>s.call(o,f(c),f(h),i))}};return Ae(n,e?{add:Cn("add"),set:Cn("set"),delete:Cn("delete"),clear:Cn("clear")}:{add(s){!t&&!Fe(s)&&!vt(s)&&(s=re(s));const o=re(this);return Sn(o).has.call(o,s)||(o.add(s),st(o,"add",s,s)),this},set(s,o){!t&&!Fe(o)&&!vt(o)&&(o=re(o));const i=re(this),{has:a,get:l}=Sn(i);let f=a.call(i,s);f||(s=re(s),f=a.call(i,s));const c=l.call(i,s);return i.set(s,o),f?mt(o,c)&&st(i,"set",s,o):st(i,"add",s,o),this},delete(s){const o=re(this),{has:i,get:a}=Sn(o);let l=i.call(o,s);l||(s=re(s),l=i.call(o,s)),a&&a.call(o,s);const f=o.delete(s);return l&&st(o,"delete",s,void 0),f},clear(){const s=re(this),o=s.size!==0,i=s.clear();return o&&st(s,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(s=>{n[s]=Zi(s,e,t)}),n}function Ur(e,t){const n=ea(e,t);return(r,s,o)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(ie(n,s)&&s in r?n:r,s,o)}const ta={get:Ur(!1,!1)},na={get:Ur(!1,!0)},ra={get:Ur(!0,!1)};const Co=new WeakMap,Eo=new WeakMap,To=new WeakMap,sa=new WeakMap;function oa(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ia(e){return e.__v_skip||!Object.isExtensible(e)?0:oa(Pi(e))}function vn(e){return vt(e)?e:Wr(e,!1,zi,ta,Co)}function Ao(e){return Wr(e,!1,Xi,na,Eo)}function Ro(e){return Wr(e,!0,Qi,ra,To)}function Wr(e,t,n,r,s){if(!he(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=ia(e);if(o===0)return e;const i=s.get(e);if(i)return i;const a=new Proxy(e,o===2?r:n);return s.set(e,a),a}function yt(e){return vt(e)?yt(e.__v_raw):!!(e&&e.__v_isReactive)}function vt(e){return!!(e&&e.__v_isReadonly)}function Fe(e){return!!(e&&e.__v_isShallow)}function Kr(e){return e?!!e.__v_raw:!1}function re(e){const t=e&&e.__v_raw;return t?re(t):e}function Yr(e){return!ie(e,"__v_skip")&&Object.isExtensible(e)&&pr(e,"__v_skip",!0),e}const Se=e=>he(e)?vn(e):e,$n=e=>he(e)?Ro(e):e;function ve(e){return e?e.__v_isRef===!0:!1}function be(e){return Io(e,!1)}function aa(e){return Io(e,!0)}function Io(e,t){return ve(e)?e:new la(e,t)}class la{constructor(t,n){this.dep=new Br,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:re(t),this._value=n?t:Se(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||Fe(t)||vt(t);t=r?t:re(t),mt(t,n)&&(this._rawValue=t,this._value=r?t:Se(t),this.dep.trigger())}}function Ft(e){return ve(e)?e.value:e}const ca={get:(e,t,n)=>t==="__v_raw"?e:Ft(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return ve(s)&&!ve(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function xo(e){return yt(e)?e:new Proxy(e,ca)}function ua(e){const t=G(e)?new Array(e.length):{};for(const n in e)t[n]=da(e,n);return t}class fa{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Wi(re(this._object),this._key)}}function da(e,t,n){const r=e[t];return ve(r)?r:new fa(e,t,n)}class ha{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Br(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=ln-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&de!==this)return po(this,!0),!0}get value(){const t=this.dep.track();return yo(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function pa(e,t,n=!1){let r,s;return ee(e)?r=e:(r=e.get,s=e.set),new ha(r,s,n)}const En={},Mn=new WeakMap;let St;function ga(e,t=!1,n=St){if(n){let r=Mn.get(n);r||Mn.set(n,r=[]),r.push(e)}}function ma(e,t,n=ce){const{immediate:r,deep:s,once:o,scheduler:i,augmentJob:a,call:l}=n,f=N=>s?N:Fe(N)||s===!1||s===0?ot(N,1):ot(N);let c,h,p,m,R=!1,O=!1;if(ve(e)?(h=()=>e.value,R=Fe(e)):yt(e)?(h=()=>f(e),R=!0):G(e)?(O=!0,R=e.some(N=>yt(N)||Fe(N)),h=()=>e.map(N=>{if(ve(N))return N.value;if(yt(N))return f(N);if(ee(N))return l?l(N,2):N()})):ee(e)?t?h=l?()=>l(e,2):e:h=()=>{if(p){at();try{p()}finally{lt()}}const N=St;St=c;try{return l?l(e,3,[m]):e(m)}finally{St=N}}:h=Ze,t&&s){const N=h,Q=s===!0?1/0:s;h=()=>ot(N(),Q)}const q=uo(),V=()=>{c.stop(),q&&q.active&&Dr(q.effects,c)};if(o&&t){const N=t;t=(...Q)=>{N(...Q),V()}}let F=O?new Array(e.length).fill(En):En;const H=N=>{if(!(!(c.flags&1)||!c.dirty&&!N))if(t){const Q=c.run();if(s||R||(O?Q.some((ue,D)=>mt(ue,F[D])):mt(Q,F))){p&&p();const ue=St;St=c;try{const D=[Q,F===En?void 0:O&&F[0]===En?[]:F,m];F=Q,l?l(t,3,D):t(...D)}finally{St=ue}}}else c.run()};return a&&a(H),c=new fo(h),c.scheduler=i?()=>i(H,!1):H,m=N=>ga(N,!1,c),p=c.onStop=()=>{const N=Mn.get(c);if(N){if(l)l(N,4);else for(const Q of N)Q();Mn.delete(c)}},t?r?H(!0):F=c.run():i?i(H.bind(null,!0),!0):c.run(),V.pause=c.pause.bind(c),V.resume=c.resume.bind(c),V.stop=V,V}function ot(e,t=1/0,n){if(t<=0||!he(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ve(e))ot(e.value,t,n);else if(G(e))for(let r=0;r<e.length;r++)ot(e[r],t,n);else if(Wt(e)||Lt(e))e.forEach(r=>{ot(r,t,n)});else if(so(e)){for(const r in e)ot(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&ot(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function _n(e,t,n,r){try{return r?e(...r):e()}catch(s){Un(s,t,n)}}function et(e,t,n,r){if(ee(e)){const s=_n(e,t,n,r);return s&&no(s)&&s.catch(o=>{Un(o,t,n)}),s}if(G(e)){const s=[];for(let o=0;o<e.length;o++)s.push(et(e[o],t,n,r));return s}}function Un(e,t,n,r=!0){const s=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ce;if(t){let a=t.parent;const l=t.proxy,f=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const c=a.ec;if(c){for(let h=0;h<c.length;h++)if(c[h](e,l,f)===!1)return}a=a.parent}if(o){at(),_n(o,null,10,[e,l,f]),lt();return}}ya(e,n,s,r,i)}function ya(e,t,n,r=!0,s=!1){if(s)throw e;console.error(e)}const Ie=[];let Qe=-1;const kt=[];let ht=null,Mt=0;const Oo=Promise.resolve();let Dn=null;function Wn(e){const t=Dn||Oo;return e?t.then(this?e.bind(this):e):t}function va(e){let t=Qe+1,n=Ie.length;for(;t<n;){const r=t+n>>>1,s=Ie[r],o=un(s);o<e||o===e&&s.flags&2?t=r+1:n=r}return t}function Gr(e){if(!(e.flags&1)){const t=un(e),n=Ie[Ie.length-1];!n||!(e.flags&2)&&t>=un(n)?Ie.push(e):Ie.splice(va(t),0,e),e.flags|=1,Po()}}function Po(){Dn||(Dn=Oo.then(Mo))}function _a(e){G(e)?kt.push(...e):ht&&e.id===-1?ht.splice(Mt+1,0,e):e.flags&1||(kt.push(e),e.flags|=1),Po()}function as(e,t,n=Qe+1){for(;n<Ie.length;n++){const r=Ie[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Ie.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&4||(r.flags&=-2)}}}function $o(e){if(kt.length){const t=[...new Set(kt)].sort((n,r)=>un(n)-un(r));if(kt.length=0,ht){ht.push(...t);return}for(ht=t,Mt=0;Mt<ht.length;Mt++){const n=ht[Mt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}ht=null,Mt=0}}const un=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Mo(e){try{for(Qe=0;Qe<Ie.length;Qe++){const t=Ie[Qe];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),_n(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Qe<Ie.length;Qe++){const t=Ie[Qe];t&&(t.flags&=-2)}Qe=-1,Ie.length=0,$o(),Dn=null,(Ie.length||kt.length)&&Mo()}}let we=null,Do=null;function Nn(e){const t=we;return we=e,Do=e&&e.type.__scopeId||null,t}function Ct(e,t=we,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&ys(-1);const o=Nn(t);let i;try{i=e(...s)}finally{Nn(o),r._d&&ys(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function Wu(e,t){if(we===null)return e;const n=Jn(we),r=e.dirs||(e.dirs=[]);for(let s=0;s<t.length;s++){let[o,i,a,l=ce]=t[s];o&&(ee(o)&&(o={mounted:o,updated:o}),o.deep&&ot(i),r.push({dir:o,instance:n,value:i,oldValue:void 0,arg:a,modifiers:l}))}return e}function bt(e,t,n,r){const s=e.dirs,o=t&&t.dirs;for(let i=0;i<s.length;i++){const a=s[i];o&&(a.oldValue=o[i].value);let l=a.dir[r];l&&(at(),et(l,n,8,[e.el,a,e,t]),lt())}}const ba=Symbol("_vte"),wa=e=>e.__isTeleport;function qr(e,t){e.shapeFlag&6&&e.component?(e.transition=t,qr(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Kn(e,t){return ee(e)?Ae({name:e.name},t,{setup:e}):e}function No(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function en(e,t,n,r,s=!1){if(G(e)){e.forEach((R,O)=>en(R,t&&(G(t)?t[O]:t),n,r,s));return}if(jt(r)&&!s){r.shapeFlag&512&&r.type.__asyncResolved&&r.component.subTree.component&&en(e,t,n,r.component.subTree);return}const o=r.shapeFlag&4?Jn(r.component):r.el,i=s?null:o,{i:a,r:l}=e,f=t&&t.r,c=a.refs===ce?a.refs={}:a.refs,h=a.setupState,p=re(h),m=h===ce?()=>!1:R=>ie(p,R);if(f!=null&&f!==l&&(_e(f)?(c[f]=null,m(f)&&(h[f]=null)):ve(f)&&(f.value=null)),ee(l))_n(l,a,12,[i,c]);else{const R=_e(l),O=ve(l);if(R||O){const q=()=>{if(e.f){const V=R?m(l)?h[l]:c[l]:l.value;s?G(V)&&Dr(V,o):G(V)?V.includes(o)||V.push(o):R?(c[l]=[o],m(l)&&(h[l]=c[l])):(l.value=[o],e.k&&(c[e.k]=l.value))}else R?(c[l]=i,m(l)&&(h[l]=i)):O&&(l.value=i,e.k&&(c[e.k]=i))};i?(q.id=-1,Me(q,n)):q()}}}Vn().requestIdleCallback;Vn().cancelIdleCallback;const jt=e=>!!e.type.__asyncLoader,Lo=e=>e.type.__isKeepAlive;function Sa(e,t){Fo(e,"a",t)}function Ca(e,t){Fo(e,"da",t)}function Fo(e,t,n=Ce){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(Yn(t,r,n),n){let s=n.parent;for(;s&&s.parent;)Lo(s.parent.vnode)&&Ea(r,t,n,s),s=s.parent}}function Ea(e,t,n,r){const s=Yn(t,e,r,!0);jo(()=>{Dr(r[t],s)},n)}function Yn(e,t,n=Ce,r=!1){if(n){const s=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{at();const a=bn(n),l=et(t,n,e,i);return a(),lt(),l});return r?s.unshift(o):s.push(o),o}}const ut=e=>(t,n=Ce)=>{(!pn||e==="sp")&&Yn(e,(...r)=>t(...r),n)},Ta=ut("bm"),ko=ut("m"),Aa=ut("bu"),Ra=ut("u"),Ia=ut("bum"),jo=ut("um"),xa=ut("sp"),Oa=ut("rtg"),Pa=ut("rtc");function $a(e,t=Ce){Yn("ec",e,t)}const Ma="components";function vr(e,t){return Na(Ma,e,!0,t)||e}const Da=Symbol.for("v-ndc");function Na(e,t,n=!0,r=!1){const s=we||Ce;if(s){const o=s.type;{const a=Sl(o,!1);if(a&&(a===t||a===He(t)||a===Hn(He(t))))return o}const i=ls(s[e]||o[e],t)||ls(s.appContext[e],t);return!i&&r?o:i}}function ls(e,t){return e&&(e[t]||e[He(t)]||e[Hn(He(t))])}function Ku(e,t,n,r){let s;const o=n,i=G(e);if(i||_e(e)){const a=i&&yt(e);let l=!1,f=!1;a&&(l=!Fe(e),f=vt(e),e=Bn(e)),s=new Array(e.length);for(let c=0,h=e.length;c<h;c++)s[c]=t(l?f?$n(Se(e[c])):Se(e[c]):e[c],c,void 0,o)}else if(typeof e=="number"){s=new Array(e);for(let a=0;a<e;a++)s[a]=t(a+1,a,void 0,o)}else if(he(e))if(e[Symbol.iterator])s=Array.from(e,(a,l)=>t(a,l,void 0,o));else{const a=Object.keys(e);s=new Array(a.length);for(let l=0,f=a.length;l<f;l++){const c=a[l];s[l]=t(e[c],c,l,o)}}else s=[];return s}function Yu(e,t,n={},r,s){if(we.ce||we.parent&&jt(we.parent)&&we.parent.ce)return t!=="default"&&(n.name=t),fn(),Cr(Le,null,[me("slot",n,r)],64);let o=e[t];o&&o._c&&(o._d=!1),fn();const i=o&&Ho(o(n)),a=n.key||i&&i.key,l=Cr(Le,{key:(a&&!Be(a)?a:`_${t}`)+(!i&&r?"_fb":"")},i||[],i&&e._===1?64:-2);return o&&o._c&&(o._d=!0),l}function Ho(e){return e.some(t=>hn(t)?!(t.type===ct||t.type===Le&&!Ho(t.children)):!0)?e:null}const _r=e=>e?ai(e)?Jn(e):_r(e.parent):null,tn=Ae(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>_r(e.parent),$root:e=>_r(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Bo(e),$forceUpdate:e=>e.f||(e.f=()=>{Gr(e.update)}),$nextTick:e=>e.n||(e.n=Wn.bind(e.proxy)),$watch:e=>rl.bind(e)}),or=(e,t)=>e!==ce&&!e.__isScriptSetup&&ie(e,t),La={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:s,props:o,accessCache:i,type:a,appContext:l}=e;let f;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return o[t]}else{if(or(r,t))return i[t]=1,r[t];if(s!==ce&&ie(s,t))return i[t]=2,s[t];if((f=e.propsOptions[0])&&ie(f,t))return i[t]=3,o[t];if(n!==ce&&ie(n,t))return i[t]=4,n[t];br&&(i[t]=0)}}const c=tn[t];let h,p;if(c)return t==="$attrs"&&Te(e.attrs,"get",""),c(e);if((h=a.__cssModules)&&(h=h[t]))return h;if(n!==ce&&ie(n,t))return i[t]=4,n[t];if(p=l.config.globalProperties,ie(p,t))return p[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:o}=e;return or(s,t)?(s[t]=n,!0):r!==ce&&ie(r,t)?(r[t]=n,!0):ie(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:o}},i){let a;return!!n[i]||e!==ce&&ie(e,i)||or(t,i)||(a=o[0])&&ie(a,i)||ie(r,i)||ie(tn,i)||ie(s.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ie(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function cs(e){return G(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let br=!0;function Fa(e){const t=Bo(e),n=e.proxy,r=e.ctx;br=!1,t.beforeCreate&&us(t.beforeCreate,e,"bc");const{data:s,computed:o,methods:i,watch:a,provide:l,inject:f,created:c,beforeMount:h,mounted:p,beforeUpdate:m,updated:R,activated:O,deactivated:q,beforeDestroy:V,beforeUnmount:F,destroyed:H,unmounted:N,render:Q,renderTracked:ue,renderTriggered:D,errorCaptured:w,serverPrefetch:S,expose:v,inheritAttrs:k,components:X,directives:A,filters:T}=t;if(f&&ka(f,r,null),i)for(const j in i){const J=i[j];ee(J)&&(r[j]=J.bind(n))}if(s){const j=s.call(n,n);he(j)&&(e.data=vn(j))}if(br=!0,o)for(const j in o){const J=o[j],ge=ee(J)?J.bind(n,n):ee(J.get)?J.get.bind(n,n):Ze,We=!ee(J)&&ee(J.set)?J.set.bind(n):Ze,Ke=te({get:ge,set:We});Object.defineProperty(r,j,{enumerable:!0,configurable:!0,get:()=>Ke.value,set:xe=>Ke.value=xe})}if(a)for(const j in a)Vo(a[j],r,n,j);if(l){const j=ee(l)?l.call(n):l;Reflect.ownKeys(j).forEach(J=>{An(J,j[J])})}c&&us(c,e,"c");function B(j,J){G(J)?J.forEach(ge=>j(ge.bind(n))):J&&j(J.bind(n))}if(B(Ta,h),B(ko,p),B(Aa,m),B(Ra,R),B(Sa,O),B(Ca,q),B($a,w),B(Pa,ue),B(Oa,D),B(Ia,F),B(jo,N),B(xa,S),G(v))if(v.length){const j=e.exposed||(e.exposed={});v.forEach(J=>{Object.defineProperty(j,J,{get:()=>n[J],set:ge=>n[J]=ge})})}else e.exposed||(e.exposed={});Q&&e.render===Ze&&(e.render=Q),k!=null&&(e.inheritAttrs=k),X&&(e.components=X),A&&(e.directives=A),S&&No(e)}function ka(e,t,n=Ze){G(e)&&(e=wr(e));for(const r in e){const s=e[r];let o;he(s)?"default"in s?o=ke(s.from||r,s.default,!0):o=ke(s.from||r):o=ke(s),ve(o)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[r]=o}}function us(e,t,n){et(G(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function Vo(e,t,n,r){let s=r.includes(".")?ti(n,r):()=>n[r];if(_e(e)){const o=t[e];ee(o)&&nn(s,o)}else if(ee(e))nn(s,e.bind(n));else if(he(e))if(G(e))e.forEach(o=>Vo(o,t,n,r));else{const o=ee(e.handler)?e.handler.bind(n):t[e.handler];ee(o)&&nn(s,o,e)}}function Bo(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,a=o.get(t);let l;return a?l=a:!s.length&&!n&&!r?l=t:(l={},s.length&&s.forEach(f=>Ln(l,f,i,!0)),Ln(l,t,i)),he(t)&&o.set(t,l),l}function Ln(e,t,n,r=!1){const{mixins:s,extends:o}=t;o&&Ln(e,o,n,!0),s&&s.forEach(i=>Ln(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const a=ja[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const ja={data:fs,props:ds,emits:ds,methods:zt,computed:zt,beforeCreate:Re,created:Re,beforeMount:Re,mounted:Re,beforeUpdate:Re,updated:Re,beforeDestroy:Re,beforeUnmount:Re,destroyed:Re,unmounted:Re,activated:Re,deactivated:Re,errorCaptured:Re,serverPrefetch:Re,components:zt,directives:zt,watch:Va,provide:fs,inject:Ha};function fs(e,t){return t?e?function(){return Ae(ee(e)?e.call(this,this):e,ee(t)?t.call(this,this):t)}:t:e}function Ha(e,t){return zt(wr(e),wr(t))}function wr(e){if(G(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Re(e,t){return e?[...new Set([].concat(e,t))]:t}function zt(e,t){return e?Ae(Object.create(null),e,t):t}function ds(e,t){return e?G(e)&&G(t)?[...new Set([...e,...t])]:Ae(Object.create(null),cs(e),cs(t??{})):t}function Va(e,t){if(!e)return t;if(!t)return e;const n=Ae(Object.create(null),e);for(const r in t)n[r]=Re(e[r],t[r]);return n}function Uo(){return{app:null,config:{isNativeTag:xi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Ba=0;function Ua(e,t){return function(r,s=null){ee(r)||(r=Ae({},r)),s!=null&&!he(s)&&(s=null);const o=Uo(),i=new WeakSet,a=[];let l=!1;const f=o.app={_uid:Ba++,_component:r,_props:s,_container:null,_context:o,_instance:null,version:El,get config(){return o.config},set config(c){},use(c,...h){return i.has(c)||(c&&ee(c.install)?(i.add(c),c.install(f,...h)):ee(c)&&(i.add(c),c(f,...h))),f},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),f},component(c,h){return h?(o.components[c]=h,f):o.components[c]},directive(c,h){return h?(o.directives[c]=h,f):o.directives[c]},mount(c,h,p){if(!l){const m=f._ceVNode||me(r,s);return m.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),e(m,c,p),l=!0,f._container=c,c.__vue_app__=f,Jn(m.component)}},onUnmount(c){a.push(c)},unmount(){l&&(et(a,f._instance,16),e(null,f._container),delete f._container.__vue_app__)},provide(c,h){return o.provides[c]=h,f},runWithContext(c){const h=At;At=f;try{return c()}finally{At=h}}};return f}}let At=null;function An(e,t){if(Ce){let n=Ce.provides;const r=Ce.parent&&Ce.parent.provides;r===n&&(n=Ce.provides=Object.create(r)),n[e]=t}}function ke(e,t,n=!1){const r=Ce||we;if(r||At){let s=At?At._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(s&&e in s)return s[e];if(arguments.length>1)return n&&ee(t)?t.call(r&&r.proxy):t}}function Wa(){return!!(Ce||we||At)}const Wo={},Ko=()=>Object.create(Wo),Yo=e=>Object.getPrototypeOf(e)===Wo;function Ka(e,t,n,r=!1){const s={},o=Ko();e.propsDefaults=Object.create(null),Go(e,t,s,o);for(const i in e.propsOptions[0])i in s||(s[i]=void 0);n?e.props=r?s:Ao(s):e.type.props?e.props=s:e.props=o,e.attrs=o}function Ya(e,t,n,r){const{props:s,attrs:o,vnode:{patchFlag:i}}=e,a=re(s),[l]=e.propsOptions;let f=!1;if((r||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let h=0;h<c.length;h++){let p=c[h];if(Gn(e.emitsOptions,p))continue;const m=t[p];if(l)if(ie(o,p))m!==o[p]&&(o[p]=m,f=!0);else{const R=He(p);s[R]=Sr(l,a,R,m,e,!1)}else m!==o[p]&&(o[p]=m,f=!0)}}}else{Go(e,t,s,o)&&(f=!0);let c;for(const h in a)(!t||!ie(t,h)&&((c=It(h))===h||!ie(t,c)))&&(l?n&&(n[h]!==void 0||n[c]!==void 0)&&(s[h]=Sr(l,a,h,void 0,e,!0)):delete s[h]);if(o!==a)for(const h in o)(!t||!ie(t,h))&&(delete o[h],f=!0)}f&&st(e.attrs,"set","")}function Go(e,t,n,r){const[s,o]=e.propsOptions;let i=!1,a;if(t)for(let l in t){if(Qt(l))continue;const f=t[l];let c;s&&ie(s,c=He(l))?!o||!o.includes(c)?n[c]=f:(a||(a={}))[c]=f:Gn(e.emitsOptions,l)||(!(l in r)||f!==r[l])&&(r[l]=f,i=!0)}if(o){const l=re(n),f=a||ce;for(let c=0;c<o.length;c++){const h=o[c];n[h]=Sr(s,l,h,f[h],e,!ie(f,h))}}return i}function Sr(e,t,n,r,s,o){const i=e[n];if(i!=null){const a=ie(i,"default");if(a&&r===void 0){const l=i.default;if(i.type!==Function&&!i.skipFactory&&ee(l)){const{propsDefaults:f}=s;if(n in f)r=f[n];else{const c=bn(s);r=f[n]=l.call(null,t),c()}}else r=l;s.ce&&s.ce._setProp(n,r)}i[0]&&(o&&!a?r=!1:i[1]&&(r===""||r===It(n))&&(r=!0))}return r}const Ga=new WeakMap;function qo(e,t,n=!1){const r=n?Ga:t.propsCache,s=r.get(e);if(s)return s;const o=e.props,i={},a=[];let l=!1;if(!ee(e)){const c=h=>{l=!0;const[p,m]=qo(h,t,!0);Ae(i,p),m&&a.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!l)return he(e)&&r.set(e,Nt),Nt;if(G(o))for(let c=0;c<o.length;c++){const h=He(o[c]);hs(h)&&(i[h]=ce)}else if(o)for(const c in o){const h=He(c);if(hs(h)){const p=o[c],m=i[h]=G(p)||ee(p)?{type:p}:Ae({},p),R=m.type;let O=!1,q=!0;if(G(R))for(let V=0;V<R.length;++V){const F=R[V],H=ee(F)&&F.name;if(H==="Boolean"){O=!0;break}else H==="String"&&(q=!1)}else O=ee(R)&&R.name==="Boolean";m[0]=O,m[1]=q,(O||ie(m,"default"))&&a.push(h)}}const f=[i,a];return he(e)&&r.set(e,f),f}function hs(e){return e[0]!=="$"&&!Qt(e)}const Jr=e=>e[0]==="_"||e==="$stable",zr=e=>G(e)?e.map(Xe):[Xe(e)],qa=(e,t,n)=>{if(t._n)return t;const r=Ct((...s)=>zr(t(...s)),n);return r._c=!1,r},Jo=(e,t,n)=>{const r=e._ctx;for(const s in e){if(Jr(s))continue;const o=e[s];if(ee(o))t[s]=qa(s,o,r);else if(o!=null){const i=zr(o);t[s]=()=>i}}},zo=(e,t)=>{const n=zr(t);e.slots.default=()=>n},Qo=(e,t,n)=>{for(const r in t)(n||!Jr(r))&&(e[r]=t[r])},Ja=(e,t,n)=>{const r=e.slots=Ko();if(e.vnode.shapeFlag&32){const s=t.__;s&&pr(r,"__",s,!0);const o=t._;o?(Qo(r,t,n),n&&pr(r,"_",o,!0)):Jo(t,r)}else t&&zo(e,t)},za=(e,t,n)=>{const{vnode:r,slots:s}=e;let o=!0,i=ce;if(r.shapeFlag&32){const a=t._;a?n&&a===1?o=!1:Qo(s,t,n):(o=!t.$stable,Jo(t,s)),i=t}else t&&(zo(e,t),i={default:1});if(o)for(const a in s)!Jr(a)&&i[a]==null&&delete s[a]},Me=ul;function Qa(e){return Xa(e)}function Xa(e,t){const n=Vn();n.__VUE__=!0;const{insert:r,remove:s,patchProp:o,createElement:i,createText:a,createComment:l,setText:f,setElementText:c,parentNode:h,nextSibling:p,setScopeId:m=Ze,insertStaticContent:R}=e,O=(u,d,g,y=null,C=null,b=null,P=void 0,x=null,I=!!d.dynamicChildren)=>{if(u===d)return;u&&!Gt(u,d)&&(y=_(u),xe(u,C,b,!0),u=null),d.patchFlag===-2&&(I=!1,d.dynamicChildren=null);const{type:E,ref:Y,shapeFlag:M}=d;switch(E){case qn:q(u,d,g,y);break;case ct:V(u,d,g,y);break;case Rn:u==null&&F(d,g,y,P);break;case Le:X(u,d,g,y,C,b,P,x,I);break;default:M&1?Q(u,d,g,y,C,b,P,x,I):M&6?A(u,d,g,y,C,b,P,x,I):(M&64||M&128)&&E.process(u,d,g,y,C,b,P,x,I,U)}Y!=null&&C?en(Y,u&&u.ref,b,d||u,!d):Y==null&&u&&u.ref!=null&&en(u.ref,null,b,u,!0)},q=(u,d,g,y)=>{if(u==null)r(d.el=a(d.children),g,y);else{const C=d.el=u.el;d.children!==u.children&&f(C,d.children)}},V=(u,d,g,y)=>{u==null?r(d.el=l(d.children||""),g,y):d.el=u.el},F=(u,d,g,y)=>{[u.el,u.anchor]=R(u.children,d,g,y,u.el,u.anchor)},H=({el:u,anchor:d},g,y)=>{let C;for(;u&&u!==d;)C=p(u),r(u,g,y),u=C;r(d,g,y)},N=({el:u,anchor:d})=>{let g;for(;u&&u!==d;)g=p(u),s(u),u=g;s(d)},Q=(u,d,g,y,C,b,P,x,I)=>{d.type==="svg"?P="svg":d.type==="math"&&(P="mathml"),u==null?ue(d,g,y,C,b,P,x,I):S(u,d,C,b,P,x,I)},ue=(u,d,g,y,C,b,P,x)=>{let I,E;const{props:Y,shapeFlag:M,transition:K,dirs:Z}=u;if(I=u.el=i(u.type,b,Y&&Y.is,Y),M&8?c(I,u.children):M&16&&w(u.children,I,null,y,C,ir(u,b),P,x),Z&&bt(u,null,y,"created"),D(I,u,u.scopeId,P,y),Y){for(const fe in Y)fe!=="value"&&!Qt(fe)&&o(I,fe,null,Y[fe],b,y);"value"in Y&&o(I,"value",null,Y.value,b),(E=Y.onVnodeBeforeMount)&&Je(E,y,u)}Z&&bt(u,null,y,"beforeMount");const ne=Za(C,K);ne&&K.beforeEnter(I),r(I,d,g),((E=Y&&Y.onVnodeMounted)||ne||Z)&&Me(()=>{E&&Je(E,y,u),ne&&K.enter(I),Z&&bt(u,null,y,"mounted")},C)},D=(u,d,g,y,C)=>{if(g&&m(u,g),y)for(let b=0;b<y.length;b++)m(u,y[b]);if(C){let b=C.subTree;if(d===b||ri(b.type)&&(b.ssContent===d||b.ssFallback===d)){const P=C.vnode;D(u,P,P.scopeId,P.slotScopeIds,C.parent)}}},w=(u,d,g,y,C,b,P,x,I=0)=>{for(let E=I;E<u.length;E++){const Y=u[E]=x?pt(u[E]):Xe(u[E]);O(null,Y,d,g,y,C,b,P,x)}},S=(u,d,g,y,C,b,P)=>{const x=d.el=u.el;let{patchFlag:I,dynamicChildren:E,dirs:Y}=d;I|=u.patchFlag&16;const M=u.props||ce,K=d.props||ce;let Z;if(g&&wt(g,!1),(Z=K.onVnodeBeforeUpdate)&&Je(Z,g,d,u),Y&&bt(d,u,g,"beforeUpdate"),g&&wt(g,!0),(M.innerHTML&&K.innerHTML==null||M.textContent&&K.textContent==null)&&c(x,""),E?v(u.dynamicChildren,E,x,g,y,ir(d,C),b):P||J(u,d,x,null,g,y,ir(d,C),b,!1),I>0){if(I&16)k(x,M,K,g,C);else if(I&2&&M.class!==K.class&&o(x,"class",null,K.class,C),I&4&&o(x,"style",M.style,K.style,C),I&8){const ne=d.dynamicProps;for(let fe=0;fe<ne.length;fe++){const ae=ne[fe],Oe=M[ae],Pe=K[ae];(Pe!==Oe||ae==="value")&&o(x,ae,Oe,Pe,C,g)}}I&1&&u.children!==d.children&&c(x,d.children)}else!P&&E==null&&k(x,M,K,g,C);((Z=K.onVnodeUpdated)||Y)&&Me(()=>{Z&&Je(Z,g,d,u),Y&&bt(d,u,g,"updated")},y)},v=(u,d,g,y,C,b,P)=>{for(let x=0;x<d.length;x++){const I=u[x],E=d[x],Y=I.el&&(I.type===Le||!Gt(I,E)||I.shapeFlag&198)?h(I.el):g;O(I,E,Y,null,y,C,b,P,!0)}},k=(u,d,g,y,C)=>{if(d!==g){if(d!==ce)for(const b in d)!Qt(b)&&!(b in g)&&o(u,b,d[b],null,C,y);for(const b in g){if(Qt(b))continue;const P=g[b],x=d[b];P!==x&&b!=="value"&&o(u,b,x,P,C,y)}"value"in g&&o(u,"value",d.value,g.value,C)}},X=(u,d,g,y,C,b,P,x,I)=>{const E=d.el=u?u.el:a(""),Y=d.anchor=u?u.anchor:a("");let{patchFlag:M,dynamicChildren:K,slotScopeIds:Z}=d;Z&&(x=x?x.concat(Z):Z),u==null?(r(E,g,y),r(Y,g,y),w(d.children||[],g,Y,C,b,P,x,I)):M>0&&M&64&&K&&u.dynamicChildren?(v(u.dynamicChildren,K,g,C,b,P,x),(d.key!=null||C&&d===C.subTree)&&Xo(u,d,!0)):J(u,d,g,Y,C,b,P,x,I)},A=(u,d,g,y,C,b,P,x,I)=>{d.slotScopeIds=x,u==null?d.shapeFlag&512?C.ctx.activate(d,g,y,P,I):T(d,g,y,C,b,P,I):W(u,d,I)},T=(u,d,g,y,C,b,P)=>{const x=u.component=yl(u,y,C);if(Lo(u)&&(x.ctx.renderer=U),vl(x,!1,P),x.asyncDep){if(C&&C.registerDep(x,B,P),!u.el){const I=x.subTree=me(ct);V(null,I,d,g)}}else B(x,u,d,g,C,b,P)},W=(u,d,g)=>{const y=d.component=u.component;if(ll(u,d,g))if(y.asyncDep&&!y.asyncResolved){j(y,d,g);return}else y.next=d,y.update();else d.el=u.el,y.vnode=d},B=(u,d,g,y,C,b,P)=>{const x=()=>{if(u.isMounted){let{next:M,bu:K,u:Z,parent:ne,vnode:fe}=u;{const Ge=Zo(u);if(Ge){M&&(M.el=fe.el,j(u,M,P)),Ge.asyncDep.then(()=>{u.isUnmounted||x()});return}}let ae=M,Oe;wt(u,!1),M?(M.el=fe.el,j(u,M,P)):M=fe,K&&Tn(K),(Oe=M.props&&M.props.onVnodeBeforeUpdate)&&Je(Oe,ne,M,fe),wt(u,!0);const Pe=gs(u),Ye=u.subTree;u.subTree=Pe,O(Ye,Pe,h(Ye.el),_(Ye),u,C,b),M.el=Pe.el,ae===null&&cl(u,Pe.el),Z&&Me(Z,C),(Oe=M.props&&M.props.onVnodeUpdated)&&Me(()=>Je(Oe,ne,M,fe),C)}else{let M;const{el:K,props:Z}=d,{bm:ne,m:fe,parent:ae,root:Oe,type:Pe}=u,Ye=jt(d);wt(u,!1),ne&&Tn(ne),!Ye&&(M=Z&&Z.onVnodeBeforeMount)&&Je(M,ae,d),wt(u,!0);{Oe.ce&&Oe.ce._def.shadowRoot!==!1&&Oe.ce._injectChildStyle(Pe);const Ge=u.subTree=gs(u);O(null,Ge,g,y,u,C,b),d.el=Ge.el}if(fe&&Me(fe,C),!Ye&&(M=Z&&Z.onVnodeMounted)){const Ge=d;Me(()=>Je(M,ae,Ge),C)}(d.shapeFlag&256||ae&&jt(ae.vnode)&&ae.vnode.shapeFlag&256)&&u.a&&Me(u.a,C),u.isMounted=!0,d=g=y=null}};u.scope.on();const I=u.effect=new fo(x);u.scope.off();const E=u.update=I.run.bind(I),Y=u.job=I.runIfDirty.bind(I);Y.i=u,Y.id=u.uid,I.scheduler=()=>Gr(Y),wt(u,!0),E()},j=(u,d,g)=>{d.component=u;const y=u.vnode.props;u.vnode=d,u.next=null,Ya(u,d.props,y,g),za(u,d.children,g),at(),as(u),lt()},J=(u,d,g,y,C,b,P,x,I=!1)=>{const E=u&&u.children,Y=u?u.shapeFlag:0,M=d.children,{patchFlag:K,shapeFlag:Z}=d;if(K>0){if(K&128){We(E,M,g,y,C,b,P,x,I);return}else if(K&256){ge(E,M,g,y,C,b,P,x,I);return}}Z&8?(Y&16&&Ne(E,C,b),M!==E&&c(g,M)):Y&16?Z&16?We(E,M,g,y,C,b,P,x,I):Ne(E,C,b,!0):(Y&8&&c(g,""),Z&16&&w(M,g,y,C,b,P,x,I))},ge=(u,d,g,y,C,b,P,x,I)=>{u=u||Nt,d=d||Nt;const E=u.length,Y=d.length,M=Math.min(E,Y);let K;for(K=0;K<M;K++){const Z=d[K]=I?pt(d[K]):Xe(d[K]);O(u[K],Z,g,null,C,b,P,x,I)}E>Y?Ne(u,C,b,!0,!1,M):w(d,g,y,C,b,P,x,I,M)},We=(u,d,g,y,C,b,P,x,I)=>{let E=0;const Y=d.length;let M=u.length-1,K=Y-1;for(;E<=M&&E<=K;){const Z=u[E],ne=d[E]=I?pt(d[E]):Xe(d[E]);if(Gt(Z,ne))O(Z,ne,g,null,C,b,P,x,I);else break;E++}for(;E<=M&&E<=K;){const Z=u[M],ne=d[K]=I?pt(d[K]):Xe(d[K]);if(Gt(Z,ne))O(Z,ne,g,null,C,b,P,x,I);else break;M--,K--}if(E>M){if(E<=K){const Z=K+1,ne=Z<Y?d[Z].el:y;for(;E<=K;)O(null,d[E]=I?pt(d[E]):Xe(d[E]),g,ne,C,b,P,x,I),E++}}else if(E>K)for(;E<=M;)xe(u[E],C,b,!0),E++;else{const Z=E,ne=E,fe=new Map;for(E=ne;E<=K;E++){const $e=d[E]=I?pt(d[E]):Xe(d[E]);$e.key!=null&&fe.set($e.key,E)}let ae,Oe=0;const Pe=K-ne+1;let Ye=!1,Ge=0;const Kt=new Array(Pe);for(E=0;E<Pe;E++)Kt[E]=0;for(E=Z;E<=M;E++){const $e=u[E];if(Oe>=Pe){xe($e,C,b,!0);continue}let qe;if($e.key!=null)qe=fe.get($e.key);else for(ae=ne;ae<=K;ae++)if(Kt[ae-ne]===0&&Gt($e,d[ae])){qe=ae;break}qe===void 0?xe($e,C,b,!0):(Kt[qe-ne]=E+1,qe>=Ge?Ge=qe:Ye=!0,O($e,d[qe],g,null,C,b,P,x,I),Oe++)}const ts=Ye?el(Kt):Nt;for(ae=ts.length-1,E=Pe-1;E>=0;E--){const $e=ne+E,qe=d[$e],ns=$e+1<Y?d[$e+1].el:y;Kt[E]===0?O(null,qe,g,ns,C,b,P,x,I):Ye&&(ae<0||E!==ts[ae]?Ke(qe,g,ns,2):ae--)}}},Ke=(u,d,g,y,C=null)=>{const{el:b,type:P,transition:x,children:I,shapeFlag:E}=u;if(E&6){Ke(u.component.subTree,d,g,y);return}if(E&128){u.suspense.move(d,g,y);return}if(E&64){P.move(u,d,g,U);return}if(P===Le){r(b,d,g);for(let M=0;M<I.length;M++)Ke(I[M],d,g,y);r(u.anchor,d,g);return}if(P===Rn){H(u,d,g);return}if(y!==2&&E&1&&x)if(y===0)x.beforeEnter(b),r(b,d,g),Me(()=>x.enter(b),C);else{const{leave:M,delayLeave:K,afterLeave:Z}=x,ne=()=>{u.ctx.isUnmounted?s(b):r(b,d,g)},fe=()=>{M(b,()=>{ne(),Z&&Z()})};K?K(b,ne,fe):fe()}else r(b,d,g)},xe=(u,d,g,y=!1,C=!1)=>{const{type:b,props:P,ref:x,children:I,dynamicChildren:E,shapeFlag:Y,patchFlag:M,dirs:K,cacheIndex:Z}=u;if(M===-2&&(C=!1),x!=null&&(at(),en(x,null,g,u,!0),lt()),Z!=null&&(d.renderCache[Z]=void 0),Y&256){d.ctx.deactivate(u);return}const ne=Y&1&&K,fe=!jt(u);let ae;if(fe&&(ae=P&&P.onVnodeBeforeUnmount)&&Je(ae,d,u),Y&6)wn(u.component,g,y);else{if(Y&128){u.suspense.unmount(g,y);return}ne&&bt(u,null,d,"beforeUnmount"),Y&64?u.type.remove(u,d,g,U,y):E&&!E.hasOnce&&(b!==Le||M>0&&M&64)?Ne(E,d,g,!1,!0):(b===Le&&M&384||!C&&Y&16)&&Ne(I,d,g),y&&xt(u)}(fe&&(ae=P&&P.onVnodeUnmounted)||ne)&&Me(()=>{ae&&Je(ae,d,u),ne&&bt(u,null,d,"unmounted")},g)},xt=u=>{const{type:d,el:g,anchor:y,transition:C}=u;if(d===Le){Ot(g,y);return}if(d===Rn){N(u);return}const b=()=>{s(g),C&&!C.persisted&&C.afterLeave&&C.afterLeave()};if(u.shapeFlag&1&&C&&!C.persisted){const{leave:P,delayLeave:x}=C,I=()=>P(g,b);x?x(u.el,b,I):I()}else b()},Ot=(u,d)=>{let g;for(;u!==d;)g=p(u),s(u),u=g;s(d)},wn=(u,d,g)=>{const{bum:y,scope:C,job:b,subTree:P,um:x,m:I,a:E,parent:Y,slots:{__:M}}=u;ps(I),ps(E),y&&Tn(y),Y&&G(M)&&M.forEach(K=>{Y.renderCache[K]=void 0}),C.stop(),b&&(b.flags|=8,xe(P,u,d,g)),x&&Me(x,d),Me(()=>{u.isUnmounted=!0},d),d&&d.pendingBranch&&!d.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===d.pendingId&&(d.deps--,d.deps===0&&d.resolve())},Ne=(u,d,g,y=!1,C=!1,b=0)=>{for(let P=b;P<u.length;P++)xe(u[P],d,g,y,C)},_=u=>{if(u.shapeFlag&6)return _(u.component.subTree);if(u.shapeFlag&128)return u.suspense.next();const d=p(u.anchor||u.el),g=d&&d[ba];return g?p(g):d};let L=!1;const $=(u,d,g)=>{u==null?d._vnode&&xe(d._vnode,null,null,!0):O(d._vnode||null,u,d,null,null,null,g),d._vnode=u,L||(L=!0,as(),$o(),L=!1)},U={p:O,um:xe,m:Ke,r:xt,mt:T,mc:w,pc:J,pbc:v,n:_,o:e};return{render:$,hydrate:void 0,createApp:Ua($)}}function ir({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function wt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Za(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Xo(e,t,n=!1){const r=e.children,s=t.children;if(G(r)&&G(s))for(let o=0;o<r.length;o++){const i=r[o];let a=s[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=s[o]=pt(s[o]),a.el=i.el),!n&&a.patchFlag!==-2&&Xo(i,a)),a.type===qn&&(a.el=i.el),a.type===ct&&!a.el&&(a.el=i.el)}}function el(e){const t=e.slice(),n=[0];let r,s,o,i,a;const l=e.length;for(r=0;r<l;r++){const f=e[r];if(f!==0){if(s=n[n.length-1],e[s]<f){t[r]=s,n.push(r);continue}for(o=0,i=n.length-1;o<i;)a=o+i>>1,e[n[a]]<f?o=a+1:i=a;f<e[n[o]]&&(o>0&&(t[r]=n[o-1]),n[o]=r)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Zo(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Zo(t)}function ps(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const tl=Symbol.for("v-scx"),nl=()=>ke(tl);function nn(e,t,n){return ei(e,t,n)}function ei(e,t,n=ce){const{immediate:r,deep:s,flush:o,once:i}=n,a=Ae({},n),l=t&&r||!t&&o!=="post";let f;if(pn){if(o==="sync"){const m=nl();f=m.__watcherHandles||(m.__watcherHandles=[])}else if(!l){const m=()=>{};return m.stop=Ze,m.resume=Ze,m.pause=Ze,m}}const c=Ce;a.call=(m,R,O)=>et(m,c,R,O);let h=!1;o==="post"?a.scheduler=m=>{Me(m,c&&c.suspense)}:o!=="sync"&&(h=!0,a.scheduler=(m,R)=>{R?m():Gr(m)}),a.augmentJob=m=>{t&&(m.flags|=4),h&&(m.flags|=2,c&&(m.id=c.uid,m.i=c))};const p=ma(e,t,a);return pn&&(f?f.push(p):l&&p()),p}function rl(e,t,n){const r=this.proxy,s=_e(e)?e.includes(".")?ti(r,e):()=>r[e]:e.bind(r,r);let o;ee(t)?o=t:(o=t.handler,n=t);const i=bn(this),a=ei(s,o.bind(r),n);return i(),a}function ti(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}const sl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${He(t)}Modifiers`]||e[`${It(t)}Modifiers`];function ol(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||ce;let s=n;const o=t.startsWith("update:"),i=o&&sl(r,t.slice(7));i&&(i.trim&&(s=n.map(c=>_e(c)?c.trim():c)),i.number&&(s=n.map(On)));let a,l=r[a=er(t)]||r[a=er(He(t))];!l&&o&&(l=r[a=er(It(t))]),l&&et(l,e,6,s);const f=r[a+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,et(f,e,6,s)}}function ni(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const o=e.emits;let i={},a=!1;if(!ee(e)){const l=f=>{const c=ni(f,t,!0);c&&(a=!0,Ae(i,c))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!o&&!a?(he(e)&&r.set(e,null),null):(G(o)?o.forEach(l=>i[l]=null):Ae(i,o),he(e)&&r.set(e,i),i)}function Gn(e,t){return!e||!kn(t)?!1:(t=t.slice(2).replace(/Once$/,""),ie(e,t[0].toLowerCase()+t.slice(1))||ie(e,It(t))||ie(e,t))}function gs(e){const{type:t,vnode:n,proxy:r,withProxy:s,propsOptions:[o],slots:i,attrs:a,emit:l,render:f,renderCache:c,props:h,data:p,setupState:m,ctx:R,inheritAttrs:O}=e,q=Nn(e);let V,F;try{if(n.shapeFlag&4){const N=s||r,Q=N;V=Xe(f.call(Q,N,c,h,m,p,R)),F=a}else{const N=t;V=Xe(N.length>1?N(h,{attrs:a,slots:i,emit:l}):N(h,null)),F=t.props?a:il(a)}}catch(N){rn.length=0,Un(N,e,1),V=me(ct)}let H=V;if(F&&O!==!1){const N=Object.keys(F),{shapeFlag:Q}=H;N.length&&Q&7&&(o&&N.some(Mr)&&(F=al(F,o)),H=Ht(H,F,!1,!0))}return n.dirs&&(H=Ht(H,null,!1,!0),H.dirs=H.dirs?H.dirs.concat(n.dirs):n.dirs),n.transition&&qr(H,n.transition),V=H,Nn(q),V}const il=e=>{let t;for(const n in e)(n==="class"||n==="style"||kn(n))&&((t||(t={}))[n]=e[n]);return t},al=(e,t)=>{const n={};for(const r in e)(!Mr(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function ll(e,t,n){const{props:r,children:s,component:o}=e,{props:i,children:a,patchFlag:l}=t,f=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return r?ms(r,i,f):!!i;if(l&8){const c=t.dynamicProps;for(let h=0;h<c.length;h++){const p=c[h];if(i[p]!==r[p]&&!Gn(f,p))return!0}}}else return(s||a)&&(!a||!a.$stable)?!0:r===i?!1:r?i?ms(r,i,f):!0:!!i;return!1}function ms(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const o=r[s];if(t[o]!==e[o]&&!Gn(n,o))return!0}return!1}function cl({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const ri=e=>e.__isSuspense;function ul(e,t){t&&t.pendingBranch?G(e)?t.effects.push(...e):t.effects.push(e):_a(e)}const Le=Symbol.for("v-fgt"),qn=Symbol.for("v-txt"),ct=Symbol.for("v-cmt"),Rn=Symbol.for("v-stc"),rn=[];let De=null;function fn(e=!1){rn.push(De=e?null:[])}function fl(){rn.pop(),De=rn[rn.length-1]||null}let dn=1;function ys(e,t=!1){dn+=e,e<0&&De&&t&&(De.hasOnce=!0)}function si(e){return e.dynamicChildren=dn>0?De||Nt:null,fl(),dn>0&&De&&De.push(e),e}function oi(e,t,n,r,s,o){return si(pe(e,t,n,r,s,o,!0))}function Cr(e,t,n,r,s){return si(me(e,t,n,r,s,!0))}function hn(e){return e?e.__v_isVNode===!0:!1}function Gt(e,t){return e.type===t.type&&e.key===t.key}const ii=({key:e})=>e??null,In=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?_e(e)||ve(e)||ee(e)?{i:we,r:e,k:t,f:!!n}:e:null);function pe(e,t=null,n=null,r=0,s=null,o=e===Le?0:1,i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&ii(t),ref:t&&In(t),scopeId:Do,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:we};return a?(Qr(l,n),o&128&&e.normalize(l)):n&&(l.shapeFlag|=_e(n)?8:16),dn>0&&!i&&De&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&De.push(l),l}const me=dl;function dl(e,t=null,n=null,r=0,s=null,o=!1){if((!e||e===Da)&&(e=ct),hn(e)){const a=Ht(e,t,!0);return n&&Qr(a,n),dn>0&&!o&&De&&(a.shapeFlag&6?De[De.indexOf(e)]=a:De.push(a)),a.patchFlag=-2,a}if(Cl(e)&&(e=e.__vccOpts),t){t=hl(t);let{class:a,style:l}=t;a&&!_e(a)&&(t.class=Fr(a)),he(l)&&(Kr(l)&&!G(l)&&(l=Ae({},l)),t.style=Lr(l))}const i=_e(e)?1:ri(e)?128:wa(e)?64:he(e)?4:ee(e)?2:0;return pe(e,t,n,r,s,i,o,!0)}function hl(e){return e?Kr(e)||Yo(e)?Ae({},e):e:null}function Ht(e,t,n=!1,r=!1){const{props:s,ref:o,patchFlag:i,children:a,transition:l}=e,f=t?pl(s||{},t):s,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&ii(f),ref:t&&t.ref?n&&o?G(o)?o.concat(In(t)):[o,In(t)]:In(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Le?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ht(e.ssContent),ssFallback:e.ssFallback&&Ht(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&r&&qr(c,l.clone(c)),c}function Et(e=" ",t=0){return me(qn,null,e,t)}function vs(e,t){const n=me(Rn,null,e);return n.staticCount=t,n}function Gu(e="",t=!1){return t?(fn(),Cr(ct,null,e)):me(ct,null,e)}function Xe(e){return e==null||typeof e=="boolean"?me(ct):G(e)?me(Le,null,e.slice()):hn(e)?pt(e):me(qn,null,String(e))}function pt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ht(e)}function Qr(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(G(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),Qr(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!Yo(t)?t._ctx=we:s===3&&we&&(we.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else ee(t)?(t={default:t,_ctx:we},n=32):(t=String(t),r&64?(n=16,t=[Et(t)]):n=8);e.children=t,e.shapeFlag|=n}function pl(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=Fr([t.class,r.class]));else if(s==="style")t.style=Lr([t.style,r.style]);else if(kn(s)){const o=t[s],i=r[s];i&&o!==i&&!(G(o)&&o.includes(i))&&(t[s]=o?[].concat(o,i):i)}else s!==""&&(t[s]=r[s])}return t}function Je(e,t,n,r=null){et(e,t,7,[n,r])}const gl=Uo();let ml=0;function yl(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||gl,o={uid:ml++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new lo(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:qo(r,s),emitsOptions:ni(r,s),emit:null,emitted:null,propsDefaults:ce,inheritAttrs:r.inheritAttrs,ctx:ce,data:ce,props:ce,attrs:ce,slots:ce,refs:ce,setupState:ce,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=ol.bind(null,o),e.ce&&e.ce(o),o}let Ce=null,Fn,Er;{const e=Vn(),t=(n,r)=>{let s;return(s=e[n])||(s=e[n]=[]),s.push(r),o=>{s.length>1?s.forEach(i=>i(o)):s[0](o)}};Fn=t("__VUE_INSTANCE_SETTERS__",n=>Ce=n),Er=t("__VUE_SSR_SETTERS__",n=>pn=n)}const bn=e=>{const t=Ce;return Fn(e),e.scope.on(),()=>{e.scope.off(),Fn(t)}},_s=()=>{Ce&&Ce.scope.off(),Fn(null)};function ai(e){return e.vnode.shapeFlag&4}let pn=!1;function vl(e,t=!1,n=!1){t&&Er(t);const{props:r,children:s}=e.vnode,o=ai(e);Ka(e,r,o,t),Ja(e,s,n||t);const i=o?_l(e,t):void 0;return t&&Er(!1),i}function _l(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,La);const{setup:r}=n;if(r){at();const s=e.setupContext=r.length>1?wl(e):null,o=bn(e),i=_n(r,e,0,[e.props,s]),a=no(i);if(lt(),o(),(a||e.sp)&&!jt(e)&&No(e),a){if(i.then(_s,_s),t)return i.then(l=>{bs(e,l)}).catch(l=>{Un(l,e,0)});e.asyncDep=i}else bs(e,i)}else li(e)}function bs(e,t,n){ee(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:he(t)&&(e.setupState=xo(t)),li(e)}function li(e,t,n){const r=e.type;e.render||(e.render=r.render||Ze);{const s=bn(e);at();try{Fa(e)}finally{lt(),s()}}}const bl={get(e,t){return Te(e,"get",""),e[t]}};function wl(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,bl),slots:e.slots,emit:e.emit,expose:t}}function Jn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(xo(Yr(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in tn)return tn[n](e)},has(t,n){return n in t||n in tn}})):e.proxy}function Sl(e,t=!0){return ee(e)?e.displayName||e.name:e.name||t&&e.__name}function Cl(e){return ee(e)&&"__vccOpts"in e}const te=(e,t)=>pa(e,t,pn);function ci(e,t,n){const r=arguments.length;return r===2?he(t)&&!G(t)?hn(t)?me(e,null,[t]):me(e,t):me(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&hn(n)&&(n=[n]),me(e,t,n))}const El="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Tr;const ws=typeof window<"u"&&window.trustedTypes;if(ws)try{Tr=ws.createPolicy("vue",{createHTML:e=>e})}catch{}const ui=Tr?e=>Tr.createHTML(e):e=>e,Tl="http://www.w3.org/2000/svg",Al="http://www.w3.org/1998/Math/MathML",rt=typeof document<"u"?document:null,Ss=rt&&rt.createElement("template"),Rl={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t==="svg"?rt.createElementNS(Tl,e):t==="mathml"?rt.createElementNS(Al,e):n?rt.createElement(e,{is:n}):rt.createElement(e);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>rt.createTextNode(e),createComment:e=>rt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>rt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,o){const i=n?n.previousSibling:t.lastChild;if(s&&(s===o||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===o||!(s=s.nextSibling)););else{Ss.innerHTML=ui(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const a=Ss.content;if(r==="svg"||r==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Il=Symbol("_vtc");function xl(e,t,n){const r=e[Il];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Cs=Symbol("_vod"),Ol=Symbol("_vsh"),Pl=Symbol(""),$l=/(^|;)\s*display\s*:/;function Ml(e,t,n){const r=e.style,s=_e(n);let o=!1;if(n&&!s){if(t)if(_e(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();n[a]==null&&xn(r,a,"")}else for(const i in t)n[i]==null&&xn(r,i,"");for(const i in n)i==="display"&&(o=!0),xn(r,i,n[i])}else if(s){if(t!==n){const i=r[Pl];i&&(n+=";"+i),r.cssText=n,o=$l.test(n)}}else t&&e.removeAttribute("style");Cs in e&&(e[Cs]=o?r.display:"",e[Ol]&&(r.display="none"))}const Es=/\s*!important$/;function xn(e,t,n){if(G(n))n.forEach(r=>xn(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=Dl(e,t);Es.test(n)?e.setProperty(It(r),n.replace(Es,""),"important"):e[r]=n}}const Ts=["Webkit","Moz","ms"],ar={};function Dl(e,t){const n=ar[t];if(n)return n;let r=He(t);if(r!=="filter"&&r in e)return ar[t]=r;r=Hn(r);for(let s=0;s<Ts.length;s++){const o=Ts[s]+r;if(o in e)return ar[t]=o}return t}const As="http://www.w3.org/1999/xlink";function Rs(e,t,n,r,s,o=ji(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(As,t.slice(6,t.length)):e.setAttributeNS(As,t,n):n==null||o&&!oo(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Be(n)?String(n):n)}function Is(e,t,n,r,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?ui(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=oo(n):n==null&&a==="string"?(n="",i=!0):a==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(s||t)}function it(e,t,n,r){e.addEventListener(t,n,r)}function Nl(e,t,n,r){e.removeEventListener(t,n,r)}const xs=Symbol("_vei");function Ll(e,t,n,r,s=null){const o=e[xs]||(e[xs]={}),i=o[t];if(r&&i)i.value=r;else{const[a,l]=Fl(t);if(r){const f=o[t]=Hl(r,s);it(e,a,f,l)}else i&&(Nl(e,a,i,l),o[t]=void 0)}}const Os=/(?:Once|Passive|Capture)$/;function Fl(e){let t;if(Os.test(e)){t={};let r;for(;r=e.match(Os);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):It(e.slice(2)),t]}let lr=0;const kl=Promise.resolve(),jl=()=>lr||(kl.then(()=>lr=0),lr=Date.now());function Hl(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;et(Vl(r,n.value),t,5,[r])};return n.value=e,n.attached=jl(),n}function Vl(e,t){if(G(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const Ps=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Bl=(e,t,n,r,s,o)=>{const i=s==="svg";t==="class"?xl(e,r,i):t==="style"?Ml(e,n,r):kn(t)?Mr(t)||Ll(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Ul(e,t,r,i))?(Is(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Rs(e,t,r,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!_e(r))?Is(e,He(t),r,o,t):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Rs(e,t,r,i))};function Ul(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ps(t)&&ee(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const s=e.tagName;if(s==="IMG"||s==="VIDEO"||s==="CANVAS"||s==="SOURCE")return!1}return Ps(t)&&_e(n)?!1:t in e}const _t=e=>{const t=e.props["onUpdate:modelValue"]||!1;return G(t)?n=>Tn(t,n):t};function Wl(e){e.target.composing=!0}function $s(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const je=Symbol("_assign"),qu={created(e,{modifiers:{lazy:t,trim:n,number:r}},s){e[je]=_t(s);const o=r||s.props&&s.props.type==="number";it(e,t?"change":"input",i=>{if(i.target.composing)return;let a=e.value;n&&(a=a.trim()),o&&(a=On(a)),e[je](a)}),n&&it(e,"change",()=>{e.value=e.value.trim()}),t||(it(e,"compositionstart",Wl),it(e,"compositionend",$s),it(e,"change",$s))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:r,trim:s,number:o}},i){if(e[je]=_t(i),e.composing)return;const a=(o||e.type==="number")&&!/^0\d/.test(e.value)?On(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(r&&t===n||s&&e.value.trim()===l)||(e.value=l))}},Ju={deep:!0,created(e,t,n){e[je]=_t(n),it(e,"change",()=>{const r=e._modelValue,s=Vt(e),o=e.checked,i=e[je];if(G(r)){const a=kr(r,s),l=a!==-1;if(o&&!l)i(r.concat(s));else if(!o&&l){const f=[...r];f.splice(a,1),i(f)}}else if(Wt(r)){const a=new Set(r);o?a.add(s):a.delete(s),i(a)}else i(fi(e,o))})},mounted:Ms,beforeUpdate(e,t,n){e[je]=_t(n),Ms(e,t,n)}};function Ms(e,{value:t,oldValue:n},r){e._modelValue=t;let s;if(G(t))s=kr(t,r.props.value)>-1;else if(Wt(t))s=t.has(r.props.value);else{if(t===n)return;s=Rt(t,fi(e,!0))}e.checked!==s&&(e.checked=s)}const zu={created(e,{value:t},n){e.checked=Rt(t,n.props.value),e[je]=_t(n),it(e,"change",()=>{e[je](Vt(e))})},beforeUpdate(e,{value:t,oldValue:n},r){e[je]=_t(r),t!==n&&(e.checked=Rt(t,r.props.value))}},Qu={deep:!0,created(e,{value:t,modifiers:{number:n}},r){const s=Wt(t);it(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?On(Vt(i)):Vt(i));e[je](e.multiple?s?new Set(o):o:o[0]),e._assigning=!0,Wn(()=>{e._assigning=!1})}),e[je]=_t(r)},mounted(e,{value:t}){Ds(e,t)},beforeUpdate(e,t,n){e[je]=_t(n)},updated(e,{value:t}){e._assigning||Ds(e,t)}};function Ds(e,t){const n=e.multiple,r=G(t);if(!(n&&!r&&!Wt(t))){for(let s=0,o=e.options.length;s<o;s++){const i=e.options[s],a=Vt(i);if(n)if(r){const l=typeof a;l==="string"||l==="number"?i.selected=t.some(f=>String(f)===String(a)):i.selected=kr(t,a)>-1}else i.selected=t.has(a);else if(Rt(Vt(i),t)){e.selectedIndex!==s&&(e.selectedIndex=s);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Vt(e){return"_value"in e?e._value:e.value}function fi(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Kl=["ctrl","shift","alt","meta"],Yl={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Kl.some(n=>e[`${n}Key`]&&!t.includes(n))},Xu=(e,t)=>{const n=e._withMods||(e._withMods={}),r=t.join(".");return n[r]||(n[r]=(s,...o)=>{for(let i=0;i<t.length;i++){const a=Yl[t[i]];if(a&&a(s,t))return}return e(s,...o)})},Gl=Ae({patchProp:Bl},Rl);let Ns;function ql(){return Ns||(Ns=Qa(Gl))}const Jl=(...e)=>{const t=ql().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=Ql(r);if(!s)return;const o=t._component;!ee(o)&&!o.render&&!o.template&&(o.template=s.innerHTML),s.nodeType===1&&(s.textContent="");const i=n(s,!1,zl(s));return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),i},t};function zl(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Ql(e){return _e(e)?document.querySelector(e):e}const Xl={id:"app"},Zl={class:"app-nav"},ec={class:"app-nav__content"},tc={class:"app-nav__links"},nc={class:"app-main"},rc=Kn({__name:"App",setup(e){return(t,n)=>{const r=vr("router-link"),s=vr("router-view");return fn(),oi("div",Xl,[pe("nav",Zl,[pe("div",ec,[me(r,{to:"/",class:"app-nav__logo"},{default:Ct(()=>n[0]||(n[0]=[Et(" 🎲 AI Story Builder ")])),_:1,__:[0]}),pe("div",tc,[me(r,{to:"/",class:"app-nav__link"},{default:Ct(()=>n[1]||(n[1]=[Et("Home")])),_:1,__:[1]}),me(r,{to:"/generator",class:"app-nav__link"},{default:Ct(()=>n[2]||(n[2]=[Et("Generator")])),_:1,__:[2]}),me(r,{to:"/library",class:"app-nav__link"},{default:Ct(()=>n[3]||(n[3]=[Et("Library")])),_:1,__:[3]})])])]),pe("main",nc,[me(s)])])}}}),sc="modulepreload",oc=function(e){return"/"+e},Ls={},cr=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){let l=function(f){return Promise.all(f.map(c=>Promise.resolve(c).then(h=>({status:"fulfilled",value:h}),h=>({status:"rejected",reason:h}))))};document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),a=i?.nonce||i?.getAttribute("nonce");s=l(n.map(f=>{if(f=oc(f),f in Ls)return;Ls[f]=!0;const c=f.endsWith(".css"),h=c?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${f}"]${h}`))return;const p=document.createElement("link");if(p.rel=c?"stylesheet":sc,c||(p.as="script"),p.crossOrigin="",p.href=f,a&&p.setAttribute("nonce",a),document.head.appendChild(p),c)return new Promise((m,R)=>{p.addEventListener("load",m),p.addEventListener("error",()=>R(new Error(`Unable to preload CSS for ${f}`)))})}))}function o(i){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=i,window.dispatchEvent(a),!a.defaultPrevented)throw i}return s.then(i=>{for(const a of i||[])a.status==="rejected"&&o(a.reason);return t().catch(o)})};/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Dt=typeof document<"u";function di(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function ic(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&di(e.default)}const oe=Object.assign;function ur(e,t){const n={};for(const r in t){const s=t[r];n[r]=Ue(s)?s.map(e):e(s)}return n}const sn=()=>{},Ue=Array.isArray,hi=/#/g,ac=/&/g,lc=/\//g,cc=/=/g,uc=/\?/g,pi=/\+/g,fc=/%5B/g,dc=/%5D/g,gi=/%5E/g,hc=/%60/g,mi=/%7B/g,pc=/%7C/g,yi=/%7D/g,gc=/%20/g;function Xr(e){return encodeURI(""+e).replace(pc,"|").replace(fc,"[").replace(dc,"]")}function mc(e){return Xr(e).replace(mi,"{").replace(yi,"}").replace(gi,"^")}function Ar(e){return Xr(e).replace(pi,"%2B").replace(gc,"+").replace(hi,"%23").replace(ac,"%26").replace(hc,"`").replace(mi,"{").replace(yi,"}").replace(gi,"^")}function yc(e){return Ar(e).replace(cc,"%3D")}function vc(e){return Xr(e).replace(hi,"%23").replace(uc,"%3F")}function _c(e){return e==null?"":vc(e).replace(lc,"%2F")}function gn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const bc=/\/$/,wc=e=>e.replace(bc,"");function fr(e,t,n="/"){let r,s={},o="",i="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(r=t.slice(0,l),o=t.slice(l+1,a>-1?a:t.length),s=e(o)),a>-1&&(r=r||t.slice(0,a),i=t.slice(a,t.length)),r=Tc(r??t,n),{fullPath:r+(o&&"?")+o+i,path:r,query:s,hash:gn(i)}}function Sc(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Fs(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Cc(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&Bt(t.matched[r],n.matched[s])&&vi(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Bt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function vi(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Ec(e[n],t[n]))return!1;return!0}function Ec(e,t){return Ue(e)?ks(e,t):Ue(t)?ks(t,e):e===t}function ks(e,t){return Ue(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function Tc(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let o=n.length-1,i,a;for(i=0;i<r.length;i++)if(a=r[i],a!==".")if(a==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+r.slice(i).join("/")}const ft={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var mn;(function(e){e.pop="pop",e.push="push"})(mn||(mn={}));var on;(function(e){e.back="back",e.forward="forward",e.unknown=""})(on||(on={}));function Ac(e){if(!e)if(Dt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),wc(e)}const Rc=/^[^#]+#/;function Ic(e,t){return e.replace(Rc,"#")+t}function xc(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const zn=()=>({left:window.scrollX,top:window.scrollY});function Oc(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=xc(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function js(e,t){return(history.state?history.state.position-t:-1)+e}const Rr=new Map;function Pc(e,t){Rr.set(e,t)}function $c(e){const t=Rr.get(e);return Rr.delete(e),t}let Mc=()=>location.protocol+"//"+location.host;function _i(e,t){const{pathname:n,search:r,hash:s}=t,o=e.indexOf("#");if(o>-1){let a=s.includes(e.slice(o))?e.slice(o).length:1,l=s.slice(a);return l[0]!=="/"&&(l="/"+l),Fs(l,"")}return Fs(n,e)+r+s}function Dc(e,t,n,r){let s=[],o=[],i=null;const a=({state:p})=>{const m=_i(e,location),R=n.value,O=t.value;let q=0;if(p){if(n.value=m,t.value=p,i&&i===R){i=null;return}q=O?p.position-O.position:0}else r(m);s.forEach(V=>{V(n.value,R,{delta:q,type:mn.pop,direction:q?q>0?on.forward:on.back:on.unknown})})};function l(){i=n.value}function f(p){s.push(p);const m=()=>{const R=s.indexOf(p);R>-1&&s.splice(R,1)};return o.push(m),m}function c(){const{history:p}=window;p.state&&p.replaceState(oe({},p.state,{scroll:zn()}),"")}function h(){for(const p of o)p();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:l,listen:f,destroy:h}}function Hs(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?zn():null}}function Nc(e){const{history:t,location:n}=window,r={value:_i(e,n)},s={value:t.state};s.value||o(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(l,f,c){const h=e.indexOf("#"),p=h>-1?(n.host&&document.querySelector("base")?e:e.slice(h))+l:Mc()+e+l;try{t[c?"replaceState":"pushState"](f,"",p),s.value=f}catch(m){console.error(m),n[c?"replace":"assign"](p)}}function i(l,f){const c=oe({},t.state,Hs(s.value.back,l,s.value.forward,!0),f,{position:s.value.position});o(l,c,!0),r.value=l}function a(l,f){const c=oe({},s.value,t.state,{forward:l,scroll:zn()});o(c.current,c,!0);const h=oe({},Hs(r.value,l,null),{position:c.position+1},f);o(l,h,!1),r.value=l}return{location:r,state:s,push:a,replace:i}}function Lc(e){e=Ac(e);const t=Nc(e),n=Dc(e,t.state,t.location,t.replace);function r(o,i=!0){i||n.pauseListeners(),history.go(o)}const s=oe({location:"",base:e,go:r,createHref:Ic.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function Fc(e){return typeof e=="string"||e&&typeof e=="object"}function bi(e){return typeof e=="string"||typeof e=="symbol"}const wi=Symbol("");var Vs;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Vs||(Vs={}));function Ut(e,t){return oe(new Error,{type:e,[wi]:!0},t)}function nt(e,t){return e instanceof Error&&wi in e&&(t==null||!!(e.type&t))}const Bs="[^/]+?",kc={sensitive:!1,strict:!1,start:!0,end:!0},jc=/[.+*?^${}()[\]/\\]/g;function Hc(e,t){const n=oe({},kc,t),r=[];let s=n.start?"^":"";const o=[];for(const f of e){const c=f.length?[]:[90];n.strict&&!f.length&&(s+="/");for(let h=0;h<f.length;h++){const p=f[h];let m=40+(n.sensitive?.25:0);if(p.type===0)h||(s+="/"),s+=p.value.replace(jc,"\\$&"),m+=40;else if(p.type===1){const{value:R,repeatable:O,optional:q,regexp:V}=p;o.push({name:R,repeatable:O,optional:q});const F=V||Bs;if(F!==Bs){m+=10;try{new RegExp(`(${F})`)}catch(N){throw new Error(`Invalid custom RegExp for param "${R}" (${F}): `+N.message)}}let H=O?`((?:${F})(?:/(?:${F}))*)`:`(${F})`;h||(H=q&&f.length<2?`(?:/${H})`:"/"+H),q&&(H+="?"),s+=H,m+=20,q&&(m+=-8),O&&(m+=-20),F===".*"&&(m+=-50)}c.push(m)}r.push(c)}if(n.strict&&n.end){const f=r.length-1;r[f][r[f].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&!s.endsWith("/")&&(s+="(?:/|$)");const i=new RegExp(s,n.sensitive?"":"i");function a(f){const c=f.match(i),h={};if(!c)return null;for(let p=1;p<c.length;p++){const m=c[p]||"",R=o[p-1];h[R.name]=m&&R.repeatable?m.split("/"):m}return h}function l(f){let c="",h=!1;for(const p of e){(!h||!c.endsWith("/"))&&(c+="/"),h=!1;for(const m of p)if(m.type===0)c+=m.value;else if(m.type===1){const{value:R,repeatable:O,optional:q}=m,V=R in f?f[R]:"";if(Ue(V)&&!O)throw new Error(`Provided param "${R}" is an array but it is not repeatable (* or + modifiers)`);const F=Ue(V)?V.join("/"):V;if(!F)if(q)p.length<2&&(c.endsWith("/")?c=c.slice(0,-1):h=!0);else throw new Error(`Missing required param "${R}"`);c+=F}}return c||"/"}return{re:i,score:r,keys:o,parse:a,stringify:l}}function Vc(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Si(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const o=Vc(r[n],s[n]);if(o)return o;n++}if(Math.abs(s.length-r.length)===1){if(Us(r))return 1;if(Us(s))return-1}return s.length-r.length}function Us(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Bc={type:0,value:""},Uc=/[a-zA-Z0-9_]/;function Wc(e){if(!e)return[[]];if(e==="/")return[[Bc]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${f}": ${m}`)}let n=0,r=n;const s=[];let o;function i(){o&&s.push(o),o=[]}let a=0,l,f="",c="";function h(){f&&(n===0?o.push({type:0,value:f}):n===1||n===2||n===3?(o.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:f,regexp:c,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),f="")}function p(){f+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:l==="/"?(f&&h(),i()):l===":"?(h(),n=1):p();break;case 4:p(),n=r;break;case 1:l==="("?n=2:Uc.test(l)?p():(h(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+l:n=3:c+=l;break;case 3:h(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${f}"`),h(),i(),s}function Kc(e,t,n){const r=Hc(Wc(e.path),n),s=oe(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function Yc(e,t){const n=[],r=new Map;t=Gs({strict:!1,end:!0,sensitive:!1},t);function s(h){return r.get(h)}function o(h,p,m){const R=!m,O=Ks(h);O.aliasOf=m&&m.record;const q=Gs(t,h),V=[O];if("alias"in h){const N=typeof h.alias=="string"?[h.alias]:h.alias;for(const Q of N)V.push(Ks(oe({},O,{components:m?m.record.components:O.components,path:Q,aliasOf:m?m.record:O})))}let F,H;for(const N of V){const{path:Q}=N;if(p&&Q[0]!=="/"){const ue=p.record.path,D=ue[ue.length-1]==="/"?"":"/";N.path=p.record.path+(Q&&D+Q)}if(F=Kc(N,p,q),m?m.alias.push(F):(H=H||F,H!==F&&H.alias.push(F),R&&h.name&&!Ys(F)&&i(h.name)),Ci(F)&&l(F),O.children){const ue=O.children;for(let D=0;D<ue.length;D++)o(ue[D],F,m&&m.children[D])}m=m||F}return H?()=>{i(H)}:sn}function i(h){if(bi(h)){const p=r.get(h);p&&(r.delete(h),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(h);p>-1&&(n.splice(p,1),h.record.name&&r.delete(h.record.name),h.children.forEach(i),h.alias.forEach(i))}}function a(){return n}function l(h){const p=Jc(h,n);n.splice(p,0,h),h.record.name&&!Ys(h)&&r.set(h.record.name,h)}function f(h,p){let m,R={},O,q;if("name"in h&&h.name){if(m=r.get(h.name),!m)throw Ut(1,{location:h});q=m.record.name,R=oe(Ws(p.params,m.keys.filter(H=>!H.optional).concat(m.parent?m.parent.keys.filter(H=>H.optional):[]).map(H=>H.name)),h.params&&Ws(h.params,m.keys.map(H=>H.name))),O=m.stringify(R)}else if(h.path!=null)O=h.path,m=n.find(H=>H.re.test(O)),m&&(R=m.parse(O),q=m.record.name);else{if(m=p.name?r.get(p.name):n.find(H=>H.re.test(p.path)),!m)throw Ut(1,{location:h,currentLocation:p});q=m.record.name,R=oe({},p.params,h.params),O=m.stringify(R)}const V=[];let F=m;for(;F;)V.unshift(F.record),F=F.parent;return{name:q,path:O,params:R,matched:V,meta:qc(V)}}e.forEach(h=>o(h));function c(){n.length=0,r.clear()}return{addRoute:o,resolve:f,removeRoute:i,clearRoutes:c,getRoutes:a,getRecordMatcher:s}}function Ws(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function Ks(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Gc(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Gc(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Ys(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function qc(e){return e.reduce((t,n)=>oe(t,n.meta),{})}function Gs(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Jc(e,t){let n=0,r=t.length;for(;n!==r;){const o=n+r>>1;Si(e,t[o])<0?r=o:n=o+1}const s=zc(e);return s&&(r=t.lastIndexOf(s,r-1)),r}function zc(e){let t=e;for(;t=t.parent;)if(Ci(t)&&Si(e,t)===0)return t}function Ci({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Qc(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const o=r[s].replace(pi," "),i=o.indexOf("="),a=gn(i<0?o:o.slice(0,i)),l=i<0?null:gn(o.slice(i+1));if(a in t){let f=t[a];Ue(f)||(f=t[a]=[f]),f.push(l)}else t[a]=l}return t}function qs(e){let t="";for(let n in e){const r=e[n];if(n=yc(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(Ue(r)?r.map(o=>o&&Ar(o)):[r&&Ar(r)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Xc(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=Ue(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const Zc=Symbol(""),Js=Symbol(""),Qn=Symbol(""),Zr=Symbol(""),Ir=Symbol("");function qt(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function gt(e,t,n,r,s,o=i=>i()){const i=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((a,l)=>{const f=p=>{p===!1?l(Ut(4,{from:n,to:t})):p instanceof Error?l(p):Fc(p)?l(Ut(2,{from:t,to:p})):(i&&r.enterCallbacks[s]===i&&typeof p=="function"&&i.push(p),a())},c=o(()=>e.call(r&&r.instances[s],t,n,f));let h=Promise.resolve(c);e.length<3&&(h=h.then(f)),h.catch(p=>l(p))})}function dr(e,t,n,r,s=o=>o()){const o=[];for(const i of e)for(const a in i.components){let l=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(di(l)){const c=(l.__vccOpts||l)[t];c&&o.push(gt(c,n,r,i,a,s))}else{let f=l();o.push(()=>f.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${a}" at "${i.path}"`);const h=ic(c)?c.default:c;i.mods[a]=c,i.components[a]=h;const m=(h.__vccOpts||h)[t];return m&&gt(m,n,r,i,a,s)()}))}}return o}function zs(e){const t=ke(Qn),n=ke(Zr),r=te(()=>{const l=Ft(e.to);return t.resolve(l)}),s=te(()=>{const{matched:l}=r.value,{length:f}=l,c=l[f-1],h=n.matched;if(!c||!h.length)return-1;const p=h.findIndex(Bt.bind(null,c));if(p>-1)return p;const m=Qs(l[f-2]);return f>1&&Qs(c)===m&&h[h.length-1].path!==m?h.findIndex(Bt.bind(null,l[f-2])):p}),o=te(()=>s.value>-1&&su(n.params,r.value.params)),i=te(()=>s.value>-1&&s.value===n.matched.length-1&&vi(n.params,r.value.params));function a(l={}){if(ru(l)){const f=t[Ft(e.replace)?"replace":"push"](Ft(e.to)).catch(sn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>f),f}return Promise.resolve()}return{route:r,href:te(()=>r.value.href),isActive:o,isExactActive:i,navigate:a}}function eu(e){return e.length===1?e[0]:e}const tu=Kn({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:zs,setup(e,{slots:t}){const n=vn(zs(e)),{options:r}=ke(Qn),s=te(()=>({[Xs(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Xs(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&eu(t.default(n));return e.custom?o:ci("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},o)}}}),nu=tu;function ru(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function su(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!Ue(s)||s.length!==r.length||r.some((o,i)=>o!==s[i]))return!1}return!0}function Qs(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Xs=(e,t,n)=>e??t??n,ou=Kn({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=ke(Ir),s=te(()=>e.route||r.value),o=ke(Js,0),i=te(()=>{let f=Ft(o);const{matched:c}=s.value;let h;for(;(h=c[f])&&!h.components;)f++;return f}),a=te(()=>s.value.matched[i.value]);An(Js,te(()=>i.value+1)),An(Zc,a),An(Ir,s);const l=be();return nn(()=>[l.value,a.value,e.name],([f,c,h],[p,m,R])=>{c&&(c.instances[h]=f,m&&m!==c&&f&&f===p&&(c.leaveGuards.size||(c.leaveGuards=m.leaveGuards),c.updateGuards.size||(c.updateGuards=m.updateGuards))),f&&c&&(!m||!Bt(c,m)||!p)&&(c.enterCallbacks[h]||[]).forEach(O=>O(f))},{flush:"post"}),()=>{const f=s.value,c=e.name,h=a.value,p=h&&h.components[c];if(!p)return Zs(n.default,{Component:p,route:f});const m=h.props[c],R=m?m===!0?f.params:typeof m=="function"?m(f):m:null,q=ci(p,oe({},R,t,{onVnodeUnmounted:V=>{V.component.isUnmounted&&(h.instances[c]=null)},ref:l}));return Zs(n.default,{Component:q,route:f})||q}}});function Zs(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const iu=ou;function au(e){const t=Yc(e.routes,e),n=e.parseQuery||Qc,r=e.stringifyQuery||qs,s=e.history,o=qt(),i=qt(),a=qt(),l=aa(ft);let f=ft;Dt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=ur.bind(null,_=>""+_),h=ur.bind(null,_c),p=ur.bind(null,gn);function m(_,L){let $,U;return bi(_)?($=t.getRecordMatcher(_),U=L):U=_,t.addRoute(U,$)}function R(_){const L=t.getRecordMatcher(_);L&&t.removeRoute(L)}function O(){return t.getRoutes().map(_=>_.record)}function q(_){return!!t.getRecordMatcher(_)}function V(_,L){if(L=oe({},L||l.value),typeof _=="string"){const g=fr(n,_,L.path),y=t.resolve({path:g.path},L),C=s.createHref(g.fullPath);return oe(g,y,{params:p(y.params),hash:gn(g.hash),redirectedFrom:void 0,href:C})}let $;if(_.path!=null)$=oe({},_,{path:fr(n,_.path,L.path).path});else{const g=oe({},_.params);for(const y in g)g[y]==null&&delete g[y];$=oe({},_,{params:h(g)}),L.params=h(L.params)}const U=t.resolve($,L),le=_.hash||"";U.params=c(p(U.params));const u=Sc(r,oe({},_,{hash:mc(le),path:U.path})),d=s.createHref(u);return oe({fullPath:u,hash:le,query:r===qs?Xc(_.query):_.query||{}},U,{redirectedFrom:void 0,href:d})}function F(_){return typeof _=="string"?fr(n,_,l.value.path):oe({},_)}function H(_,L){if(f!==_)return Ut(8,{from:L,to:_})}function N(_){return D(_)}function Q(_){return N(oe(F(_),{replace:!0}))}function ue(_){const L=_.matched[_.matched.length-1];if(L&&L.redirect){const{redirect:$}=L;let U=typeof $=="function"?$(_):$;return typeof U=="string"&&(U=U.includes("?")||U.includes("#")?U=F(U):{path:U},U.params={}),oe({query:_.query,hash:_.hash,params:U.path!=null?{}:_.params},U)}}function D(_,L){const $=f=V(_),U=l.value,le=_.state,u=_.force,d=_.replace===!0,g=ue($);if(g)return D(oe(F(g),{state:typeof g=="object"?oe({},le,g.state):le,force:u,replace:d}),L||$);const y=$;y.redirectedFrom=L;let C;return!u&&Cc(r,U,$)&&(C=Ut(16,{to:y,from:U}),Ke(U,U,!0,!1)),(C?Promise.resolve(C):v(y,U)).catch(b=>nt(b)?nt(b,2)?b:We(b):J(b,y,U)).then(b=>{if(b){if(nt(b,2))return D(oe({replace:d},F(b.to),{state:typeof b.to=="object"?oe({},le,b.to.state):le,force:u}),L||y)}else b=X(y,U,!0,d,le);return k(y,U,b),b})}function w(_,L){const $=H(_,L);return $?Promise.reject($):Promise.resolve()}function S(_){const L=Ot.values().next().value;return L&&typeof L.runWithContext=="function"?L.runWithContext(_):_()}function v(_,L){let $;const[U,le,u]=lu(_,L);$=dr(U.reverse(),"beforeRouteLeave",_,L);for(const g of U)g.leaveGuards.forEach(y=>{$.push(gt(y,_,L))});const d=w.bind(null,_,L);return $.push(d),Ne($).then(()=>{$=[];for(const g of o.list())$.push(gt(g,_,L));return $.push(d),Ne($)}).then(()=>{$=dr(le,"beforeRouteUpdate",_,L);for(const g of le)g.updateGuards.forEach(y=>{$.push(gt(y,_,L))});return $.push(d),Ne($)}).then(()=>{$=[];for(const g of u)if(g.beforeEnter)if(Ue(g.beforeEnter))for(const y of g.beforeEnter)$.push(gt(y,_,L));else $.push(gt(g.beforeEnter,_,L));return $.push(d),Ne($)}).then(()=>(_.matched.forEach(g=>g.enterCallbacks={}),$=dr(u,"beforeRouteEnter",_,L,S),$.push(d),Ne($))).then(()=>{$=[];for(const g of i.list())$.push(gt(g,_,L));return $.push(d),Ne($)}).catch(g=>nt(g,8)?g:Promise.reject(g))}function k(_,L,$){a.list().forEach(U=>S(()=>U(_,L,$)))}function X(_,L,$,U,le){const u=H(_,L);if(u)return u;const d=L===ft,g=Dt?history.state:{};$&&(U||d?s.replace(_.fullPath,oe({scroll:d&&g&&g.scroll},le)):s.push(_.fullPath,le)),l.value=_,Ke(_,L,$,d),We()}let A;function T(){A||(A=s.listen((_,L,$)=>{if(!wn.listening)return;const U=V(_),le=ue(U);if(le){D(oe(le,{replace:!0,force:!0}),U).catch(sn);return}f=U;const u=l.value;Dt&&Pc(js(u.fullPath,$.delta),zn()),v(U,u).catch(d=>nt(d,12)?d:nt(d,2)?(D(oe(F(d.to),{force:!0}),U).then(g=>{nt(g,20)&&!$.delta&&$.type===mn.pop&&s.go(-1,!1)}).catch(sn),Promise.reject()):($.delta&&s.go(-$.delta,!1),J(d,U,u))).then(d=>{d=d||X(U,u,!1),d&&($.delta&&!nt(d,8)?s.go(-$.delta,!1):$.type===mn.pop&&nt(d,20)&&s.go(-1,!1)),k(U,u,d)}).catch(sn)}))}let W=qt(),B=qt(),j;function J(_,L,$){We(_);const U=B.list();return U.length?U.forEach(le=>le(_,L,$)):console.error(_),Promise.reject(_)}function ge(){return j&&l.value!==ft?Promise.resolve():new Promise((_,L)=>{W.add([_,L])})}function We(_){return j||(j=!_,T(),W.list().forEach(([L,$])=>_?$(_):L()),W.reset()),_}function Ke(_,L,$,U){const{scrollBehavior:le}=e;if(!Dt||!le)return Promise.resolve();const u=!$&&$c(js(_.fullPath,0))||(U||!$)&&history.state&&history.state.scroll||null;return Wn().then(()=>le(_,L,u)).then(d=>d&&Oc(d)).catch(d=>J(d,_,L))}const xe=_=>s.go(_);let xt;const Ot=new Set,wn={currentRoute:l,listening:!0,addRoute:m,removeRoute:R,clearRoutes:t.clearRoutes,hasRoute:q,getRoutes:O,resolve:V,options:e,push:N,replace:Q,go:xe,back:()=>xe(-1),forward:()=>xe(1),beforeEach:o.add,beforeResolve:i.add,afterEach:a.add,onError:B.add,isReady:ge,install(_){const L=this;_.component("RouterLink",nu),_.component("RouterView",iu),_.config.globalProperties.$router=L,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>Ft(l)}),Dt&&!xt&&l.value===ft&&(xt=!0,N(s.location).catch(le=>{}));const $={};for(const le in ft)Object.defineProperty($,le,{get:()=>l.value[le],enumerable:!0});_.provide(Qn,L),_.provide(Zr,Ao($)),_.provide(Ir,l);const U=_.unmount;Ot.add(_),_.unmount=function(){Ot.delete(_),Ot.size<1&&(f=ft,A&&A(),A=null,l.value=ft,xt=!1,j=!1),U()}}};function Ne(_){return _.reduce((L,$)=>L.then(()=>S($)),Promise.resolve())}return wn}function lu(e,t){const n=[],r=[],s=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const a=t.matched[i];a&&(e.matched.find(f=>Bt(f,a))?r.push(a):n.push(a));const l=e.matched[i];l&&(t.matched.find(f=>Bt(f,l))||s.push(l))}return[n,r,s]}function Zu(){return ke(Qn)}function ef(e){return ke(Zr)}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let Ei;const Xn=e=>Ei=e,Ti=Symbol();function xr(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var an;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(an||(an={}));function cu(){const e=co(!0),t=e.run(()=>be({}));let n=[],r=[];const s=Yr({install(o){Xn(s),s._a=o,o.provide(Ti,s),o.config.globalProperties.$pinia=s,r.forEach(i=>n.push(i)),r=[]},use(o){return this._a?n.push(o):r.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return s}const Ai=()=>{};function eo(e,t,n,r=Ai){e.push(t);const s=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),r())};return!n&&uo()&&Vi(s),s}function $t(e,...t){e.slice().forEach(n=>{n(...t)})}const uu=e=>e(),to=Symbol(),hr=Symbol();function Or(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,r)=>e.set(r,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],s=e[n];xr(s)&&xr(r)&&e.hasOwnProperty(n)&&!ve(r)&&!yt(r)?e[n]=Or(s,r):e[n]=r}return e}const fu=Symbol();function du(e){return!xr(e)||!Object.prototype.hasOwnProperty.call(e,fu)}const{assign:dt}=Object;function hu(e){return!!(ve(e)&&e.effect)}function pu(e,t,n,r){const{state:s,actions:o,getters:i}=t,a=n.state.value[e];let l;function f(){a||(n.state.value[e]=s?s():{});const c=ua(n.state.value[e]);return dt(c,o,Object.keys(i||{}).reduce((h,p)=>(h[p]=Yr(te(()=>{Xn(n);const m=n._s.get(e);return i[p].call(m,m)})),h),{}))}return l=Ri(e,f,t,n,r,!0),l}function Ri(e,t,n={},r,s,o){let i;const a=dt({actions:{}},n),l={deep:!0};let f,c,h=[],p=[],m;const R=r.state.value[e];!o&&!R&&(r.state.value[e]={}),be({});let O;function q(w){let S;f=c=!1,typeof w=="function"?(w(r.state.value[e]),S={type:an.patchFunction,storeId:e,events:m}):(Or(r.state.value[e],w),S={type:an.patchObject,payload:w,storeId:e,events:m});const v=O=Symbol();Wn().then(()=>{O===v&&(f=!0)}),c=!0,$t(h,S,r.state.value[e])}const V=o?function(){const{state:S}=n,v=S?S():{};this.$patch(k=>{dt(k,v)})}:Ai;function F(){i.stop(),h=[],p=[],r._s.delete(e)}const H=(w,S="")=>{if(to in w)return w[hr]=S,w;const v=function(){Xn(r);const k=Array.from(arguments),X=[],A=[];function T(j){X.push(j)}function W(j){A.push(j)}$t(p,{args:k,name:v[hr],store:Q,after:T,onError:W});let B;try{B=w.apply(this&&this.$id===e?this:Q,k)}catch(j){throw $t(A,j),j}return B instanceof Promise?B.then(j=>($t(X,j),j)).catch(j=>($t(A,j),Promise.reject(j))):($t(X,B),B)};return v[to]=!0,v[hr]=S,v},N={_p:r,$id:e,$onAction:eo.bind(null,p),$patch:q,$reset:V,$subscribe(w,S={}){const v=eo(h,w,S.detached,()=>k()),k=i.run(()=>nn(()=>r.state.value[e],X=>{(S.flush==="sync"?c:f)&&w({storeId:e,type:an.direct,events:m},X)},dt({},l,S)));return v},$dispose:F},Q=vn(N);r._s.set(e,Q);const D=(r._a&&r._a.runWithContext||uu)(()=>r._e.run(()=>(i=co()).run(()=>t({action:H}))));for(const w in D){const S=D[w];if(ve(S)&&!hu(S)||yt(S))o||(R&&du(S)&&(ve(S)?S.value=R[w]:Or(S,R[w])),r.state.value[e][w]=S);else if(typeof S=="function"){const v=H(S,w);D[w]=v,a.actions[w]=S}}return dt(Q,D),dt(re(Q),D),Object.defineProperty(Q,"$state",{get:()=>r.state.value[e],set:w=>{q(S=>{dt(S,w)})}}),r._p.forEach(w=>{dt(Q,i.run(()=>w({store:Q,app:r._a,pinia:r,options:a})))}),R&&o&&n.hydrate&&n.hydrate(Q.$state,R),f=!0,c=!0,Q}/*! #__NO_SIDE_EFFECTS__ */function Zn(e,t,n){let r;const s=typeof t=="function";r=s?n:t;function o(i,a){const l=Wa();return i=i||(l?ke(Ti,null):null),i&&Xn(i),i=Ei,i._s.has(e)||(s?Ri(e,t,r,i):pu(e,r,i)),i._s.get(e)}return o.$id=e,o}var se=(e=>(e.FANTASY="fantasy",e.SCIFI="sci-fi",e.MODERN="modern",e.HISTORICAL="historical",e.POST_APOCALYPTIC="post-apocalyptic",e.MYSTERY="mystery",e.ROMANCE="romance",e.HORROR="horror",e.COMEDY="comedy",e.ADVENTURE="adventure",e.THRILLER="thriller",e.WESTERN="western",e))(se||{}),ze=(e=>(e.BRAVE="brave",e.COWARDLY="cowardly",e.INTELLIGENT="intelligent",e.NAIVE="naive",e.KIND="kind",e.CRUEL="cruel",e.AMBITIOUS="ambitious",e.LAZY="lazy",e.HONEST="honest",e.DECEPTIVE="deceptive",e.LOYAL="loyal",e.TREACHEROUS="treacherous",e.OPTIMISTIC="optimistic",e.PESSIMISTIC="pessimistic",e.CREATIVE="creative",e.PRACTICAL="practical",e))(ze||{}),z=(e=>(e.BETRAYAL="betrayal",e.FRIENDSHIP="friendship",e.LOVE="love",e.REVENGE="revenge",e.REDEMPTION="redemption",e.SACRIFICE="sacrifice",e.POWER="power",e.FREEDOM="freedom",e.JUSTICE="justice",e.SURVIVAL="survival",e.DISCOVERY="discovery",e.TRANSFORMATION="transformation",e.TIME_TRAVEL="time-travel",e.FAMILY="family",e.IDENTITY="identity",e.COMING_OF_AGE="coming-of-age",e))(z||{}),Ii=(e=>(e.THREE_ACT="three-act",e.FIVE_ACT="five-act",e.HERO_JOURNEY="hero-journey",e.FREYTAG_PYRAMID="freytag-pyramid",e))(Ii||{}),ye=(e=>(e.URBAN="urban",e.RURAL="rural",e.WILDERNESS="wilderness",e.UNDERGROUND="underground",e.SPACE="space",e.UNDERWATER="underwater",e.MAGICAL_REALM="magical-realm",e.DYSTOPIAN_CITY="dystopian-city",e.SMALL_TOWN="small-town",e.CASTLE="castle",e.SPACESHIP="spaceship",e.DESERT="desert",e.FOREST="forest",e.MOUNTAIN="mountain",e.ISLAND="island",e))(ye||{});const gu=[{id:"char-1",name:"Aria Shadowbane",age:24,description:"A skilled rogue with a mysterious past",personalityTraits:[ze.BRAVE,ze.DECEPTIVE,ze.LOYAL],goals:["Find her lost brother","Uncover the truth about her heritage"],flaws:["Trusts too easily","Haunted by past mistakes"],backstory:"Raised in the streets after her family disappeared",appearance:"Dark hair, green eyes, always wears a hooded cloak",role:"protagonist",isTemplate:!0,createdAt:new Date,updatedAt:new Date},{id:"char-2",name:"Lord Malachar",age:45,description:"A powerful sorcerer seeking immortality",personalityTraits:[ze.AMBITIOUS,ze.INTELLIGENT,ze.CRUEL],goals:["Achieve immortality","Rule the kingdom"],flaws:["Arrogant","Underestimates others"],backstory:"Former court wizard who was banished for dark magic",appearance:"Tall, pale, with silver hair and piercing blue eyes",role:"antagonist",isTemplate:!0,createdAt:new Date,updatedAt:new Date},{id:"char-3",name:"Captain Rex Sterling",age:35,description:"A space marine with a strong moral compass",personalityTraits:[ze.BRAVE,ze.HONEST,ze.LOYAL],goals:["Protect the innocent","Uncover the conspiracy"],flaws:["Too rigid in thinking","Struggles with authority"],backstory:"Veteran of the Galactic Wars, now fights corruption",appearance:"Muscular build, scarred face, always in military gear",role:"protagonist",isTemplate:!0,createdAt:new Date,updatedAt:new Date}],mu=[{id:"setting-1",name:"The Whispering Woods",genre:se.FANTASY,type:ye.FOREST,description:"An ancient forest where the trees seem to whisper secrets",atmosphere:"Mysterious and enchanting, with dappled sunlight filtering through ancient oaks",keyLocations:["The Elder Tree","Moonlit Clearing","Hidden Grove"],timeOfDay:"evening",weather:"Misty",season:"autumn",isTemplate:!0,createdAt:new Date,updatedAt:new Date},{id:"setting-2",name:"Neo-Tokyo Megacity",genre:se.SCIFI,type:ye.URBAN,description:"A sprawling cyberpunk metropolis with towering skyscrapers",atmosphere:"Neon-lit, bustling, and technologically advanced",keyLocations:["Corporate District","Underground Markets","Skyway Networks"],timeOfDay:"night",weather:"Rain",season:"winter",isTemplate:!0,createdAt:new Date,updatedAt:new Date},{id:"setting-3",name:"The Crimson Desert",genre:se.POST_APOCALYPTIC,type:ye.DESERT,description:"A vast wasteland of red sand and ruined cities",atmosphere:"Harsh, unforgiving, with remnants of lost civilization",keyLocations:["The Glass City Ruins","Oasis of Hope","The Bone Bridge"],timeOfDay:"noon",weather:"Sandstorm",season:"summer",isTemplate:!0,createdAt:new Date,updatedAt:new Date}],yu=[{id:"theme-1",primary:z.REDEMPTION,secondary:[z.FRIENDSHIP,z.SACRIFICE],description:"A story about finding redemption through friendship and sacrifice",conflictType:"both",mood:"mixed",isTemplate:!0,createdAt:new Date,updatedAt:new Date},{id:"theme-2",primary:z.SURVIVAL,secondary:[z.DISCOVERY,z.TRANSFORMATION],description:"A tale of survival that leads to self-discovery and transformation",conflictType:"external",mood:"dark",isTemplate:!0,createdAt:new Date,updatedAt:new Date},{id:"theme-3",primary:z.LOVE,secondary:[z.BETRAYAL,z.JUSTICE],description:"A love story complicated by betrayal and the pursuit of justice",conflictType:"internal",mood:"mixed",isTemplate:!0,createdAt:new Date,updatedAt:new Date}];se.FANTASY+"",se.SCIFI+"",se.MODERN+"",se.HISTORICAL+"",se.POST_APOCALYPTIC+"",se.MYSTERY+"",se.ROMANCE+"",se.HORROR+"",se.COMEDY+"",se.ADVENTURE+"",se.THRILLER+"",se.WESTERN+"";const vu=[{primary:z.REDEMPTION,secondary:[z.SACRIFICE,z.FRIENDSHIP],description:"A powerful combination exploring how sacrifice and friendship can lead to redemption"},{primary:z.POWER,secondary:[z.BETRAYAL,z.JUSTICE],description:"Classic themes of corruption, betrayal, and the fight for justice"},{primary:z.DISCOVERY,secondary:[z.TRANSFORMATION,z.IDENTITY],description:"A journey of self-discovery that transforms the character's identity"},{primary:z.SURVIVAL,secondary:[z.FAMILY,z.SACRIFICE],description:"Survival story where family bonds drive characters to sacrifice for each other"}],_u=Zn("character",()=>{const e=be([...gu]),t=be([]),n=be(!1),r=be(null),s=te(()=>e.value.filter(D=>D.role==="protagonist")),o=te(()=>e.value.filter(D=>D.role==="antagonist")),i=te(()=>e.value.filter(D=>D.role==="supporting")),a=te(()=>e.value.filter(D=>D.isTemplate)),l=te(()=>e.value.length);function f(D){const w={...D,id:`char-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,createdAt:new Date,updatedAt:new Date};return e.value.push(w),w}function c(D,w){const S=e.value.findIndex(v=>v.id===D);S!==-1&&(e.value[S]={...e.value[S],...w,updatedAt:new Date})}function h(D){const w=e.value.findIndex(S=>S.id===D);if(w!==-1){e.value.splice(w,1);const S=t.value.findIndex(v=>v.id===D);S!==-1&&t.value.splice(S,1)}}function p(D){t.value.find(w=>w.id===D.id)||t.value.push(D)}function m(D){const w=t.value.findIndex(S=>S.id===D);w!==-1&&t.value.splice(w,1)}function R(){t.value=[]}function O(D){const w=e.value.find(S=>S.id===D);if(w){const S={...w,name:`${w.name} (Copy)`,isTemplate:!1};return f(S)}return null}function q(D){const w=["Aiden","Luna","Kai","Zara","Orion","Nova","Sage","Phoenix","River","Storm","Ember","Atlas","Iris","Jasper","Lyra","Dante"],S=["Find their true purpose","Protect their loved ones","Uncover a hidden truth","Master their abilities","Seek revenge","Find redemption","Discover their heritage","Save their world"],v=["Too trusting","Quick to anger","Haunted by the past","Overly cautious","Struggles with self-doubt","Too proud","Fears commitment","Impulsive decisions"],k=w[Math.floor(Math.random()*w.length)],X=S.sort(()=>.5-Math.random()).slice(0,2),A=v.sort(()=>.5-Math.random()).slice(0,2);return f({name:k,age:Math.floor(Math.random()*50)+18,description:"A mysterious character with an unknown past",personalityTraits:D||[],goals:X,flaws:A,role:"supporting",isTemplate:!1})}function V(D){return e.value.find(w=>w.id===D)}function F(D){const w=D.toLowerCase();return e.value.filter(S=>S.name.toLowerCase().includes(w)||S.description.toLowerCase().includes(w)||S.personalityTraits.some(v=>v.toLowerCase().includes(w)))}function H(D){r.value=D}function N(){n.value=!n.value}function Q(){localStorage.setItem("storyBuilder_characters",JSON.stringify(e.value)),localStorage.setItem("storyBuilder_selectedCharacters",JSON.stringify(t.value))}function ue(){const D=localStorage.getItem("storyBuilder_characters"),w=localStorage.getItem("storyBuilder_selectedCharacters");if(D)try{e.value=JSON.parse(D)}catch(S){console.error("Failed to load characters from storage:",S)}if(w)try{t.value=JSON.parse(w)}catch(S){console.error("Failed to load selected characters from storage:",S)}}return{characters:e,selectedCharacters:t,isCreatingCharacter:n,editingCharacter:r,protagonists:s,antagonists:o,supportingCharacters:i,templates:a,characterCount:l,addCharacter:f,updateCharacter:c,deleteCharacter:h,selectCharacter:p,deselectCharacter:m,clearSelectedCharacters:R,duplicateCharacter:O,generateRandomCharacter:q,getCharacterById:V,searchCharacters:F,setEditingCharacter:H,toggleCreatingCharacter:N,saveToStorage:Q,loadFromStorage:ue}}),bu=Zn("setting",()=>{const e=be([...mu]),t=be([]),n=be(!1),r=be(null),s=te(()=>{const w={};return e.value.forEach(S=>{w[S.genre]||(w[S.genre]=[]),w[S.genre].push(S)}),w}),o=te(()=>{const w={};return e.value.forEach(S=>{w[S.type]||(w[S.type]=[]),w[S.type].push(S)}),w}),i=te(()=>e.value.filter(w=>w.isTemplate)),a=te(()=>e.value.length);function l(w){const S={...w,id:`setting-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,createdAt:new Date,updatedAt:new Date};return e.value.push(S),S}function f(w,S){const v=e.value.findIndex(k=>k.id===w);v!==-1&&(e.value[v]={...e.value[v],...S,updatedAt:new Date})}function c(w){const S=e.value.findIndex(v=>v.id===w);if(S!==-1){e.value.splice(S,1);const v=t.value.findIndex(k=>k.id===w);v!==-1&&t.value.splice(v,1)}}function h(w){t.value.find(S=>S.id===w.id)||t.value.push(w)}function p(w){const S=t.value.findIndex(v=>v.id===w);S!==-1&&t.value.splice(S,1)}function m(){t.value=[]}function R(w){const S=e.value.find(v=>v.id===w);if(S){const v={...S,name:`${S.name} (Copy)`,isTemplate:!1};return l(v)}return null}function O(w,S){const v={[ye.URBAN]:["Neon City","Steel Harbor","Glass Metropolis","Chrome District"],[ye.FOREST]:["Whispering Woods","Shadowleaf Forest","Ancient Grove","Moonlit Thicket"],[ye.DESERT]:["Crimson Sands","Glass Desert","Bone Valley","Mirage Wastes"],[ye.MOUNTAIN]:["Frost Peaks","Dragon Spine","Cloud Reach","Stone Crown"],[ye.SPACE]:["Void Station","Nebula Outpost","Star Gate","Cosmic Harbor"],[ye.CASTLE]:["Iron Keep","Shadowmere Castle","Thornwall Fortress","Moonstone Palace"],[ye.ISLAND]:["Coral Haven","Storm Isle","Mist Island","Treasure Cove"],[ye.UNDERGROUND]:["Deep Tunnels","Crystal Caverns","Shadow Depths","Lost Catacombs"],[ye.UNDERWATER]:["Coral City","Abyssal Depths","Kelp Gardens","Sunken Palace"],[ye.MAGICAL_REALM]:["Ethereal Plane","Dreamscape","Fae Realm","Spirit World"],[ye.DYSTOPIAN_CITY]:["Sector 7","The Ruins","New Babylon","Iron District"],[ye.SMALL_TOWN]:["Millbrook","Cedar Falls","Willowdale","Harmony Springs"],[ye.SPACESHIP]:["Star Cruiser","Deep Space Vessel","Battle Frigate","Explorer Ship"],[ye.RURAL]:["Green Valley","Harvest Fields","Countryside","Meadowlands"],[ye.WILDERNESS]:["Wild Frontier","Untamed Lands","Savage Territory","Lost Wilderness"]},k=["Mysterious and foreboding","Bright and welcoming","Dark and oppressive","Peaceful and serene","Chaotic and dangerous","Ancient and mystical","Modern and sleek","Rustic and charming"],X=S||Object.values(ye)[Math.floor(Math.random()*Object.values(ye).length)],A=w||Object.values(se)[Math.floor(Math.random()*Object.values(se).length)],T=v[X]||["Mysterious Place"],W=T[Math.floor(Math.random()*T.length)],B=k[Math.floor(Math.random()*k.length)];return l({name:W,genre:A,type:X,description:`A ${X.replace("-"," ")} with a unique character`,atmosphere:B,keyLocations:["Main Area","Hidden Spot","Important Landmark"],isTemplate:!1})}function q(w){return e.value.find(S=>S.id===w)}function V(w){const S=w.toLowerCase();return e.value.filter(v=>v.name.toLowerCase().includes(S)||v.description.toLowerCase().includes(S)||v.atmosphere.toLowerCase().includes(S)||v.genre.toLowerCase().includes(S)||v.type.toLowerCase().includes(S))}function F(w){return e.value.filter(S=>S.genre===w)}function H(w){return e.value.filter(S=>S.type===w)}function N(w){r.value=w}function Q(){n.value=!n.value}function ue(){localStorage.setItem("storyBuilder_settings",JSON.stringify(e.value)),localStorage.setItem("storyBuilder_selectedSettings",JSON.stringify(t.value))}function D(){const w=localStorage.getItem("storyBuilder_settings"),S=localStorage.getItem("storyBuilder_selectedSettings");if(w)try{e.value=JSON.parse(w)}catch(v){console.error("Failed to load settings from storage:",v)}if(S)try{t.value=JSON.parse(S)}catch(v){console.error("Failed to load selected settings from storage:",v)}}return{settings:e,selectedSettings:t,isCreatingSetting:n,editingSetting:r,settingsByGenre:s,settingsByType:o,templates:i,settingCount:a,addSetting:l,updateSetting:f,deleteSetting:c,selectSetting:h,deselectSetting:p,clearSelectedSettings:m,duplicateSetting:R,generateRandomSetting:O,getSettingById:q,searchSettings:V,getSettingsByGenre:F,getSettingsByType:H,setEditingSetting:N,toggleCreatingSetting:Q,saveToStorage:ue,loadFromStorage:D}}),wu=Zn("theme",()=>{const e=be([...yu]),t=be([]),n=be(!1),r=be(null),s=te(()=>{const A={};return e.value.forEach(T=>{A[T.mood]||(A[T.mood]=[]),A[T.mood].push(T)}),A}),o=te(()=>{const A={};return e.value.forEach(T=>{A[T.conflictType]||(A[T.conflictType]=[]),A[T.conflictType].push(T)}),A}),i=te(()=>e.value.filter(A=>A.isTemplate)),a=te(()=>e.value.length),l=te(()=>vu);function f(A){const T={...A,id:`theme-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,createdAt:new Date,updatedAt:new Date};return e.value.push(T),T}function c(A,T){const W=e.value.findIndex(B=>B.id===A);W!==-1&&(e.value[W]={...e.value[W],...T,updatedAt:new Date})}function h(A){const T=e.value.findIndex(W=>W.id===A);if(T!==-1){e.value.splice(T,1);const W=t.value.findIndex(B=>B.id===A);W!==-1&&t.value.splice(W,1)}}function p(A){t.value.find(T=>T.id===A.id)||t.value.push(A)}function m(A){const T=t.value.findIndex(W=>W.id===A);T!==-1&&t.value.splice(T,1)}function R(){t.value=[]}function O(A){const T=e.value.find(W=>W.id===A);if(T){const W={...T,description:`${T.description} (Copy)`,isTemplate:!1};return f(W)}return null}function q(){const A=Object.values(z),T=["light","dark","neutral","mixed"],W=["internal","external","both"],B=A[Math.floor(Math.random()*A.length)],j=A.filter(We=>We!==B).sort(()=>.5-Math.random()).slice(0,Math.floor(Math.random()*3)+1),J=T[Math.floor(Math.random()*T.length)],ge=W[Math.floor(Math.random()*W.length)];return f({primary:B,secondary:j,description:`A story exploring ${B} with elements of ${j.join(", ")}`,conflictType:ge,mood:J,isTemplate:!1})}function V(A){return f({primary:A.primary,secondary:A.secondary,description:A.description,conflictType:"both",mood:"mixed",isTemplate:!1})}function F(A){return e.value.find(T=>T.id===A)}function H(A){const T=A.toLowerCase();return e.value.filter(W=>W.description.toLowerCase().includes(T)||W.primary.toLowerCase().includes(T)||W.secondary.some(B=>B.toLowerCase().includes(T))||W.mood.toLowerCase().includes(T)||W.conflictType.toLowerCase().includes(T))}function N(A){return e.value.filter(T=>T.mood===A)}function Q(A){return e.value.filter(T=>T.conflictType===A)}function ue(A){return e.value.filter(T=>T.primary===A)}function D(A){return t.value.some(T=>T.primary===A||T.secondary.includes(A))}function w(A){const T={[z.LOVE]:[z.BETRAYAL],[z.JUSTICE]:[z.REVENGE],[z.REDEMPTION]:[z.REVENGE],[z.FRIENDSHIP]:[z.BETRAYAL],[z.FREEDOM]:[z.POWER]},W=[];return t.value.forEach(B=>{const j=T[B.primary]||[],J=B.secondary.flatMap(ge=>T[ge]||[]);(j.includes(A.primary)||J.includes(A.primary)||A.secondary.some(ge=>j.includes(ge)||J.includes(ge)))&&W.push(B)}),W}function S(A){r.value=A}function v(){n.value=!n.value}function k(){localStorage.setItem("storyBuilder_themes",JSON.stringify(e.value)),localStorage.setItem("storyBuilder_selectedThemes",JSON.stringify(t.value))}function X(){const A=localStorage.getItem("storyBuilder_themes"),T=localStorage.getItem("storyBuilder_selectedThemes");if(A)try{e.value=JSON.parse(A)}catch(W){console.error("Failed to load themes from storage:",W)}if(T)try{t.value=JSON.parse(T)}catch(W){console.error("Failed to load selected themes from storage:",W)}}return{themes:e,selectedThemes:t,isCreatingTheme:n,editingTheme:r,themesByMood:s,themesByConflictType:o,templates:i,themeCount:a,suggestions:l,addTheme:f,updateTheme:c,deleteTheme:h,selectTheme:p,deselectTheme:m,clearSelectedThemes:R,duplicateTheme:O,generateRandomTheme:q,createThemeFromSuggestion:V,getThemeById:F,searchThemes:H,getThemesByMood:N,getThemesByConflictType:Q,getThemesByPrimary:ue,hasTheme:D,getConflictingThemes:w,setEditingTheme:S,toggleCreatingTheme:v,saveToStorage:k,loadFromStorage:X}}),Su={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1};class Pr{config;constructor(t){this.config=t}async generateChapterContent(t,n,r,s){const o=this.buildPrompt(t,n,r,s);try{switch(this.config.provider){case"openai":return await this.generateWithOpenAI(o);case"anthropic":return await this.generateWithAnthropic(o);case"local":return await this.generateWithLocalModel(o);default:throw new Error(`Unsupported AI provider: ${this.config.provider}`)}}catch(i){return console.error("AI generation failed:",i),this.generateFallbackContent(t,n,r,s)}}buildPrompt(t,n,r,s){const o=n.characters.filter(l=>t.characters.includes(l.id)),i=n.settings.find(l=>l.id===t.setting);o.find(l=>l.role==="protagonist")||o[0];let a=`Write Chapter ${s} of a ${n.genre} story titled "${n.title}".

`;return a+=`STORY CONTEXT:
`,a+=`- Genre: ${n.genre}
`,a+=`- Themes: ${n.themes.join(", ")}
`,a+=`- Summary: ${n.summary}

`,a+=`CHAPTER DETAILS:
`,a+=`- Title: ${t.title}
`,a+=`- Plot Point Type: ${t.type}
`,a+=`- Act: ${t.act}
`,a+=`- Description: ${t.description}

`,i&&(a+=`SETTING:
`,a+=`- Name: ${i.name}
`,a+=`- Type: ${i.type}
`,a+=`- Description: ${i.description}
`,a+=`- Atmosphere: ${i.atmosphere}
`,i.keyLocations.length>0&&(a+=`- Key Locations: ${i.keyLocations.join(", ")}
`),a+=`
`),a+=`CHARACTERS IN THIS CHAPTER:
`,o.forEach(l=>{a+=`- ${l.name} (${l.role}): ${l.description}
`,l.personalityTraits.length>0&&(a+=`  Personality: ${l.personalityTraits.join(", ")}
`),l.goals.length>0&&(a+=`  Goals: ${l.goals.join(", ")}
`),l.flaws.length>0&&(a+=`  Flaws: ${l.flaws.join(", ")}
`)}),a+=`
`,a+=`WRITING STYLE:
`,a+=`- Perspective: ${r.narrativePerspective}
`,a+=`- Tense: ${r.tense}
`,a+=`- Include dialogue: ${r.includeDialogue?"Yes":"No"}
`,a+=`- Target length: ${this.getTargetLength(r.length)} words

`,a+=`INSTRUCTIONS:
`,a+=`Write a compelling chapter that:
`,a+=`1. Advances the plot according to the plot point description
`,a+=`2. Develops the characters and their relationships
`,a+=`3. Maintains the established tone and atmosphere
`,a+=`4. Includes vivid descriptions of the setting
`,r.includeDialogue&&(a+=`5. Features natural dialogue that reveals character
`),a+=`6. Builds toward the overall story themes
`,a+=`7. Ends with appropriate tension or resolution for this plot point

`,a+="Write the chapter now:",a}async generateWithOpenAI(t){if(!this.config.apiKey)throw new Error("OpenAI API key not provided");const n=await fetch("https://api.openai.com/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`},body:JSON.stringify({model:this.config.model||"gpt-4",messages:[{role:"system",content:"You are a skilled creative writer specializing in narrative fiction. Write engaging, well-structured prose with vivid descriptions and compelling character development."},{role:"user",content:t}],max_tokens:2e3,temperature:.8})});if(!n.ok)throw new Error(`OpenAI API error: ${n.statusText}`);return(await n.json()).choices[0]?.message?.content||""}async generateWithAnthropic(t){if(!this.config.apiKey)throw new Error("Anthropic API key not provided");const n=await fetch("https://api.anthropic.com/v1/messages",{method:"POST",headers:{"Content-Type":"application/json","x-api-key":this.config.apiKey,"anthropic-version":"2023-06-01"},body:JSON.stringify({model:this.config.model||"claude-3-sonnet-20240229",max_tokens:2e3,messages:[{role:"user",content:t}],temperature:.8})});if(!n.ok)throw new Error(`Anthropic API error: ${n.statusText}`);return(await n.json()).content[0]?.text||""}async generateWithLocalModel(t){const n=this.config.baseURL||"http://localhost:11434",r=await fetch(`${n}/api/generate`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({model:this.config.model||"llama2",prompt:t,stream:!1,options:{temperature:.8,top_p:.9}})});if(!r.ok)throw new Error(`Local model API error: ${r.statusText}`);return(await r.json()).response||""}generateFallbackContent(t,n,r,s){return`# Chapter ${s}: ${t.title}

${t.description}

[This chapter would be generated using the enhanced rule-based system as a fallback when AI generation is unavailable.]`}getTargetLength(t){switch(t){case"short":return 300;case"medium":return 600;case"long":return 1e3;default:return 600}}static isConfigured(t){switch(t.provider){case"openai":case"anthropic":return!!t.apiKey;case"local":return!0;default:return!1}}}function Cu(){try{const e=localStorage.getItem("aiStoryConfig");if(e){const t=JSON.parse(e);if(Pr.isConfigured(t))return t}}catch(e){console.warn("Failed to load AI config from localStorage:",e)}try{const e=Su;if(e.VITE_OPENAI_API_KEY)return{provider:"openai",apiKey:e.VITE_OPENAI_API_KEY,model:e.VITE_OPENAI_MODEL||"gpt-4"};if(e.VITE_ANTHROPIC_API_KEY)return{provider:"anthropic",apiKey:e.VITE_ANTHROPIC_API_KEY,model:e.VITE_ANTHROPIC_MODEL||"claude-3-sonnet-20240229"}}catch{}return null}class Eu{static async generateStoryContent(t,n){let r=`# ${t.title}

`;r+=`*${t.summary}*

`;for(let s=0;s<t.plotPoints.length;s++){const o=t.plotPoints[s],i=await this.generateChapterContent(o,t,n,s+1);r+=i+`

`}return r}static async generateChapterContent(t,n,r,s){if(r.useAI){const l=Cu();if(l&&Pr.isConfigured(l))try{const c=await new Pr(l).generateChapterContent(t,n,r,s);if(c&&c.length>100)return`## Chapter ${s}: ${t.title}

${c}`}catch(f){console.warn("AI generation failed, falling back to rule-based generation:",f)}}const o=n.characters.filter(l=>t.characters.includes(l.id)),i=n.settings.find(l=>l.id===t.setting);let a=`## Chapter ${s}: ${t.title}

`;return a+=this.generateSceneDescription(t,i,o,r),a+=`

`,a+=this.generateNarrativeContent(t,o,i,r),r.includeDialogue&&o.length>1&&(a+=`

`,a+=this.generateDialogueSection(t,o,r)),a}static generateSceneDescription(t,n,r,s){if(!n)return"";const o=n.timeOfDay?` during ${n.timeOfDay}`:"",i=n.weather?` The weather is ${n.weather.toLowerCase()}.`:"",a=n.atmosphere?` ${n.atmosphere}.`:"";let l=`The scene takes place in ${n.name}${o}.${i}${a}`;if(r.length>0){const f=r.map(c=>c.name).join(", ");l+=` Present in this scene: ${f}.`}return l}static generateNarrativeContent(t,n,r,s){const o=n.find(i=>i.role==="protagonist")||n[0];switch(t.type){case"setup":return this.generateSetupContent(o,r,s);case"inciting-incident":return this.generateIncitingIncidentContent(o,t,s);case"plot-point":return this.generatePlotPointContent(o,n,t,s);case"midpoint":return this.generateMidpointContent(o,n,t,s);case"crisis":return this.generateCrisisContent(o,t,s);case"climax":return this.generateClimaxContent(n,t,s);case"resolution":return this.generateResolutionContent(o,t,s);default:return this.generateGenericContent(o,t,s)}}static generateSetupContent(t,n,r){const s=this.getPerspectivePrefix(t,r);n&&`${n.name}`;let o="";return n&&(o+=`${n.atmosphere} ${n.description} `,n.timeOfDay&&(o+=`The ${n.timeOfDay} light cast long shadows across ${n.keyLocations[0]||"the landscape"}. `),n.weather&&(o+=`${n.weather} weather added to the mood of the place. `),o+=`

`),o+=`${s} ${t.name} moved through this world with the practiced ease of someone who belonged here. `,o+=`${this.getDetailedCharacterDescription(t)} `,t.age&&(o+=`At ${t.age}, ${t.name} had seen enough of life to understand its complexities. `),o+=`

`,o+=`${t.name}'s days were consumed by a single driving purpose: ${t.goals[0]||"finding their true calling"}. `,t.goals.length>1&&(o+=`Beyond that, they also sought to ${t.goals[1]}. `),o+=`Yet despite their determination, ${t.name} wrestled with ${t.flaws[0]||"an inner uncertainty"}. `,t.flaws.length>1&&(o+=`This was compounded by their tendency toward ${t.flaws[1]}. `),o+=`

`,o+=`As ${t.name} went about their routine this particular day, they had no way of knowing that forces beyond their understanding were already in motion. `,o+="The ordinary world they knew so well was about to be shattered, and nothing would ever be the same again.",o}static generateIncitingIncidentContent(t,n,r){this.getPerspectivePrefix(t,r);let s="";return s+=`${t.name} had been going about their day when something felt... different. `,s+="The air seemed charged with an energy they couldn't quite place. ",s+=`Perhaps it was intuition, or perhaps it was simply coincidence, but ${t.name} found themselves more alert than usual. `,s+=`

`,s+="Then it happened. ",s+=`${n.description} `,s+=`The moment stretched like an eternity, every detail burning itself into ${t.name}'s memory. `,s+="The sound, the sight, the very feeling of the world shifting beneath their feet. ",s+=`

`,s+=`${t.name}'s first instinct was ${this.getCharacterReaction(t,"shock")}. `,s+="Their mind raced, trying to process what had just occurred. ",s+="This wasn't supposed to happen. This wasn't part of the plan. ",s+=`

`,s+="As the initial shock wore off, a terrible understanding began to dawn. ",s+=`The comfortable, predictable world ${t.name} had known was gone. `,s+="In its place stood something unknown, something that demanded a choice. ",s+="They could try to return to the way things were, or they could step forward into uncertainty. ",s+=`But deep down, ${t.name} already knew there was no going back.`,s}static generatePlotPointContent(t,n,r,s){const o=this.getPerspectivePrefix(t,s),i=n.find(l=>l.role==="antagonist");let a=`${o} ${t.name} faces a significant challenge. ${r.description} `;return i&&(a+=`${i.name} presents a formidable obstacle, testing ${t.name}'s resolve. `),a+=`This moment requires ${t.name} to dig deeper and find strength they didn't know they possessed.`,a}static generateMidpointContent(t,n,r,s){return`${this.getPerspectivePrefix(t,s)} ${t.name} reaches a crucial turning point. ${r.description} Everything they thought they knew is called into question. The stakes have never been higher, and there's no going back to the way things were. ${t.name} must embrace their transformation or risk losing everything.`}static generateCrisisContent(t,n,r){return`${this.getPerspectivePrefix(t,r)} ${t.name} faces their darkest hour. ${n.description} All hope seems lost, and their greatest fears are realized. ${t.flaws[0]?`Their tendency toward ${t.flaws[0]} threatens to overwhelm them. `:""}In this moment of despair, ${t.name} must find the inner strength to continue.`}static generateClimaxContent(t,n,r){const s=t.find(a=>a.role==="protagonist")||t[0],o=t.find(a=>a.role==="antagonist");this.getPerspectivePrefix(s,r);let i="";return i+=`The moment ${s.name} had been dreading and preparing for had finally arrived. `,i+="Every step of their journey, every lesson learned, every sacrifice made had led to this single point in time. ",i+="There was no more running, no more preparation, no more doubt. ",i+="This was it. ",i+=`

`,o&&(i+=`${s.name} stood face to face with ${o.name}, the air between them crackling with tension. `,i+=`"${this.generateClimaxDialogue(s,"confrontation")}" ${s.name} said, their voice steady despite the storm raging within. `,i+=`

`,i+=`${o.name} smiled, but there was no warmth in it. "${this.generateClimaxDialogue(o,"response")}" `,i+=`

`),i+=`${n.description} `,i+=`Everything ${s.name} had learned about themselves, about courage, about what truly mattered, was put to the ultimate test. `,i+=`

`,i+=`In that crucial moment, ${s.name} found strength they never knew they possessed. `,i+="Not just physical strength, but the strength that comes from knowing who you are and what you stand for. ",i+=`The ${s.flaws[0]||"doubt"} that had plagued them throughout their journey finally fell away, replaced by clarity and purpose. `,i+=`

`,i+=`When the dust settled, ${s.name} stood transformed. `,i+="They had not just survived the confrontation—they had grown beyond what they ever thought possible. ",i+=`The goal they had started with—${s.goals[0]||"finding their purpose"}—had evolved into something far greater. `,i}static generateResolutionContent(t,n,r){return`${this.getPerspectivePrefix(t,r)} the dust settles and a new equilibrium emerges. ${n.description} ${t.name} has been fundamentally changed by their journey. The goal they once sought - ${t.goals[0]||"their purpose"} - has evolved into something deeper and more meaningful. Though the adventure is over, the lessons learned will last a lifetime.`}static generateGenericContent(t,n,r){return`${this.getPerspectivePrefix(t,r)} ${t.name} continues their journey. ${n.description} Each step forward brings new challenges and revelations. The path ahead remains uncertain, but their determination grows stronger.`}static generateDialogueSection(t,n,r){if(n.length<2)return"";const s=n.find(a=>a.role==="protagonist")||n[0],o=n.find(a=>a.id!==s.id)||n[1];let i=`---

`;return i+=`"${this.generateCharacterLine(s,t,"opening")}" ${s.name} said.

`,i+=`"${this.generateCharacterLine(o,t,"response")}" ${o.name} replied.

`,i+=`"${this.generateCharacterLine(s,t,"reaction")}" ${s.name} responded.

`,i}static generateCharacterLine(t,n,r){const s=t.personalityTraits;t.goals[0],t.flaws[0];const o={opening:["I never expected things to turn out this way","We need to talk about what happened","There's something I need to tell you","I'm not sure I can handle this alone"],response:["I understand how you feel","That's not what I expected to hear","We'll figure this out together","You're stronger than you think"],reaction:["Thank you for believing in me","I hope you're right about this","I won't let you down","Let's do what needs to be done"]};let i=o[r][Math.floor(Math.random()*o[r].length)];return s.includes("brave")&&r==="opening"?i="I'm ready to face whatever comes next":s.includes("deceptive")&&r==="response"&&(i="Of course, you can trust me completely"),i}static generateClimaxDialogue(t,n){const s={confrontation:["This ends now. I won't let you hurt anyone else.","I've come too far to back down now.","You've taken everything from me, but you won't take my future.","I'm not the same person who started this journey.","Whatever happens next, I'm ready for it."],response:["You think you've grown stronger? You're still the same weak fool.","Your journey ends here, just as I always planned.","You cannot stop what has already been set in motion.","Strength? You know nothing of true power.","This is where your story ends."]}[n];return s[Math.floor(Math.random()*s.length)]}static getPerspectivePrefix(t,n){switch(n.narrativePerspective){case"first":return"I";case"second":return"You";case"third-limited":return`${t.name}`;case"third-omniscient":return"In this moment,";default:return`${t.name}`}}static getCharacterDescription(t){const n=t.personalityTraits.slice(0,2).join(" and "),r=t.description||"a person of mystery";return`${t.name} is ${r}, known for being ${n}.`}static getDetailedCharacterDescription(t){let n=t.description||"a person of mystery";if(t.appearance&&(n+=` ${t.appearance}`),t.personalityTraits.length>0){const r=t.personalityTraits.slice(0,3);n+=` Their personality was marked by being ${r.join(", ")}.`}return t.backstory&&(n+=` ${t.backstory}`),n}static getCharacterReaction(t,n){const r={shock:{brave:["to stand firm despite the chaos","to face the situation head-on","to protect others nearby"],cowardly:["to step back in fear","to look for an escape route","to hide behind something solid"],intelligent:["to analyze what was happening","to look for logical explanations","to assess the situation carefully"],kind:["to worry about others who might be affected","to check if anyone needed help","to think of those they cared about"],deceptive:["to consider how this might be used to their advantage","to wonder who might be behind this","to mask their true feelings"]}};for(const s of t.personalityTraits)if(r[n]&&r[n][s]){const o=r[n][s];return o[Math.floor(Math.random()*o.length)]}return"to freeze momentarily, unsure how to respond"}static estimateReadingTime(t){const r=t.split(/\s+/).length;return Math.ceil(r/200)}static generateChapterTitles(t){return t.map((n,r)=>`Chapter ${r+1}: ${n.title}`)}}const Tu=Zn("story",()=>{const e=be([]),t=be(null),n=be(null),r=be({currentStep:"characters",selectedCharacters:[],selectedSettings:[],selectedThemes:[],generationOptions:{useAI:!1,creativity:.7,length:"medium",includeDialogue:!0,narrativePerspective:"third-limited",tense:"past"},isGenerating:!1,progress:0}),s=te(()=>e.value.length),o=te(()=>e.value.filter(v=>v.isComplete)),i=te(()=>e.value.filter(v=>!v.isComplete)),a=te(()=>r.value.selectedCharacters.length>0&&r.value.selectedSettings.length>0&&r.value.selectedThemes.length>0),l=te(()=>["characters","settings","themes","generation","editing","complete"].indexOf(r.value.currentStep));function f(v,k,X,A){const T=k[0]?.genre||se.FANTASY,W=X.map(j=>j.primary);return{id:`outline-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,title:h(W,T),genre:T,themes:W,structure:Ii.THREE_ACT,characters:[...v],settings:[...k],plotPoints:c(v,k,X),summary:p(v,k,X),wordCountTarget:m(A.length),estimatedReadingTime:R(A.length),createdAt:new Date,updatedAt:new Date}}function c(v,k,X,A){const T=v.find(ge=>ge.role==="protagonist")||v[0],W=v.find(ge=>ge.role==="antagonist"),B=k[0],j=X[0],J=[];return J.push({id:`plot-${Date.now()}-1`,title:"Opening Scene",description:`Introduce ${T.name} in ${B.name}. Establish the ordinary world and hint at the central conflict.`,act:1,order:1,type:"setup",characters:[T.id],setting:B.id}),J.push({id:`plot-${Date.now()}-2`,title:"Inciting Incident",description:`An event disrupts ${T.name}'s normal life, setting the story in motion. This relates to the theme of ${j.primary}.`,act:1,order:2,type:"inciting-incident",characters:[T.id],setting:B.id}),J.push({id:`plot-${Date.now()}-3`,title:"First Plot Point",description:`${T.name} commits to the journey or quest. No turning back.`,act:1,order:3,type:"plot-point",characters:[T.id],setting:B.id}),J.push({id:`plot-${Date.now()}-4`,title:"First Obstacle",description:`${T.name} faces their first major challenge. ${W?`${W.name} begins to emerge as a threat.`:"The central conflict intensifies."}`,act:2,order:4,type:"plot-point",characters:W?[T.id,W.id]:[T.id],setting:k[1]?.id||B.id}),J.push({id:`plot-${Date.now()}-5`,title:"Midpoint",description:`A major revelation or turning point. ${T.name} gains new understanding but faces greater stakes.`,act:2,order:5,type:"midpoint",characters:[T.id],setting:k[1]?.id||B.id}),J.push({id:`plot-${Date.now()}-6`,title:"Crisis",description:`${T.name} faces their darkest moment. All seems lost. This tests the theme of ${j.primary}.`,act:2,order:6,type:"crisis",characters:[T.id],setting:k[2]?.id||B.id}),J.push({id:`plot-${Date.now()}-7`,title:"Climax",description:`The final confrontation. ${T.name} uses everything they've learned to face the ultimate challenge.`,act:3,order:7,type:"climax",characters:v.map(ge=>ge.id),setting:k[k.length-1]?.id||B.id}),J.push({id:`plot-${Date.now()}-8`,title:"Resolution",description:`The aftermath. ${T.name} has changed, and the world reflects the theme of ${j.primary}.`,act:3,order:8,type:"resolution",characters:[T.id],setting:B.id}),J}function h(v,k){const X={[z.REDEMPTION]:["Redemption","Second Chance","Forgiveness"],[z.BETRAYAL]:["Betrayal","Broken Trust","False Friend"],[z.LOVE]:["Love","Heart","Devotion"],[z.REVENGE]:["Vengeance","Retribution","Payback"],[z.SURVIVAL]:["Survival","Last Stand","Endurance"],[z.DISCOVERY]:["Discovery","Revelation","Hidden Truth"],[z.POWER]:["Power","Crown","Dominion"],[z.FREEDOM]:["Freedom","Liberation","Escape"],[z.SACRIFICE]:["Sacrifice","Price","Cost"],[z.TRANSFORMATION]:["Transformation","Change","Metamorphosis"],[z.FRIENDSHIP]:["Friendship","Bond","Alliance"],[z.FAMILY]:["Family","Legacy","Heritage"],[z.IDENTITY]:["Identity","Self","Truth"],[z.JUSTICE]:["Justice","Balance","Judgment"],[z.COMING_OF_AGE]:["Journey","Awakening","Growth"],[z.TIME_TRAVEL]:["Time","Destiny","Paradox"]},A={[se.FANTASY]:["Realm","Kingdom","Magic","Dragon","Quest"],[se.SCIFI]:["Galaxy","Star","Future","Cyber","Nova"],[se.MYSTERY]:["Shadow","Secret","Clue","Mystery","Case"],[se.HORROR]:["Darkness","Fear","Nightmare","Terror","Haunted"],[se.ROMANCE]:["Heart","Love","Passion","Desire","Soul"],[se.ADVENTURE]:["Adventure","Quest","Journey","Expedition"],[se.THRILLER]:["Thriller","Chase","Hunt","Race"],[se.WESTERN]:["Frontier","Trail","Range","Territory"],[se.HISTORICAL]:["Era","Age","Time","Period"],[se.MODERN]:["City","Life","World","Reality"],[se.POST_APOCALYPTIC]:["Wasteland","Ruins","Ashes","Remnant"],[se.COMEDY]:["Comedy","Farce","Jest","Humor"]},T=v[0],W=X[T]?.[Math.floor(Math.random()*X[T].length)]||"Story",B=A[k]?.[Math.floor(Math.random()*(A[k]?.length||1))]||"Tale",j=[`The ${W} of ${B}`,`${W}'s ${B}`,`The ${B} ${W}`,`${B} of ${W}`];return j[Math.floor(Math.random()*j.length)]}function p(v,k,X){const A=v.find(J=>J.role==="protagonist")||v[0],T=v.find(J=>J.role==="antagonist"),W=k[0],B=X[0];let j=`In ${W.name}, ${A.name} must confront `;return T?j+=`${T.name} while dealing with themes of ${B.primary}`:j+=`challenges that test their understanding of ${B.primary}`,X.length>1&&(j+=` and ${X.slice(1).map(J=>J.primary).join(", ")}`),j+=`. Through their journey, they will discover what it truly means to ${A.goals[0]||"find their purpose"}.`,j}function m(v){switch(v){case"short":return 2e3;case"medium":return 5e3;case"long":return 1e4;default:return 5e3}}function R(v){const X=m(v);return Math.ceil(X/200)}async function O(){if(!a.value)return null;r.value.isGenerating=!0,r.value.progress=0;try{r.value.progress=25;const v=f(r.value.selectedCharacters,r.value.selectedSettings,r.value.selectedThemes,r.value.generationOptions);n.value=v,r.value.progress=50;const k=await q(v);r.value.progress=75;const X={id:`story-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,outline:v,content:k,isComplete:!1,chapters:[],metadata:{wordCount:k.split(" ").length,readingTime:Math.ceil(k.split(" ").length/200),lastEditedAt:new Date,version:1,tags:[],isPublic:!1},createdAt:new Date,updatedAt:new Date};return e.value.push(X),t.value=X,r.value.progress=100,r.value.currentStep="complete",X}catch(v){return console.error("Failed to generate story:",v),null}finally{r.value.isGenerating=!1}}async function q(v){return await Eu.generateStoryContent(v,r.value.generationOptions)}function V(v){r.value.currentStep=v}function F(){const v=["characters","settings","themes","generation","editing","complete"],k=v.indexOf(r.value.currentStep);k<v.length-1&&(r.value.currentStep=v[k+1])}function H(){const v=["characters","settings","themes","generation","editing","complete"],k=v.indexOf(r.value.currentStep);k>0&&(r.value.currentStep=v[k-1])}function N(){r.value={currentStep:"characters",selectedCharacters:[],selectedSettings:[],selectedThemes:[],generationOptions:{useAI:!1,creativity:.7,length:"medium",includeDialogue:!0,narrativePerspective:"third-limited",tense:"past"},isGenerating:!1,progress:0},t.value=null,n.value=null}function Q(v){r.value.generationOptions={...r.value.generationOptions,...v}}function ue(v){const k=e.value.findIndex(X=>X.id===v);k!==-1&&(e.value.splice(k,1),t.value?.id===v&&(t.value=null))}function D(v){return e.value.find(k=>k.id===v)}function w(){localStorage.setItem("storyBuilder_stories",JSON.stringify(e.value)),localStorage.setItem("storyBuilder_generatorState",JSON.stringify(r.value))}function S(){const v=localStorage.getItem("storyBuilder_stories"),k=localStorage.getItem("storyBuilder_generatorState");if(v)try{e.value=JSON.parse(v)}catch(X){console.error("Failed to load stories from storage:",X)}if(k)try{r.value=JSON.parse(k)}catch(X){console.error("Failed to load generator state from storage:",X)}}return{stories:e,currentStory:t,currentOutline:n,generatorState:r,storyCount:s,completedStories:o,draftStories:i,canGenerateStory:a,currentStepIndex:l,generateStory:O,setCurrentStep:V,nextStep:F,previousStep:H,resetGenerator:N,updateGenerationOptions:Q,deleteStory:ue,getStoryById:D,saveToStorage:w,loadFromStorage:S}}),Au={class:"home"},Ru={class:"home__hero"},Iu={class:"home__hero-content"},xu={class:"home__actions"},Ou={class:"home__stats"},Pu={class:"home__stat"},$u={class:"home__stat-number"},Mu={class:"home__stat"},Du={class:"home__stat-number"},Nu={class:"home__stat"},Lu={class:"home__stat-number"},Fu={class:"home__stat"},ku={class:"home__stat-number"},ju=Kn({__name:"HomeView",setup(e){const t=_u(),n=bu(),r=wu(),s=Tu(),o=te(()=>t.characterCount),i=te(()=>n.settingCount),a=te(()=>r.themeCount),l=te(()=>s.storyCount);return ko(()=>{t.loadFromStorage(),n.loadFromStorage(),r.loadFromStorage(),s.loadFromStorage()}),(f,c)=>{const h=vr("router-link");return fn(),oi("div",Au,[pe("div",Ru,[pe("div",Iu,[c[2]||(c[2]=pe("h1",{class:"home__title"},"AI Story Builder",-1)),c[3]||(c[3]=pe("p",{class:"home__subtitle"}," Create compelling stories with our interactive story generator. Choose characters, settings, and themes to craft unique narratives. ",-1)),pe("div",xu,[me(h,{to:"/generator",class:"home__cta"},{default:Ct(()=>c[0]||(c[0]=[Et(" Start Creating ")])),_:1,__:[0]}),me(h,{to:"/library",class:"home__secondary"},{default:Ct(()=>c[1]||(c[1]=[Et(" View Library ")])),_:1,__:[1]})])]),c[4]||(c[4]=vs('<div class="home__hero-visual" data-v-4433fcbf><div class="home__feature-grid" data-v-4433fcbf><div class="home__feature" data-v-4433fcbf><div class="home__feature-icon" data-v-4433fcbf>👤</div><h3 data-v-4433fcbf>Rich Characters</h3><p data-v-4433fcbf>Create detailed characters with personalities, goals, and flaws</p></div><div class="home__feature" data-v-4433fcbf><div class="home__feature-icon" data-v-4433fcbf>🌍</div><h3 data-v-4433fcbf>Vivid Settings</h3><p data-v-4433fcbf>Build immersive worlds across multiple genres and time periods</p></div><div class="home__feature" data-v-4433fcbf><div class="home__feature-icon" data-v-4433fcbf>🎭</div><h3 data-v-4433fcbf>Compelling Themes</h3><p data-v-4433fcbf>Explore deep themes like redemption, love, and transformation</p></div><div class="home__feature" data-v-4433fcbf><div class="home__feature-icon" data-v-4433fcbf>📚</div><h3 data-v-4433fcbf>Story Generation</h3><p data-v-4433fcbf>Generate structured outlines with 3-act story progression</p></div></div></div>',1))]),pe("div",Ou,[pe("div",Pu,[pe("div",$u,Jt(o.value),1),c[5]||(c[5]=pe("div",{class:"home__stat-label"},"Characters Created",-1))]),pe("div",Mu,[pe("div",Du,Jt(i.value),1),c[6]||(c[6]=pe("div",{class:"home__stat-label"},"Settings Available",-1))]),pe("div",Nu,[pe("div",Lu,Jt(a.value),1),c[7]||(c[7]=pe("div",{class:"home__stat-label"},"Themes to Explore",-1))]),pe("div",Fu,[pe("div",ku,Jt(l.value),1),c[8]||(c[8]=pe("div",{class:"home__stat-label"},"Stories Generated",-1))])]),c[9]||(c[9]=vs('<div class="home__how-it-works" data-v-4433fcbf><h2 class="home__section-title" data-v-4433fcbf>How It Works</h2><div class="home__steps" data-v-4433fcbf><div class="home__step" data-v-4433fcbf><div class="home__step-number" data-v-4433fcbf>1</div><h3 class="home__step-title" data-v-4433fcbf>Choose Characters</h3><p class="home__step-description" data-v-4433fcbf> Select from pre-made characters or create your own with unique personalities and motivations </p></div><div class="home__step" data-v-4433fcbf><div class="home__step-number" data-v-4433fcbf>2</div><h3 class="home__step-title" data-v-4433fcbf>Pick Settings</h3><p class="home__step-description" data-v-4433fcbf> Choose the worlds and locations where your story will unfold, from fantasy realms to sci-fi cities </p></div><div class="home__step" data-v-4433fcbf><div class="home__step-number" data-v-4433fcbf>3</div><h3 class="home__step-title" data-v-4433fcbf>Select Themes</h3><p class="home__step-description" data-v-4433fcbf> Define the central conflicts and themes that will drive your narrative forward </p></div><div class="home__step" data-v-4433fcbf><div class="home__step-number" data-v-4433fcbf>4</div><h3 class="home__step-title" data-v-4433fcbf>Generate Story</h3><p class="home__step-description" data-v-4433fcbf> Our engine creates a structured story outline with plot points, character arcs, and narrative flow </p></div></div></div>',1))])}}}),Hu=(e,t)=>{const n=e.__vccOpts||e;for(const[r,s]of t)n[r]=s;return n},Vu=Hu(ju,[["__scopeId","data-v-4433fcbf"]]),Bu=au({history:Lc("/"),routes:[{path:"/",name:"home",component:Vu},{path:"/generator",name:"generator",component:()=>cr(()=>import("./GeneratorView-1j8voqWk.js"),__vite__mapDeps([0,1,2,3]))},{path:"/story/:id",name:"story",component:()=>cr(()=>import("./StoryView-ZzP6SpX3.js"),__vite__mapDeps([4,1,2,5]))},{path:"/library",name:"library",component:()=>cr(()=>import("./LibraryView-CJ89Wqyo.js"),__vite__mapDeps([6,7]))}]}),Uu=cu(),es=Jl(rc);es.use(Uu);es.use(Bu);es.mount("#app");export{vs as A,bu as B,vu as C,wu as D,Pr as E,Le as F,se as G,zu as H,Tu as I,Zu as J,ef as K,vr as L,ze as P,ye as S,z as T,Hu as _,pe as a,Gu as b,oi as c,Kn as d,Fr as e,te as f,Et as g,Yu as h,be as i,vn as j,nn as k,me as l,Ct as m,Lr as n,fn as o,Wu as p,qu as q,Ku as r,Ju as s,Jt as t,Ft as u,Qu as v,Xu as w,_u as x,ko as y,Cr as z};
