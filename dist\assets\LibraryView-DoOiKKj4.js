import{d as V,E as $,i as p,f as m,y as F,c as o,a as e,l as h,m as f,J as E,p as M,q as j,F as y,r as u,t as r,g as v,e as C,w as b,b as B,H as R,o as a,_ as U}from"./index-SVRTPCSQ.js";const q={class:"library-view"},z={class:"library-view__header"},G={class:"library-view__actions"},H={class:"library-view__filters"},I={class:"library-view__search"},J={class:"library-view__filter-tabs"},Q=["onClick"],W={class:"library-view__filter-count"},Y={key:0,class:"library-view__grid"},K=["onClick"],O={class:"library-view__story-header"},P={class:"library-view__story-title"},X={class:"library-view__story-actions"},Z=["onClick"],ee=["onClick"],te=["onClick"],se={class:"library-view__story-meta"},ie={class:"library-view__story-genre"},re={class:"library-view__story-summary"},oe={class:"library-view__story-stats"},ae={class:"library-view__story-stat"},le={class:"library-view__story-stat-value"},ne={class:"library-view__story-stat"},_e={class:"library-view__story-stat-value"},ce={class:"library-view__story-stat"},de={class:"library-view__story-stat-value"},ye={class:"library-view__story-themes"},ue={key:0,class:"library-view__story-theme library-view__story-theme--more"},ve={class:"library-view__story-footer"},be={class:"library-view__story-date"},we={class:"library-view__story-version"},pe={key:1,class:"library-view__empty"},me={class:"library-view__empty-title"},he={class:"library-view__empty-text"},fe=V({__name:"LibraryView",setup(Ce){const g=R(),n=$(),c=p(""),d=p("all"),k=m(()=>[{key:"all",label:"All Stories",count:n.stories.length},{key:"complete",label:"Complete",count:n.completedStories.length},{key:"draft",label:"Drafts",count:n.draftStories.length}]),w=m(()=>{let i=n.stories;if(d.value==="complete"?i=n.completedStories:d.value==="draft"&&(i=n.draftStories),c.value){const t=c.value.toLowerCase();i=i.filter(l=>l.outline.title.toLowerCase().includes(t)||l.outline.summary.toLowerCase().includes(t)||l.outline.themes.some(s=>s.toLowerCase().includes(t))||l.outline.genre.toLowerCase().includes(t))}return i.sort((t,l)=>new Date(l.createdAt).getTime()-new Date(t.createdAt).getTime())});function S(i){g.push(`/story/${i}`)}function x(i){console.log("Export story:",i)}function D(i){console.log("Share story:",i)}function L(i){confirm("Are you sure you want to delete this story? This action cannot be undone.")&&n.deleteStory(i)}function T(i){return i.split("-").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" ")}function A(i){return i.split("-").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" ")}function N(i){return new Date(i).toLocaleDateString()}return F(()=>{n.loadFromStorage()}),(i,t)=>{const l=E("router-link");return a(),o("div",q,[e("div",z,[t[2]||(t[2]=e("div",{class:"library-view__title-section"},[e("h1",{class:"library-view__title"},"Story Library"),e("p",{class:"library-view__subtitle"}," Manage and explore your generated stories ")],-1)),e("div",G,[h(l,{to:"/generator",class:"library-view__create-button"},{default:f(()=>t[1]||(t[1]=[v(" + Create New Story ")])),_:1,__:[1]})])]),e("div",H,[e("div",I,[M(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>c.value=s),type:"text",placeholder:"Search stories...",class:"library-view__search-input"},null,512),[[j,c.value]])]),e("div",J,[(a(!0),o(y,null,u(k.value,s=>(a(),o("button",{key:s.key,class:C(["library-view__filter-tab",{"library-view__filter-tab--active":d.value===s.key}]),onClick:_=>d.value=s.key},[v(r(s.label)+" ",1),e("span",W,r(s.count),1)],10,Q))),128))])]),w.value.length?(a(),o("div",Y,[(a(!0),o(y,null,u(w.value,s=>(a(),o("div",{key:s.id,class:"library-view__story-card",onClick:_=>S(s.id)},[e("div",O,[e("h3",P,r(s.outline.title),1),e("div",X,[e("button",{class:"library-view__story-action",title:"Export",onClick:b(_=>x(s.id),["stop"])}," 📄 ",8,Z),e("button",{class:"library-view__story-action",title:"Share",onClick:b(_=>D(s.id),["stop"])}," 🔗 ",8,ee),e("button",{class:"library-view__story-action library-view__story-action--danger",title:"Delete",onClick:b(_=>L(s.id),["stop"])}," 🗑️ ",8,te)])]),e("div",se,[e("span",ie,r(T(s.outline.genre)),1),e("span",{class:C(["library-view__story-status",`library-view__story-status--${s.isComplete?"complete":"draft"}`])},r(s.isComplete?"Complete":"Draft"),3)]),e("p",re,r(s.outline.summary),1),e("div",oe,[e("div",ae,[t[3]||(t[3]=e("span",{class:"library-view__story-stat-label"},"Characters:",-1)),e("span",le,r(s.outline.characters.length),1)]),e("div",ne,[t[4]||(t[4]=e("span",{class:"library-view__story-stat-label"},"Word Count:",-1)),e("span",_e,r(s.metadata.wordCount),1)]),e("div",ce,[t[5]||(t[5]=e("span",{class:"library-view__story-stat-label"},"Reading Time:",-1)),e("span",de,r(s.outline.estimatedReadingTime)+"m",1)])]),e("div",ye,[(a(!0),o(y,null,u(s.outline.themes.slice(0,3),_=>(a(),o("span",{key:_,class:"library-view__story-theme"},r(A(_)),1))),128)),s.outline.themes.length>3?(a(),o("span",ue," +"+r(s.outline.themes.length-3),1)):B("",!0)]),e("div",ve,[e("span",be,r(N(s.createdAt)),1),e("span",we,"v"+r(s.metadata.version),1)])],8,K))),128))])):(a(),o("div",pe,[t[7]||(t[7]=e("div",{class:"library-view__empty-icon"},"📚",-1)),e("h2",me,r(c.value?"No stories found":"No stories yet"),1),e("p",he,r(c.value?"Try adjusting your search terms or filters":"Create your first story to get started"),1),h(l,{to:"/generator",class:"library-view__empty-button"},{default:f(()=>t[6]||(t[6]=[v(" Create Your First Story ")])),_:1,__:[6]})]))])}}}),ke=U(fe,[["__scopeId","data-v-1e219fbb"]]);export{ke as default};
