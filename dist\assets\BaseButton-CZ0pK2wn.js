import{d as n,c as s,e as d,h as l,o,_ as i}from"./index-Ck4PQcL2.js";const b=["disabled"],r={key:0,class:"base-button__spinner"},u=n({__name:"BaseButton",props:{variant:{default:"primary"},size:{default:"medium"},loading:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},emits:["click"],setup(p){return(e,a)=>(o(),s("button",{class:d(["base-button",`base-button--${e.variant}`,`base-button--${e.size}`,{"base-button--loading":e.loading,"base-button--disabled":e.disabled}]),disabled:e.disabled||e.loading,onClick:a[0]||(a[0]=t=>e.$emit("click",t))},[e.loading?(o(),s("span",r,"⟳")):l(e.$slots,"default",{key:1},void 0)],10,b))}}),f=i(u,[["__scopeId","data-v-e108dbc3"]]);export{f as B};
