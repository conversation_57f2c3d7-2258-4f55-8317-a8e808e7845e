import{d as q,c as n,a as e,b as k,n as he,t as u,o,_ as Y,F as P,r as L,e as M,f as N,g as y,h as ge,w as W,i as O,P as ve,j as te,k as ne,l as h,m as S,p as j,v as Z,q as x,u as Q,s as ie,x as de,y as le,z as re,G as se,S as ae,A as ce,B as ue,T as oe,C as _e,D as pe,E as ye,H as fe,I as be}from"./index-DzbaPud2.js";import{B as w}from"./BaseButton-CRuttqCK.js";const $e={class:"progress-bar"},ke={class:"progress-bar__track"},Ce={key:0,class:"progress-bar__percentage"},Se=q({__name:"ProgressBar",props:{progress:{},showPercentage:{type:Boolean,default:!0}},setup(R){return(i,p)=>(o(),n("div",$e,[e("div",ke,[e("div",{class:"progress-bar__fill",style:he({width:`${i.progress}%`})},null,4)]),i.showPercentage?(o(),n("div",Ce,u(Math.round(i.progress))+"% ",1)):k("",!0)]))}}),Te=Y(Se,[["__scopeId","data-v-1636d37a"]]),we={class:"step-indicator"},Ae=["onClick"],Ie={class:"step-indicator__circle"},Ve={key:0,class:"step-indicator__check"},Ue={key:1,class:"step-indicator__number"},Pe={class:"step-indicator__label"},Le={key:0,class:"step-indicator__connector"},Oe=q({__name:"StepIndicator",props:{steps:{},currentStepIndex:{}},emits:["step-click"],setup(R){return(i,p)=>(o(),n("div",we,[(o(!0),n(P,null,L(i.steps,(a,_)=>(o(),n("div",{key:a.key,class:M(["step-indicator__step",{"step-indicator__step--active":_===i.currentStepIndex,"step-indicator__step--completed":_<i.currentStepIndex,"step-indicator__step--disabled":_>i.currentStepIndex}]),onClick:d=>i.$emit("step-click",a.key,_)},[e("div",Ie,[_<i.currentStepIndex?(o(),n("span",Ve,"✓")):(o(),n("span",Ue,u(_+1),1))]),e("div",Pe,u(a.label),1),_<i.steps.length-1?(o(),n("div",Le)):k("",!0)],10,Ae))),128))]))}}),De=Y(Oe,[["__scopeId","data-v-c411deaf"]]),Be={class:"base-input"},Ee=["for"],Ne={key:0,class:"base-input__required"},Me={class:"base-input__wrapper"},Ge=["id","type","value","placeholder","disabled","required"],Fe={key:0,class:"base-input__suffix"},Re={key:1,class:"base-input__message"},je={key:0,class:"base-input__error"},ze={key:1,class:"base-input__hint"},Ke=q({__name:"BaseInput",props:{modelValue:{},label:{},type:{default:"text"},placeholder:{},disabled:{type:Boolean,default:!1},required:{type:Boolean,default:!1},error:{},hint:{}},emits:["update:modelValue","blur","focus"],setup(R,{emit:i}){const p=N(()=>`input-${Math.random().toString(36).substr(2,9)}`);return(a,_)=>(o(),n("div",Be,[a.label?(o(),n("label",{key:0,for:p.value,class:"base-input__label"},[y(u(a.label)+" ",1),a.required?(o(),n("span",Ne,"*")):k("",!0)],8,Ee)):k("",!0),e("div",Me,[e("input",{id:p.value,type:a.type,value:a.modelValue,placeholder:a.placeholder,disabled:a.disabled,required:a.required,class:M(["base-input__field",{"base-input__field--error":a.error,"base-input__field--disabled":a.disabled}]),onInput:_[0]||(_[0]=d=>a.$emit("update:modelValue",d.target.value)),onBlur:_[1]||(_[1]=d=>a.$emit("blur",d)),onFocus:_[2]||(_[2]=d=>a.$emit("focus",d))},null,42,Ge),a.$slots.suffix?(o(),n("div",Fe,[ge(a.$slots,"suffix",{},void 0)])):k("",!0)]),a.error||a.hint?(o(),n("div",Re,[a.error?(o(),n("span",je,u(a.error),1)):a.hint?(o(),n("span",ze,u(a.hint),1)):k("",!0)])):k("",!0)]))}}),J=Y(Ke,[["__scopeId","data-v-8dec60e3"]]),We={class:"character-card__header"},qe={class:"character-card__info"},Ye={class:"character-card__name"},He={class:"character-card__meta"},Je={key:0,class:"character-card__age"},Qe={class:"character-card__actions"},Xe={key:0,class:"character-card__action",title:"Template"},Ze={class:"character-card__description"},xe={key:0,class:"character-card__traits"},et={key:0,class:"character-card__trait character-card__trait--more"},tt={key:1,class:"character-card__goals"},st={class:"character-card__list"},at={key:0,class:"character-card__list-item character-card__list-item--more"},ot={key:2,class:"character-card__flaws"},nt={class:"character-card__list"},it={key:0,class:"character-card__list-item character-card__list-item--more"},lt={key:3,class:"character-card__selected-indicator"},rt=q({__name:"CharacterCard",props:{character:{},isSelected:{type:Boolean}},emits:["select","edit","duplicate","delete"],setup(R){function i(a){return a.charAt(0).toUpperCase()+a.slice(1)}function p(a){return a.split("-").map(_=>_.charAt(0).toUpperCase()+_.slice(1)).join(" ")}return(a,_)=>(o(),n("div",{class:M(["character-card",{"character-card--selected":a.isSelected,"character-card--template":a.character.isTemplate}]),onClick:_[3]||(_[3]=d=>a.$emit("select",a.character))},[e("div",We,[e("div",qe,[e("h3",Ye,u(a.character.name),1),e("div",He,[e("span",{class:M(["character-card__role",`character-card__role--${a.character.role}`])},u(i(a.character.role)),3),a.character.age?(o(),n("span",Je," Age "+u(a.character.age),1)):k("",!0)])]),e("div",Qe,[a.character.isTemplate?(o(),n("button",Xe," 📋 ")):k("",!0),e("button",{class:"character-card__action",title:"Edit",onClick:_[0]||(_[0]=W(d=>a.$emit("edit",a.character),["stop"]))}," ✏️ "),e("button",{class:"character-card__action",title:"Duplicate",onClick:_[1]||(_[1]=W(d=>a.$emit("duplicate",a.character.id),["stop"]))}," 📄 "),e("button",{class:"character-card__action character-card__action--danger",title:"Delete",onClick:_[2]||(_[2]=W(d=>a.$emit("delete",a.character.id),["stop"]))}," 🗑️ ")])]),e("p",Ze,u(a.character.description),1),a.character.personalityTraits.length?(o(),n("div",xe,[(o(!0),n(P,null,L(a.character.personalityTraits.slice(0,3),d=>(o(),n("span",{key:d,class:"character-card__trait"},u(p(d)),1))),128)),a.character.personalityTraits.length>3?(o(),n("span",et," +"+u(a.character.personalityTraits.length-3)+" more ",1)):k("",!0)])):k("",!0),a.character.goals.length?(o(),n("div",tt,[_[4]||(_[4]=e("h4",{class:"character-card__section-title"},"Goals:",-1)),e("ul",st,[(o(!0),n(P,null,L(a.character.goals.slice(0,2),d=>(o(),n("li",{key:d,class:"character-card__list-item"},u(d),1))),128)),a.character.goals.length>2?(o(),n("li",at," +"+u(a.character.goals.length-2)+" more goals ",1)):k("",!0)])])):k("",!0),a.character.flaws.length?(o(),n("div",ot,[_[5]||(_[5]=e("h4",{class:"character-card__section-title"},"Flaws:",-1)),e("ul",nt,[(o(!0),n(P,null,L(a.character.flaws.slice(0,2),d=>(o(),n("li",{key:d,class:"character-card__list-item"},u(d),1))),128)),a.character.flaws.length>2?(o(),n("li",it," +"+u(a.character.flaws.length-2)+" more flaws ",1)):k("",!0)])])):k("",!0),a.isSelected?(o(),n("div",lt," ✓ Selected ")):k("",!0)],2))}}),ct=Y(rt,[["__scopeId","data-v-14b101ac"]]),dt={class:"character-builder"},ut={class:"character-builder__header"},_t={class:"character-builder__title"},pt={class:"character-builder__section"},mt={class:"character-builder__row"},ht={class:"base-input"},gt={class:"base-input"},vt={class:"character-builder__section"},yt={class:"character-builder__traits"},ft=["value","checked","onChange"],bt={class:"character-builder__section"},$t={class:"character-builder__list"},kt={class:"character-builder__section"},Ct={class:"character-builder__list"},St={class:"character-builder__section"},Tt={class:"base-input"},wt={class:"base-input"},At={class:"character-builder__checkbox"},It={class:"character-builder__actions"},Vt=q({__name:"CharacterBuilder",props:{editingCharacter:{}},emits:["close","submit"],setup(R,{emit:i}){const p=R,a=i,_=O(!1),d=Object.values(ve),s=te({name:"",age:"",description:"",personalityTraits:[],goals:[""],flaws:[""],backstory:"",appearance:"",role:"supporting",isTemplate:!1}),t=te({name:"",age:""});ne(()=>p.editingCharacter,r=>{r?(s.name=r.name,s.age=r.age?.toString()||"",s.description=r.description,s.personalityTraits=[...r.personalityTraits],s.goals=r.goals.length?[...r.goals]:[""],s.flaws=r.flaws.length?[...r.flaws]:[""],s.backstory=r.backstory||"",s.appearance=r.appearance||"",s.role=r.role,s.isTemplate=r.isTemplate):f()},{immediate:!0});function f(){s.name="",s.age="",s.description="",s.personalityTraits=[],s.goals=[""],s.flaws=[""],s.backstory="",s.appearance="",s.role="supporting",s.isTemplate=!1,t.name="",t.age=""}function G(r){const c=s.personalityTraits.indexOf(r);c>-1?s.personalityTraits.splice(c,1):s.personalityTraits.push(r)}function D(r){return r.split("-").map(c=>c.charAt(0).toUpperCase()+c.slice(1)).join(" ")}function F(){s.goals.push("")}function B(r,c){s.goals[r]=c}function z(r){s.goals.length>1&&s.goals.splice(r,1)}function C(){s.flaws.push("")}function m(r,c){s.flaws[r]=c}function A(r){s.flaws.length>1&&s.flaws.splice(r,1)}function g(){return t.name="",t.age="",s.name.trim()?s.age&&(isNaN(Number(s.age))||Number(s.age)<0)?(t.age="Age must be a valid number",!1):!0:(t.name="Character name is required",!1)}async function l(){if(g()){_.value=!0;try{const r={name:s.name.trim(),age:s.age?Number(s.age):void 0,description:s.description.trim(),personalityTraits:s.personalityTraits,goals:s.goals.filter(c=>c.trim()),flaws:s.flaws.filter(c=>c.trim()),backstory:s.backstory.trim()||void 0,appearance:s.appearance.trim()||void 0,role:s.role,isTemplate:s.isTemplate};a("submit",r)}finally{_.value=!1}}}return(r,c)=>(o(),n("div",dt,[e("div",ut,[e("h2",_t,u(r.editingCharacter?"Edit Character":"Create New Character"),1),h(w,{variant:"ghost",size:"small",onClick:c[0]||(c[0]=$=>r.$emit("close"))},{default:S(()=>c[9]||(c[9]=[y(" ✕ ")])),_:1,__:[9]})]),e("form",{onSubmit:W(l,["prevent"]),class:"character-builder__form"},[e("div",pt,[c[13]||(c[13]=e("h3",{class:"character-builder__section-title"},"Basic Information",-1)),h(J,{modelValue:s.name,"onUpdate:modelValue":c[1]||(c[1]=$=>s.name=$),label:"Character Name",placeholder:"Enter character name",required:"",error:t.name},null,8,["modelValue","error"]),e("div",mt,[h(J,{modelValue:s.age,"onUpdate:modelValue":c[2]||(c[2]=$=>s.age=$),label:"Age",type:"number",placeholder:"25",error:t.age},null,8,["modelValue","error"]),e("div",ht,[c[11]||(c[11]=e("label",{class:"base-input__label"},"Role",-1)),j(e("select",{"onUpdate:modelValue":c[3]||(c[3]=$=>s.role=$),class:"character-builder__select"},c[10]||(c[10]=[e("option",{value:"protagonist"},"Protagonist",-1),e("option",{value:"antagonist"},"Antagonist",-1),e("option",{value:"supporting"},"Supporting",-1),e("option",{value:"minor"},"Minor",-1)]),512),[[Z,s.role]])])]),e("div",gt,[c[12]||(c[12]=e("label",{class:"base-input__label"},"Description",-1)),j(e("textarea",{"onUpdate:modelValue":c[4]||(c[4]=$=>s.description=$),placeholder:"Describe your character...",class:"character-builder__textarea",rows:"3"},null,512),[[x,s.description]])])]),e("div",vt,[c[14]||(c[14]=e("h3",{class:"character-builder__section-title"},"Personality Traits",-1)),e("div",yt,[(o(!0),n(P,null,L(Q(d),$=>(o(),n("label",{key:$,class:"character-builder__trait"},[e("input",{type:"checkbox",value:$,checked:s.personalityTraits.includes($),onChange:E=>G($)},null,40,ft),e("span",null,u(D($)),1)]))),128))])]),e("div",bt,[c[17]||(c[17]=e("h3",{class:"character-builder__section-title"},"Goals",-1)),e("div",$t,[(o(!0),n(P,null,L(s.goals,($,E)=>(o(),n("div",{key:E,class:"character-builder__list-item"},[h(J,{"model-value":$,placeholder:"Enter a goal...","onUpdate:modelValue":H=>B(E,H)},null,8,["model-value","onUpdate:modelValue"]),h(w,{variant:"ghost",size:"small",onClick:H=>z(E)},{default:S(()=>c[15]||(c[15]=[y(" ✕ ")])),_:2,__:[15]},1032,["onClick"])]))),128)),h(w,{variant:"ghost",size:"small",onClick:F},{default:S(()=>c[16]||(c[16]=[y(" + Add Goal ")])),_:1,__:[16]})])]),e("div",kt,[c[20]||(c[20]=e("h3",{class:"character-builder__section-title"},"Flaws",-1)),e("div",Ct,[(o(!0),n(P,null,L(s.flaws,($,E)=>(o(),n("div",{key:E,class:"character-builder__list-item"},[h(J,{"model-value":$,placeholder:"Enter a flaw...","onUpdate:modelValue":H=>m(E,H)},null,8,["model-value","onUpdate:modelValue"]),h(w,{variant:"ghost",size:"small",onClick:H=>A(E)},{default:S(()=>c[18]||(c[18]=[y(" ✕ ")])),_:2,__:[18]},1032,["onClick"])]))),128)),h(w,{variant:"ghost",size:"small",onClick:C},{default:S(()=>c[19]||(c[19]=[y(" + Add Flaw ")])),_:1,__:[19]})])]),e("div",St,[c[24]||(c[24]=e("h3",{class:"character-builder__section-title"},"Optional Details",-1)),e("div",Tt,[c[21]||(c[21]=e("label",{class:"base-input__label"},"Backstory",-1)),j(e("textarea",{"onUpdate:modelValue":c[5]||(c[5]=$=>s.backstory=$),placeholder:"Character's background and history...",class:"character-builder__textarea",rows:"3"},null,512),[[x,s.backstory]])]),e("div",wt,[c[22]||(c[22]=e("label",{class:"base-input__label"},"Appearance",-1)),j(e("textarea",{"onUpdate:modelValue":c[6]||(c[6]=$=>s.appearance=$),placeholder:"Physical description...",class:"character-builder__textarea",rows:"2"},null,512),[[x,s.appearance]])]),e("div",At,[e("label",null,[j(e("input",{type:"checkbox","onUpdate:modelValue":c[7]||(c[7]=$=>s.isTemplate=$)},null,512),[[ie,s.isTemplate]]),c[23]||(c[23]=e("span",null,"Save as template for future use",-1))])])]),e("div",It,[h(w,{variant:"secondary",onClick:c[8]||(c[8]=$=>r.$emit("close"))},{default:S(()=>c[25]||(c[25]=[y(" Cancel ")])),_:1,__:[25]}),h(w,{type:"submit",loading:_.value},{default:S(()=>[y(u(r.editingCharacter?"Update Character":"Create Character"),1)]),_:1},8,["loading"])])],32)]))}}),Ut=Y(Vt,[["__scopeId","data-v-3b9b7624"]]),Pt={class:"character-selection"},Lt={class:"character-selection__header"},Ot={class:"character-selection__actions"},Dt={class:"character-selection__filters"},Bt={class:"character-selection__filter-tabs"},Et=["onClick"],Nt={class:"character-selection__filter-count"},Mt={key:0,class:"character-selection__selected"},Gt={class:"character-selection__selected-title"},Ft={class:"character-selection__selected-list"},Rt={class:"character-selection__selected-name"},jt={class:"character-selection__selected-role"},zt=["onClick"],Kt={class:"character-selection__grid"},Wt={key:0,class:"character-selection__empty"},qt={class:"character-selection__empty-text"},Yt=q({__name:"CharacterSelection",setup(R){const i=de(),p=O(""),a=O("all"),_=O(!1),d=O(null),s=N(()=>[{key:"all",label:"All",count:i.characters.length},{key:"protagonists",label:"Protagonists",count:i.protagonists.length},{key:"antagonists",label:"Antagonists",count:i.antagonists.length},{key:"supporting",label:"Supporting",count:i.supportingCharacters.length},{key:"templates",label:"Templates",count:i.templates.length}]),t=N(()=>{let r=i.characters;return a.value==="protagonists"?r=i.protagonists:a.value==="antagonists"?r=i.antagonists:a.value==="supporting"?r=i.supportingCharacters:a.value==="templates"&&(r=i.templates),p.value&&(r=i.searchCharacters(p.value)),r}),f=N(()=>i.selectedCharacters);function G(r){return f.value.some(c=>c.id===r)}function D(r){G(r.id)?i.deselectCharacter(r.id):i.selectCharacter(r)}function F(r){i.deselectCharacter(r)}function B(){i.clearSelectedCharacters()}function z(r){d.value=r,_.value=!0}function C(r){i.duplicateCharacter(r)}function m(r){confirm("Are you sure you want to delete this character?")&&i.deleteCharacter(r)}function A(){i.generateRandomCharacter()}function g(){_.value=!1,d.value=null}function l(r){d.value?i.updateCharacter(d.value.id,r):i.addCharacter(r),g()}return le(()=>{i.loadFromStorage()}),(r,c)=>(o(),n("div",Pt,[e("div",Lt,[c[5]||(c[5]=e("div",{class:"character-selection__title-section"},[e("h2",{class:"character-selection__title"},"Choose Your Characters"),e("p",{class:"character-selection__subtitle"}," Select characters for your story or create new ones ")],-1)),e("div",Ot,[h(w,{variant:"ghost",size:"small",onClick:A},{default:S(()=>c[3]||(c[3]=[y(" 🎲 Random Character ")])),_:1,__:[3]}),h(w,{variant:"primary",onClick:c[0]||(c[0]=$=>_.value=!0)},{default:S(()=>c[4]||(c[4]=[y(" + Create Character ")])),_:1,__:[4]})])]),e("div",Dt,[h(J,{modelValue:p.value,"onUpdate:modelValue":c[1]||(c[1]=$=>p.value=$),placeholder:"Search characters...",class:"character-selection__search"},{suffix:S(()=>c[6]||(c[6]=[y(" 🔍 ")])),_:1},8,["modelValue"]),e("div",Bt,[(o(!0),n(P,null,L(s.value,$=>(o(),n("button",{key:$.key,class:M(["character-selection__filter-tab",{"character-selection__filter-tab--active":a.value===$.key}]),onClick:E=>a.value=$.key},[y(u($.label)+" ",1),e("span",Nt,u($.count),1)],10,Et))),128))])]),f.value.length?(o(),n("div",Mt,[e("h3",Gt," Selected Characters ("+u(f.value.length)+") ",1),e("div",Ft,[(o(!0),n(P,null,L(f.value,$=>(o(),n("div",{key:$.id,class:"character-selection__selected-item"},[e("span",Rt,u($.name),1),e("span",jt,u($.role),1),e("button",{class:"character-selection__selected-remove",onClick:E=>F($.id)}," ✕ ",8,zt)]))),128))]),h(w,{variant:"ghost",size:"small",onClick:B},{default:S(()=>c[7]||(c[7]=[y(" Clear All ")])),_:1,__:[7]})])):k("",!0),e("div",Kt,[(o(!0),n(P,null,L(t.value,$=>(o(),re(ct,{key:$.id,character:$,"is-selected":G($.id),onSelect:D,onEdit:z,onDuplicate:C,onDelete:m},null,8,["character","is-selected"]))),128)),t.value.length===0?(o(),n("div",Wt,[c[9]||(c[9]=e("div",{class:"character-selection__empty-icon"},"👤",-1)),c[10]||(c[10]=e("h3",{class:"character-selection__empty-title"},"No characters found",-1)),e("p",qt,u(p.value?"Try adjusting your search terms":"Create your first character to get started"),1),h(w,{variant:"primary",onClick:c[2]||(c[2]=$=>_.value=!0)},{default:S(()=>c[8]||(c[8]=[y(" Create Character ")])),_:1,__:[8]})])):k("",!0)]),_.value?(o(),n("div",{key:1,class:"character-selection__modal",onClick:W(g,["self"])},[h(Ut,{"editing-character":d.value,onClose:g,onSubmit:l},null,8,["editing-character"])])):k("",!0)]))}}),Ht=Y(Yt,[["__scopeId","data-v-6fc75cee"]]),Jt={class:"setting-card__image"},Qt={class:"setting-card__genre-badge"},Xt={class:"setting-card__content"},Zt={class:"setting-card__header"},xt={class:"setting-card__info"},es={class:"setting-card__name"},ts={class:"setting-card__meta"},ss={class:"setting-card__type"},as={key:0,class:"setting-card__time"},os={class:"setting-card__actions"},ns={key:0,class:"setting-card__action",title:"Template"},is={class:"setting-card__description"},ls={class:"setting-card__atmosphere"},rs={key:0,class:"setting-card__locations"},cs={class:"setting-card__location-tags"},ds={key:0,class:"setting-card__location-tag setting-card__location-tag--more"},us={class:"setting-card__details"},_s={key:0,class:"setting-card__detail"},ps={class:"setting-card__detail-value"},ms={key:1,class:"setting-card__detail"},hs={class:"setting-card__detail-value"},gs={key:0,class:"setting-card__selected-indicator"},vs=q({__name:"SettingCard",props:{setting:{},isSelected:{type:Boolean}},emits:["select","edit","duplicate","delete"],setup(R){function i(s){return{urban:"🏙️",rural:"🌾",wilderness:"🌲",underground:"🕳️",space:"🚀",underwater:"🌊","magical-realm":"✨","dystopian-city":"🏭","small-town":"🏘️",castle:"🏰",spaceship:"🛸",desert:"🏜️",forest:"🌳",mountain:"⛰️",island:"🏝️"}[s]||"🌍"}function p(s){return s.split("-").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" ")}function a(s){return s.split("-").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(" ")}function _(s){return s.charAt(0).toUpperCase()+s.slice(1)}function d(s){return s.charAt(0).toUpperCase()+s.slice(1)}return(s,t)=>(o(),n("div",{class:M(["setting-card",{"setting-card--selected":s.isSelected,"setting-card--template":s.setting.isTemplate}]),onClick:t[3]||(t[3]=f=>s.$emit("select",s.setting))},[e("div",Jt,[e("div",{class:M(`setting-card__image-placeholder setting-card__image-placeholder--${s.setting.type}`)},u(i(s.setting.type)),3),e("div",Qt,u(p(s.setting.genre)),1)]),e("div",Xt,[e("div",Zt,[e("div",xt,[e("h3",es,u(s.setting.name),1),e("div",ts,[e("span",ss,u(a(s.setting.type)),1),s.setting.timeOfDay?(o(),n("span",as,u(_(s.setting.timeOfDay)),1)):k("",!0)])]),e("div",os,[s.setting.isTemplate?(o(),n("button",ns," 📋 ")):k("",!0),e("button",{class:"setting-card__action",title:"Edit",onClick:t[0]||(t[0]=W(f=>s.$emit("edit",s.setting),["stop"]))}," ✏️ "),e("button",{class:"setting-card__action",title:"Duplicate",onClick:t[1]||(t[1]=W(f=>s.$emit("duplicate",s.setting.id),["stop"]))}," 📄 "),e("button",{class:"setting-card__action setting-card__action--danger",title:"Delete",onClick:t[2]||(t[2]=W(f=>s.$emit("delete",s.setting.id),["stop"]))}," 🗑️ ")])]),e("p",is,u(s.setting.description),1),e("div",ls,[t[4]||(t[4]=e("strong",null,"Atmosphere:",-1)),y(" "+u(s.setting.atmosphere),1)]),s.setting.keyLocations.length?(o(),n("div",rs,[t[5]||(t[5]=e("h4",{class:"setting-card__section-title"},"Key Locations:",-1)),e("div",cs,[(o(!0),n(P,null,L(s.setting.keyLocations.slice(0,3),f=>(o(),n("span",{key:f,class:"setting-card__location-tag"},u(f),1))),128)),s.setting.keyLocations.length>3?(o(),n("span",ds," +"+u(s.setting.keyLocations.length-3)+" more ",1)):k("",!0)])])):k("",!0),e("div",us,[s.setting.weather?(o(),n("div",_s,[t[6]||(t[6]=e("span",{class:"setting-card__detail-label"},"Weather:",-1)),e("span",ps,u(s.setting.weather),1)])):k("",!0),s.setting.season?(o(),n("div",ms,[t[7]||(t[7]=e("span",{class:"setting-card__detail-label"},"Season:",-1)),e("span",hs,u(d(s.setting.season)),1)])):k("",!0)])]),s.isSelected?(o(),n("div",gs," ✓ Selected ")):k("",!0)],2))}}),ys=Y(vs,[["__scopeId","data-v-7846e921"]]),fs={class:"setting-builder"},bs={class:"setting-builder__header"},$s={class:"setting-builder__title"},ks={class:"setting-builder__section"},Cs={class:"setting-builder__row"},Ss={class:"base-input"},Ts=["value"],ws={class:"base-input"},As=["value"],Is={class:"base-input"},Vs={class:"base-input"},Us={class:"setting-builder__section"},Ps={class:"setting-builder__list"},Ls={class:"setting-builder__section"},Os={class:"setting-builder__row"},Ds={class:"base-input"},Bs={class:"base-input"},Es={class:"setting-builder__checkbox"},Ns={class:"setting-builder__actions"},Ms=q({__name:"SettingBuilder",props:{editingSetting:{}},emits:["close","submit"],setup(R,{emit:i}){const p=R,a=i,_=O(!1),d=Object.values(se),s=Object.values(ae),t=te({name:"",genre:se.FANTASY,type:ae.FOREST,description:"",atmosphere:"",keyLocations:[""],timeOfDay:"",weather:"",season:"",isTemplate:!1}),f=te({name:""});ne(()=>p.editingSetting,g=>{g?(t.name=g.name,t.genre=g.genre,t.type=g.type,t.description=g.description,t.atmosphere=g.atmosphere,t.keyLocations=g.keyLocations.length?[...g.keyLocations]:[""],t.timeOfDay=g.timeOfDay||"",t.weather=g.weather||"",t.season=g.season||"",t.isTemplate=g.isTemplate):G()},{immediate:!0});function G(){t.name="",t.genre=se.FANTASY,t.type=ae.FOREST,t.description="",t.atmosphere="",t.keyLocations=[""],t.timeOfDay="",t.weather="",t.season="",t.isTemplate=!1,f.name=""}function D(g){return g.split("-").map(l=>l.charAt(0).toUpperCase()+l.slice(1)).join(" ")}function F(g){return g.split("-").map(l=>l.charAt(0).toUpperCase()+l.slice(1)).join(" ")}function B(){t.keyLocations.push("")}function z(g,l){t.keyLocations[g]=l}function C(g){t.keyLocations.length>1&&t.keyLocations.splice(g,1)}function m(){return f.name="",t.name.trim()?!0:(f.name="Setting name is required",!1)}async function A(){if(m()){_.value=!0;try{const g={name:t.name.trim(),genre:t.genre,type:t.type,description:t.description.trim(),atmosphere:t.atmosphere.trim(),keyLocations:t.keyLocations.filter(l=>l.trim()),timeOfDay:t.timeOfDay||void 0,weather:t.weather.trim()||void 0,season:t.season||void 0,isTemplate:t.isTemplate};a("submit",g)}finally{_.value=!1}}}return(g,l)=>(o(),n("div",fs,[e("div",bs,[e("h2",$s,u(g.editingSetting?"Edit Setting":"Create New Setting"),1),h(w,{variant:"ghost",size:"small",onClick:l[0]||(l[0]=r=>g.$emit("close"))},{default:S(()=>l[11]||(l[11]=[y(" ✕ ")])),_:1,__:[11]})]),e("form",{onSubmit:W(A,["prevent"]),class:"setting-builder__form"},[e("div",ks,[l[16]||(l[16]=e("h3",{class:"setting-builder__section-title"},"Basic Information",-1)),h(J,{modelValue:t.name,"onUpdate:modelValue":l[1]||(l[1]=r=>t.name=r),label:"Setting Name",placeholder:"Enter setting name",required:"",error:f.name},null,8,["modelValue","error"]),e("div",Cs,[e("div",Ss,[l[12]||(l[12]=e("label",{class:"base-input__label"},"Genre",-1)),j(e("select",{"onUpdate:modelValue":l[2]||(l[2]=r=>t.genre=r),class:"setting-builder__select"},[(o(!0),n(P,null,L(Q(d),r=>(o(),n("option",{key:r,value:r},u(D(r)),9,Ts))),128))],512),[[Z,t.genre]])]),e("div",ws,[l[13]||(l[13]=e("label",{class:"base-input__label"},"Type",-1)),j(e("select",{"onUpdate:modelValue":l[3]||(l[3]=r=>t.type=r),class:"setting-builder__select"},[(o(!0),n(P,null,L(Q(s),r=>(o(),n("option",{key:r,value:r},u(F(r)),9,As))),128))],512),[[Z,t.type]])])]),e("div",Is,[l[14]||(l[14]=e("label",{class:"base-input__label"},"Description",-1)),j(e("textarea",{"onUpdate:modelValue":l[4]||(l[4]=r=>t.description=r),placeholder:"Describe your setting...",class:"setting-builder__textarea",rows:"3"},null,512),[[x,t.description]])]),e("div",Vs,[l[15]||(l[15]=e("label",{class:"base-input__label"},"Atmosphere",-1)),j(e("textarea",{"onUpdate:modelValue":l[5]||(l[5]=r=>t.atmosphere=r),placeholder:"Describe the mood and feeling of this place...",class:"setting-builder__textarea",rows:"2"},null,512),[[x,t.atmosphere]])])]),e("div",Us,[l[19]||(l[19]=e("h3",{class:"setting-builder__section-title"},"Key Locations",-1)),e("div",Ps,[(o(!0),n(P,null,L(t.keyLocations,(r,c)=>(o(),n("div",{key:c,class:"setting-builder__list-item"},[h(J,{"model-value":r,placeholder:"Enter a location...","onUpdate:modelValue":$=>z(c,$)},null,8,["model-value","onUpdate:modelValue"]),h(w,{variant:"ghost",size:"small",onClick:$=>C(c)},{default:S(()=>l[17]||(l[17]=[y(" ✕ ")])),_:2,__:[17]},1032,["onClick"])]))),128)),h(w,{variant:"ghost",size:"small",onClick:B},{default:S(()=>l[18]||(l[18]=[y(" + Add Location ")])),_:1,__:[18]})])]),e("div",Ls,[l[25]||(l[25]=e("h3",{class:"setting-builder__section-title"},"Environmental Details",-1)),e("div",Os,[e("div",Ds,[l[21]||(l[21]=e("label",{class:"base-input__label"},"Time of Day",-1)),j(e("select",{"onUpdate:modelValue":l[6]||(l[6]=r=>t.timeOfDay=r),class:"setting-builder__select"},l[20]||(l[20]=[ce('<option value="" data-v-c57cbcd3>Not specified</option><option value="dawn" data-v-c57cbcd3>Dawn</option><option value="morning" data-v-c57cbcd3>Morning</option><option value="noon" data-v-c57cbcd3>Noon</option><option value="afternoon" data-v-c57cbcd3>Afternoon</option><option value="evening" data-v-c57cbcd3>Evening</option><option value="night" data-v-c57cbcd3>Night</option><option value="midnight" data-v-c57cbcd3>Midnight</option>',8)]),512),[[Z,t.timeOfDay]])]),e("div",Bs,[l[23]||(l[23]=e("label",{class:"base-input__label"},"Season",-1)),j(e("select",{"onUpdate:modelValue":l[7]||(l[7]=r=>t.season=r),class:"setting-builder__select"},l[22]||(l[22]=[ce('<option value="" data-v-c57cbcd3>Not specified</option><option value="spring" data-v-c57cbcd3>Spring</option><option value="summer" data-v-c57cbcd3>Summer</option><option value="autumn" data-v-c57cbcd3>Autumn</option><option value="winter" data-v-c57cbcd3>Winter</option>',5)]),512),[[Z,t.season]])])]),h(J,{modelValue:t.weather,"onUpdate:modelValue":l[8]||(l[8]=r=>t.weather=r),label:"Weather",placeholder:"e.g., Sunny, Rainy, Stormy, Misty"},null,8,["modelValue"]),e("div",Es,[e("label",null,[j(e("input",{type:"checkbox","onUpdate:modelValue":l[9]||(l[9]=r=>t.isTemplate=r)},null,512),[[ie,t.isTemplate]]),l[24]||(l[24]=e("span",null,"Save as template for future use",-1))])])]),e("div",Ns,[h(w,{variant:"secondary",onClick:l[10]||(l[10]=r=>g.$emit("close"))},{default:S(()=>l[26]||(l[26]=[y(" Cancel ")])),_:1,__:[26]}),h(w,{type:"submit",loading:_.value},{default:S(()=>[y(u(g.editingSetting?"Update Setting":"Create Setting"),1)]),_:1},8,["loading"])])],32)]))}}),Gs=Y(Ms,[["__scopeId","data-v-c57cbcd3"]]),Fs={class:"setting-selection"},Rs={class:"setting-selection__header"},js={class:"setting-selection__actions"},zs={class:"setting-selection__filters"},Ks={class:"setting-selection__filter-tabs"},Ws=["onClick"],qs={class:"setting-selection__filter-count"},Ys={class:"setting-selection__genre-filters"},Hs=["onClick"],Js={key:0,class:"setting-selection__selected"},Qs={class:"setting-selection__selected-title"},Xs={class:"setting-selection__selected-list"},Zs={class:"setting-selection__selected-name"},xs={class:"setting-selection__selected-type"},ea=["onClick"],ta={class:"setting-selection__grid"},sa={key:0,class:"setting-selection__empty"},aa={class:"setting-selection__empty-text"},oa=q({__name:"SettingSelection",setup(R){const i=ue(),p=O(""),a=O("all"),_=O(null),d=O(!1),s=O(null),t=Object.values(se),f=N(()=>[{key:"all",label:"All",count:i.settings.length},{key:"templates",label:"Templates",count:i.templates.length}]),G=N(()=>{let I=i.settings;return a.value==="templates"&&(I=i.templates),_.value&&(I=I.filter(V=>V.genre===_.value)),p.value&&(I=i.searchSettings(p.value)),I}),D=N(()=>i.selectedSettings);function F(I){return D.value.some(V=>V.id===I)}function B(I){F(I.id)?i.deselectSetting(I.id):i.selectSetting(I)}function z(I){i.deselectSetting(I)}function C(){i.clearSelectedSettings()}function m(I){s.value=I,d.value=!0}function A(I){i.duplicateSetting(I)}function g(I){confirm("Are you sure you want to delete this setting?")&&i.deleteSetting(I)}function l(){i.generateRandomSetting()}function r(I){_.value=_.value===I?null:I}function c(I){return I.split("-").map(V=>V.charAt(0).toUpperCase()+V.slice(1)).join(" ")}function $(I){return I.split("-").map(V=>V.charAt(0).toUpperCase()+V.slice(1)).join(" ")}function E(){d.value=!1,s.value=null}function H(I){s.value?i.updateSetting(s.value.id,I):i.addSetting(I),E()}return le(()=>{i.loadFromStorage()}),(I,V)=>(o(),n("div",Fs,[e("div",Rs,[V[5]||(V[5]=e("div",{class:"setting-selection__title-section"},[e("h2",{class:"setting-selection__title"},"Choose Your Settings"),e("p",{class:"setting-selection__subtitle"}," Select the worlds and locations where your story takes place ")],-1)),e("div",js,[h(w,{variant:"ghost",size:"small",onClick:l},{default:S(()=>V[3]||(V[3]=[y(" 🎲 Random Setting ")])),_:1,__:[3]}),h(w,{variant:"primary",onClick:V[0]||(V[0]=v=>d.value=!0)},{default:S(()=>V[4]||(V[4]=[y(" + Create Setting ")])),_:1,__:[4]})])]),e("div",zs,[h(J,{modelValue:p.value,"onUpdate:modelValue":V[1]||(V[1]=v=>p.value=v),placeholder:"Search settings...",class:"setting-selection__search"},{suffix:S(()=>V[6]||(V[6]=[y(" 🔍 ")])),_:1},8,["modelValue"]),e("div",Ks,[(o(!0),n(P,null,L(f.value,v=>(o(),n("button",{key:v.key,class:M(["setting-selection__filter-tab",{"setting-selection__filter-tab--active":a.value===v.key}]),onClick:T=>a.value=v.key},[y(u(v.label)+" ",1),e("span",qs,u(v.count),1)],10,Ws))),128))]),e("div",Ys,[(o(!0),n(P,null,L(Q(t),v=>(o(),n("button",{key:v,class:M(["setting-selection__genre-filter",{"setting-selection__genre-filter--active":_.value===v}]),onClick:T=>r(v)},u(c(v)),11,Hs))),128))])]),D.value.length?(o(),n("div",Js,[e("h3",Qs," Selected Settings ("+u(D.value.length)+") ",1),e("div",Xs,[(o(!0),n(P,null,L(D.value,v=>(o(),n("div",{key:v.id,class:"setting-selection__selected-item"},[e("span",Zs,u(v.name),1),e("span",xs,u($(v.type)),1),e("button",{class:"setting-selection__selected-remove",onClick:T=>z(v.id)}," ✕ ",8,ea)]))),128))]),h(w,{variant:"ghost",size:"small",onClick:C},{default:S(()=>V[7]||(V[7]=[y(" Clear All ")])),_:1,__:[7]})])):k("",!0),e("div",ta,[(o(!0),n(P,null,L(G.value,v=>(o(),re(ys,{key:v.id,setting:v,"is-selected":F(v.id),onSelect:B,onEdit:m,onDuplicate:A,onDelete:g},null,8,["setting","is-selected"]))),128)),G.value.length===0?(o(),n("div",sa,[V[9]||(V[9]=e("div",{class:"setting-selection__empty-icon"},"🌍",-1)),V[10]||(V[10]=e("h3",{class:"setting-selection__empty-title"},"No settings found",-1)),e("p",aa,u(p.value?"Try adjusting your search terms":"Create your first setting to get started"),1),h(w,{variant:"primary",onClick:V[2]||(V[2]=v=>d.value=!0)},{default:S(()=>V[8]||(V[8]=[y(" Create Setting ")])),_:1,__:[8]})])):k("",!0)]),d.value?(o(),n("div",{key:1,class:"setting-selection__modal",onClick:W(E,["self"])},[h(Gs,{"editing-setting":s.value,onClose:E,onSubmit:H},null,8,["editing-setting"])])):k("",!0)]))}}),na=Y(oa,[["__scopeId","data-v-41d09dc7"]]),ia={class:"theme-card__header"},la={class:"theme-card__info"},ra={class:"theme-card__primary"},ca={class:"theme-card__meta"},da={class:"theme-card__actions"},ua={key:0,class:"theme-card__action",title:"Template"},_a={class:"theme-card__description"},pa={key:0,class:"theme-card__secondary"},ma={class:"theme-card__secondary-themes"},ha={class:"theme-card__icon"},ga={key:1,class:"theme-card__selected-indicator"},va=q({__name:"ThemeCard",props:{theme:{},isSelected:{type:Boolean}},emits:["select","edit","duplicate","delete"],setup(R){function i(d){return d.split("-").map(s=>s.charAt(0).toUpperCase()+s.slice(1)).join(" ")}function p(d){return d.charAt(0).toUpperCase()+d.slice(1)}function a(d){return d.split("-").map(s=>s.charAt(0).toUpperCase()+s.slice(1)).join(" ")}function _(d){return{betrayal:"🗡️",friendship:"🤝",love:"❤️",revenge:"⚔️",redemption:"🕊️",sacrifice:"⚖️",power:"👑",freedom:"🦅",justice:"⚖️",survival:"🛡️",discovery:"🔍",transformation:"🦋","time-travel":"⏰",family:"👨‍👩‍👧‍👦",identity:"🪞","coming-of-age":"🌱"}[d]||"✨"}return(d,s)=>(o(),n("div",{class:M(["theme-card",{"theme-card--selected":d.isSelected,"theme-card--template":d.theme.isTemplate,[`theme-card--${d.theme.mood}`]:!0}]),onClick:s[3]||(s[3]=t=>d.$emit("select",d.theme))},[e("div",ia,[e("div",la,[e("h3",ra,u(i(d.theme.primary)),1),e("div",ca,[e("span",{class:M(`theme-card__mood theme-card__mood--${d.theme.mood}`)},u(p(d.theme.mood)),3),e("span",{class:M(`theme-card__conflict theme-card__conflict--${d.theme.conflictType}`)},u(a(d.theme.conflictType)),3)])]),e("div",da,[d.theme.isTemplate?(o(),n("button",ua," 📋 ")):k("",!0),e("button",{class:"theme-card__action",title:"Edit",onClick:s[0]||(s[0]=W(t=>d.$emit("edit",d.theme),["stop"]))}," ✏️ "),e("button",{class:"theme-card__action",title:"Duplicate",onClick:s[1]||(s[1]=W(t=>d.$emit("duplicate",d.theme.id),["stop"]))}," 📄 "),e("button",{class:"theme-card__action theme-card__action--danger",title:"Delete",onClick:s[2]||(s[2]=W(t=>d.$emit("delete",d.theme.id),["stop"]))}," 🗑️ ")])]),e("p",_a,u(d.theme.description),1),d.theme.secondary.length?(o(),n("div",pa,[s[4]||(s[4]=e("h4",{class:"theme-card__section-title"},"Secondary Themes:",-1)),e("div",ma,[(o(!0),n(P,null,L(d.theme.secondary,t=>(o(),n("span",{key:t,class:"theme-card__secondary-theme"},u(i(t)),1))),128))])])):k("",!0),e("div",ha,u(_(d.theme.primary)),1),d.isSelected?(o(),n("div",ga," ✓ Selected ")):k("",!0)],2))}}),ya=Y(va,[["__scopeId","data-v-840e7c35"]]),fa={class:"theme-builder"},ba={class:"theme-builder__header"},$a={class:"theme-builder__title"},ka={class:"theme-builder__section"},Ca={class:"base-input"},Sa=["value"],Ta={class:"base-input"},wa={class:"theme-builder__section"},Aa={class:"theme-builder__theme-grid"},Ia=["value","checked","onChange"],Va={class:"theme-builder__theme-label"},Ua={class:"theme-builder__section"},Pa={class:"theme-builder__row"},La={class:"base-input"},Oa={class:"base-input"},Da={class:"theme-builder__checkbox"},Ba={key:0,class:"theme-builder__section"},Ea={class:"theme-builder__suggestions"},Na=["onClick"],Ma={class:"theme-builder__suggestion-header"},Ga={class:"theme-builder__suggestion-secondary"},Fa={class:"theme-builder__suggestion-description"},Ra={class:"theme-builder__actions"},ja=q({__name:"ThemeBuilder",props:{editingTheme:{}},emits:["close","submit"],setup(R,{emit:i}){const p=R,a=i,_=O(!1),d=Object.values(oe),s=_e,t=te({primary:oe.REDEMPTION,secondary:[],description:"",conflictType:"both",mood:"mixed",isTemplate:!1}),f=N(()=>d.filter(C=>C!==t.primary));ne(()=>p.editingTheme,C=>{C?(t.primary=C.primary,t.secondary=[...C.secondary],t.description=C.description,t.conflictType=C.conflictType,t.mood=C.mood,t.isTemplate=C.isTemplate):G()},{immediate:!0});function G(){t.primary=oe.REDEMPTION,t.secondary=[],t.description="",t.conflictType="both",t.mood="mixed",t.isTemplate=!1}function D(C){return C.split("-").map(m=>m.charAt(0).toUpperCase()+m.slice(1)).join(" ")}function F(C){const m=t.secondary.indexOf(C);m>-1?t.secondary.splice(m,1):t.secondary.push(C)}function B(C){t.primary=C.primary,t.secondary=[...C.secondary],t.description=C.description}async function z(){_.value=!0;try{const C={primary:t.primary,secondary:[...t.secondary],description:t.description.trim()||`A story exploring ${D(t.primary)}${t.secondary.length?` with elements of ${t.secondary.map(D).join(", ")}`:""}`,conflictType:t.conflictType,mood:t.mood,isTemplate:t.isTemplate};a("submit",C)}finally{_.value=!1}}return(C,m)=>(o(),n("div",fa,[e("div",ba,[e("h2",$a,u(C.editingTheme?"Edit Theme":"Create New Theme"),1),h(w,{variant:"ghost",size:"small",onClick:m[0]||(m[0]=A=>C.$emit("close"))},{default:S(()=>m[7]||(m[7]=[y(" ✕ ")])),_:1,__:[7]})]),e("form",{onSubmit:W(z,["prevent"]),class:"theme-builder__form"},[e("div",ka,[m[10]||(m[10]=e("h3",{class:"theme-builder__section-title"},"Primary Theme",-1)),e("div",Ca,[m[8]||(m[8]=e("label",{class:"base-input__label"},"Main Theme",-1)),j(e("select",{"onUpdate:modelValue":m[1]||(m[1]=A=>t.primary=A),class:"theme-builder__select"},[(o(!0),n(P,null,L(Q(d),A=>(o(),n("option",{key:A,value:A},u(D(A)),9,Sa))),128))],512),[[Z,t.primary]])]),e("div",Ta,[m[9]||(m[9]=e("label",{class:"base-input__label"},"Description",-1)),j(e("textarea",{"onUpdate:modelValue":m[2]||(m[2]=A=>t.description=A),placeholder:"Describe how this theme will be explored in your story...",class:"theme-builder__textarea",rows:"3"},null,512),[[x,t.description]])])]),e("div",wa,[m[11]||(m[11]=e("h3",{class:"theme-builder__section-title"},"Secondary Themes",-1)),m[12]||(m[12]=e("p",{class:"theme-builder__section-description"}," Choose additional themes that complement your primary theme ",-1)),e("div",Aa,[(o(!0),n(P,null,L(f.value,A=>(o(),n("label",{key:A,class:"theme-builder__theme-option"},[e("input",{type:"checkbox",value:A,checked:t.secondary.includes(A),onChange:g=>F(A)},null,40,Ia),e("span",Va,u(D(A)),1)]))),128))])]),e("div",Ua,[m[18]||(m[18]=e("h3",{class:"theme-builder__section-title"},"Theme Properties",-1)),e("div",Pa,[e("div",La,[m[14]||(m[14]=e("label",{class:"base-input__label"},"Conflict Type",-1)),j(e("select",{"onUpdate:modelValue":m[3]||(m[3]=A=>t.conflictType=A),class:"theme-builder__select"},m[13]||(m[13]=[e("option",{value:"internal"},"Internal - Character vs Self",-1),e("option",{value:"external"},"External - Character vs World",-1),e("option",{value:"both"},"Both - Internal and External",-1)]),512),[[Z,t.conflictType]])]),e("div",Oa,[m[16]||(m[16]=e("label",{class:"base-input__label"},"Mood",-1)),j(e("select",{"onUpdate:modelValue":m[4]||(m[4]=A=>t.mood=A),class:"theme-builder__select"},m[15]||(m[15]=[e("option",{value:"light"},"Light - Hopeful and uplifting",-1),e("option",{value:"dark"},"Dark - Serious and somber",-1),e("option",{value:"neutral"},"Neutral - Balanced tone",-1),e("option",{value:"mixed"},"Mixed - Varies throughout",-1)]),512),[[Z,t.mood]])])]),e("div",Da,[e("label",null,[j(e("input",{type:"checkbox","onUpdate:modelValue":m[5]||(m[5]=A=>t.isTemplate=A)},null,512),[[ie,t.isTemplate]]),m[17]||(m[17]=e("span",null,"Save as template for future use",-1))])])]),Q(s).length?(o(),n("div",Ba,[m[19]||(m[19]=e("h3",{class:"theme-builder__section-title"},"Suggested Combinations",-1)),m[20]||(m[20]=e("p",{class:"theme-builder__section-description"}," Popular theme combinations that work well together ",-1)),e("div",Ea,[(o(!0),n(P,null,L(Q(s),A=>(o(),n("div",{key:A.primary,class:"theme-builder__suggestion",onClick:g=>B(A)},[e("div",Ma,[e("strong",null,u(D(A.primary)),1),e("span",Ga," + "+u(A.secondary.map(D).join(", ")),1)]),e("p",Fa,u(A.description),1)],8,Na))),128))])])):k("",!0),e("div",Ra,[h(w,{variant:"secondary",onClick:m[6]||(m[6]=A=>C.$emit("close"))},{default:S(()=>m[21]||(m[21]=[y(" Cancel ")])),_:1,__:[21]}),h(w,{type:"submit",loading:_.value},{default:S(()=>[y(u(C.editingTheme?"Update Theme":"Create Theme"),1)]),_:1},8,["loading"])])],32)]))}}),za=Y(ja,[["__scopeId","data-v-2d71b8f0"]]),Ka={class:"theme-selection"},Wa={class:"theme-selection__header"},qa={class:"theme-selection__actions"},Ya={class:"theme-selection__filters"},Ha={class:"theme-selection__filter-tabs"},Ja=["onClick"],Qa={class:"theme-selection__filter-count"},Xa={class:"theme-selection__mood-filters"},Za=["onClick"],xa={key:0,class:"theme-selection__suggestions"},eo={class:"theme-selection__suggestion-cards"},to=["onClick"],so={class:"theme-selection__suggestion-header"},ao={class:"theme-selection__suggestion-description"},oo={key:1,class:"theme-selection__selected"},no={class:"theme-selection__selected-title"},io={class:"theme-selection__selected-list"},lo={class:"theme-selection__selected-primary"},ro={key:0,class:"theme-selection__selected-secondary"},co=["onClick"],uo={class:"theme-selection__grid"},_o={key:0,class:"theme-selection__empty"},po={class:"theme-selection__empty-text"},mo=q({__name:"ThemeSelection",setup(R){const i=pe(),p=O(""),a=O("all"),_=O(null),d=O(!1),s=O(null),t=["light","dark","neutral","mixed"],f=_e,G=N(()=>[{key:"all",label:"All",count:i.themes.length},{key:"templates",label:"Templates",count:i.templates.length},{key:"internal",label:"Internal Conflict",count:i.themesByConflictType.internal?.length||0},{key:"external",label:"External Conflict",count:i.themesByConflictType.external?.length||0}]),D=N(()=>{let v=i.themes;return a.value==="templates"?v=i.templates:a.value==="internal"?v=i.getThemesByConflictType("internal"):a.value==="external"&&(v=i.getThemesByConflictType("external")),_.value&&(v=v.filter(T=>T.mood===_.value)),p.value&&(v=i.searchThemes(p.value)),v}),F=N(()=>i.selectedThemes);function B(v){return F.value.some(T=>T.id===v)}function z(v){B(v.id)?i.deselectTheme(v.id):i.selectTheme(v)}function C(v){i.deselectTheme(v)}function m(){i.clearSelectedThemes()}function A(v){s.value=v,d.value=!0}function g(v){i.duplicateTheme(v)}function l(v){confirm("Are you sure you want to delete this theme?")&&i.deleteTheme(v)}function r(){i.generateRandomTheme()}function c(v){const T=i.createThemeFromSuggestion(v);T&&i.selectTheme(T)}function $(v){_.value=_.value===v?null:v}function E(v){return v.split("-").map(T=>T.charAt(0).toUpperCase()+T.slice(1)).join(" ")}function H(v){return v.charAt(0).toUpperCase()+v.slice(1)}function I(){d.value=!1,s.value=null}function V(v){s.value?i.updateTheme(s.value.id,v):i.addTheme(v),I()}return le(()=>{i.loadFromStorage()}),(v,T)=>(o(),n("div",Ka,[e("div",Wa,[T[5]||(T[5]=e("div",{class:"theme-selection__title-section"},[e("h2",{class:"theme-selection__title"},"Choose Your Themes"),e("p",{class:"theme-selection__subtitle"}," Select the central themes and conflicts that will drive your story ")],-1)),e("div",qa,[h(w,{variant:"ghost",size:"small",onClick:r},{default:S(()=>T[3]||(T[3]=[y(" 🎲 Random Theme ")])),_:1,__:[3]}),h(w,{variant:"primary",onClick:T[0]||(T[0]=U=>d.value=!0)},{default:S(()=>T[4]||(T[4]=[y(" + Create Theme ")])),_:1,__:[4]})])]),e("div",Ya,[h(J,{modelValue:p.value,"onUpdate:modelValue":T[1]||(T[1]=U=>p.value=U),placeholder:"Search themes...",class:"theme-selection__search"},{suffix:S(()=>T[6]||(T[6]=[y(" 🔍 ")])),_:1},8,["modelValue"]),e("div",Ha,[(o(!0),n(P,null,L(G.value,U=>(o(),n("button",{key:U.key,class:M(["theme-selection__filter-tab",{"theme-selection__filter-tab--active":a.value===U.key}]),onClick:ee=>a.value=U.key},[y(u(U.label)+" ",1),e("span",Qa,u(U.count),1)],10,Ja))),128))]),e("div",Xa,[(o(),n(P,null,L(t,U=>e("button",{key:U,class:M(["theme-selection__mood-filter",`theme-selection__mood-filter--${U}`,{"theme-selection__mood-filter--active":_.value===U}]),onClick:ee=>$(U)},u(H(U)),11,Za)),64))])]),Q(f).length&&!p.value?(o(),n("div",xa,[T[9]||(T[9]=e("h3",{class:"theme-selection__suggestions-title"},"Popular Combinations",-1)),e("div",eo,[(o(!0),n(P,null,L(Q(f).slice(0,4),U=>(o(),n("div",{key:U.primary,class:"theme-selection__suggestion-card",onClick:ee=>c(U)},[e("div",so,[e("strong",null,u(E(U.primary)),1),T[7]||(T[7]=e("span",{class:"theme-selection__suggestion-plus"},"+",-1)),e("span",null,u(U.secondary.map(E).join(", ")),1)]),e("p",ao,u(U.description),1),h(w,{variant:"ghost",size:"small"},{default:S(()=>T[8]||(T[8]=[y(" Use This Combination ")])),_:1,__:[8]})],8,to))),128))])])):k("",!0),F.value.length?(o(),n("div",oo,[e("h3",no," Selected Themes ("+u(F.value.length)+") ",1),e("div",io,[(o(!0),n(P,null,L(F.value,U=>(o(),n("div",{key:U.id,class:"theme-selection__selected-item"},[e("span",lo,u(E(U.primary)),1),U.secondary.length?(o(),n("span",ro," + "+u(U.secondary.length)+" more ",1)):k("",!0),e("span",{class:M(`theme-selection__selected-mood theme-selection__selected-mood--${U.mood}`)},u(H(U.mood)),3),e("button",{class:"theme-selection__selected-remove",onClick:ee=>C(U.id)}," ✕ ",8,co)]))),128))]),h(w,{variant:"ghost",size:"small",onClick:m},{default:S(()=>T[10]||(T[10]=[y(" Clear All ")])),_:1,__:[10]})])):k("",!0),e("div",uo,[(o(!0),n(P,null,L(D.value,U=>(o(),re(ya,{key:U.id,theme:U,"is-selected":B(U.id),onSelect:z,onEdit:A,onDuplicate:g,onDelete:l},null,8,["theme","is-selected"]))),128)),D.value.length===0?(o(),n("div",_o,[T[12]||(T[12]=e("div",{class:"theme-selection__empty-icon"},"🎭",-1)),T[13]||(T[13]=e("h3",{class:"theme-selection__empty-title"},"No themes found",-1)),e("p",po,u(p.value?"Try adjusting your search terms":"Create your first theme to get started"),1),h(w,{variant:"primary",onClick:T[2]||(T[2]=U=>d.value=!0)},{default:S(()=>T[11]||(T[11]=[y(" Create Theme ")])),_:1,__:[11]})])):k("",!0)]),d.value?(o(),n("div",{key:2,class:"theme-selection__modal",onClick:W(I,["self"])},[h(za,{"editing-theme":s.value,onClose:I,onSubmit:V},null,8,["editing-theme"])])):k("",!0)]))}}),ho=Y(mo,[["__scopeId","data-v-d6c306ac"]]);class go{config;constructor(i){this.config=i}async generateChapterContent(i,p,a,_){const d=this.buildPrompt(i,p,a,_);try{switch(this.config.provider){case"openai":return await this.generateWithOpenAI(d);case"anthropic":return await this.generateWithAnthropic(d);case"local":return await this.generateWithLocalModel(d);default:throw new Error(`Unsupported AI provider: ${this.config.provider}`)}}catch(s){return console.error("AI generation failed:",s),this.generateFallbackContent(i,p,a,_)}}buildPrompt(i,p,a,_){const d=p.characters.filter(f=>i.characters.includes(f.id)),s=p.settings.find(f=>f.id===i.setting);d.find(f=>f.role==="protagonist")||d[0];let t=`Write Chapter ${_} of a ${p.genre} story titled "${p.title}".

`;return t+=`STORY CONTEXT:
`,t+=`- Genre: ${p.genre}
`,t+=`- Themes: ${p.themes.join(", ")}
`,t+=`- Summary: ${p.summary}

`,t+=`CHAPTER DETAILS:
`,t+=`- Title: ${i.title}
`,t+=`- Plot Point Type: ${i.type}
`,t+=`- Act: ${i.act}
`,t+=`- Description: ${i.description}

`,s&&(t+=`SETTING:
`,t+=`- Name: ${s.name}
`,t+=`- Type: ${s.type}
`,t+=`- Description: ${s.description}
`,t+=`- Atmosphere: ${s.atmosphere}
`,s.keyLocations.length>0&&(t+=`- Key Locations: ${s.keyLocations.join(", ")}
`),t+=`
`),t+=`CHARACTERS IN THIS CHAPTER:
`,d.forEach(f=>{t+=`- ${f.name} (${f.role}): ${f.description}
`,f.personalityTraits.length>0&&(t+=`  Personality: ${f.personalityTraits.join(", ")}
`),f.goals.length>0&&(t+=`  Goals: ${f.goals.join(", ")}
`),f.flaws.length>0&&(t+=`  Flaws: ${f.flaws.join(", ")}
`)}),t+=`
`,t+=`WRITING STYLE:
`,t+=`- Perspective: ${a.narrativePerspective}
`,t+=`- Tense: ${a.tense}
`,t+=`- Include dialogue: ${a.includeDialogue?"Yes":"No"}
`,t+=`- Target length: ${this.getTargetLength(a.length)} words

`,t+=`INSTRUCTIONS:
`,t+=`Write a compelling chapter that:
`,t+=`1. Advances the plot according to the plot point description
`,t+=`2. Develops the characters and their relationships
`,t+=`3. Maintains the established tone and atmosphere
`,t+=`4. Includes vivid descriptions of the setting
`,a.includeDialogue&&(t+=`5. Features natural dialogue that reveals character
`),t+=`6. Builds toward the overall story themes
`,t+=`7. Ends with appropriate tension or resolution for this plot point

`,t+="Write the chapter now:",t}async generateWithOpenAI(i){if(!this.config.apiKey)throw new Error("OpenAI API key not provided");const p=await fetch("https://api.openai.com/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`},body:JSON.stringify({model:this.config.model||"gpt-4",messages:[{role:"system",content:"You are a skilled creative writer specializing in narrative fiction. Write engaging, well-structured prose with vivid descriptions and compelling character development."},{role:"user",content:i}],max_tokens:2e3,temperature:.8})});if(!p.ok)throw new Error(`OpenAI API error: ${p.statusText}`);return(await p.json()).choices[0]?.message?.content||""}async generateWithAnthropic(i){if(!this.config.apiKey)throw new Error("Anthropic API key not provided");const p=await fetch("https://api.anthropic.com/v1/messages",{method:"POST",headers:{"Content-Type":"application/json","x-api-key":this.config.apiKey,"anthropic-version":"2023-06-01"},body:JSON.stringify({model:this.config.model||"claude-3-sonnet-20240229",max_tokens:2e3,messages:[{role:"user",content:i}],temperature:.8})});if(!p.ok)throw new Error(`Anthropic API error: ${p.statusText}`);return(await p.json()).content[0]?.text||""}async generateWithLocalModel(i){const p=this.config.baseURL||"http://localhost:11434",a=await fetch(`${p}/api/generate`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({model:this.config.model||"llama2",prompt:i,stream:!1,options:{temperature:.8,top_p:.9}})});if(!a.ok)throw new Error(`Local model API error: ${a.statusText}`);return(await a.json()).response||""}generateFallbackContent(i,p,a,_){return`# Chapter ${_}: ${i.title}

${i.description}

[This chapter would be generated using the enhanced rule-based system as a fallback when AI generation is unavailable.]`}getTargetLength(i){switch(i){case"short":return 300;case"medium":return 600;case"long":return 1e3;default:return 600}}static isConfigured(i){switch(i.provider){case"openai":case"anthropic":return!!i.apiKey;case"local":return!0;default:return!1}}}const vo={class:"ai-config-modal"},yo={class:"ai-config-modal__header"},fo={class:"ai-config-modal__content"},bo={class:"ai-config-modal__providers"},$o={class:"ai-config-modal__provider-options"},ko=["value"],Co={class:"ai-config-modal__provider-content"},So={class:"ai-config-modal__provider-header"},To={class:"ai-config-modal__provider-name"},wo={class:"ai-config-modal__provider-description"},Ao={class:"ai-config-modal__provider-features"},Io={key:0,class:"ai-config-modal__api-config"},Vo={class:"ai-config-modal__api-help"},Uo={key:0,class:"ai-config-modal__help-content"},Po={key:1,class:"ai-config-modal__help-content"},Lo={key:1,class:"ai-config-modal__local-config"},Oo={class:"ai-config-modal__test"},Do={class:"ai-config-modal__actions"},Bo=q({__name:"AIConfigModal",emits:["close","save"],setup(R,{emit:i}){const p=i,a=O("openai"),_=O(""),d=O(""),s=O("http://localhost:11434"),t=O(!1),f=O(null),G=O({apiKey:""}),D=[{id:"openai",name:"OpenAI",tier:"premium",description:"High-quality story generation with GPT-4. Requires API key and credits.",features:["GPT-4 Turbo","Excellent creativity","Fast generation"]},{id:"anthropic",name:"Anthropic Claude",tier:"premium",description:"Sophisticated storytelling with Claude. Requires API key and credits.",features:["Claude 3 Sonnet","Great dialogue","Nuanced characters"]},{id:"local",name:"Local Model",tier:"free",description:"Use your own local LLM. Free but requires technical setup.",features:["Privacy focused","No API costs","Offline capable"]}],F=N(()=>a.value==="local"?s.value.trim()!==""&&d.value.trim()!=="":_.value.trim()!==""),B=N(()=>a.value==="local"?s.value.trim()!==""&&d.value.trim()!=="":_.value.trim()!==""&&!G.value.apiKey);function z(g){return{openai:"OpenAI",anthropic:"Anthropic",local:"Local Model"}[g]||g}function C(g){return{openai:"gpt-4",anthropic:"claude-3-sonnet-20240229",local:"llama2"}[g]||""}async function m(){t.value=!0,f.value=null,G.value.apiKey="";try{const g={provider:a.value,apiKey:a.value!=="local"?_.value:void 0,model:d.value||C(a.value),baseURL:a.value==="local"?s.value:void 0},l=new go(g),r="Write a single sentence about a brave knight.";a.value==="openai"?await l.generateWithOpenAI(r):a.value==="anthropic"?await l.generateWithAnthropic(r):await l.generateWithLocalModel(r),f.value={type:"success",message:"✅ Connection successful! AI generation is ready to use."}}catch(g){f.value={type:"error",message:`❌ Connection failed: ${g.message}`},g.message.includes("API key")&&(G.value.apiKey="Invalid API key")}finally{t.value=!1}}function A(){if(!B.value)return;const g={provider:a.value,apiKey:a.value!=="local"?_.value:void 0,model:d.value||C(a.value),baseURL:a.value==="local"?s.value:void 0};localStorage.setItem("aiStoryConfig",JSON.stringify(g)),p("save",g)}return(g,l)=>(o(),n("div",vo,[e("div",yo,[l[8]||(l[8]=e("h2",{class:"ai-config-modal__title"},"AI Story Generation Setup",-1)),h(w,{variant:"ghost",size:"small",onClick:l[0]||(l[0]=r=>g.$emit("close"))},{default:S(()=>l[7]||(l[7]=[y(" ✕ ")])),_:1,__:[7]})]),e("div",fo,[l[16]||(l[16]=e("p",{class:"ai-config-modal__description"}," Enable AI-powered story generation for richer, more detailed narratives. Choose your preferred AI provider and configure your settings. ",-1)),e("div",bo,[l[9]||(l[9]=e("h3",{class:"ai-config-modal__section-title"},"Choose AI Provider",-1)),e("div",$o,[(o(),n(P,null,L(D,r=>e("label",{key:r.id,class:M(["ai-config-modal__provider",{"ai-config-modal__provider--selected":a.value===r.id}])},[j(e("input",{type:"radio",value:r.id,"onUpdate:modelValue":l[1]||(l[1]=c=>a.value=c),class:"ai-config-modal__provider-radio"},null,8,ko),[[ye,a.value]]),e("div",Co,[e("div",So,[e("h4",To,u(r.name),1),e("span",{class:M(["ai-config-modal__provider-badge",`ai-config-modal__provider-badge--${r.tier}`])},u(r.tier),3)]),e("p",wo,u(r.description),1),e("div",Ao,[(o(!0),n(P,null,L(r.features,c=>(o(),n("span",{key:c,class:"ai-config-modal__provider-feature"},u(c),1))),128))])])],2)),64))])]),a.value!=="local"?(o(),n("div",Io,[l[13]||(l[13]=e("h3",{class:"ai-config-modal__section-title"},"API Configuration",-1)),h(J,{modelValue:_.value,"onUpdate:modelValue":l[2]||(l[2]=r=>_.value=r),label:"API Key",type:"password",placeholder:`Enter your ${z(a.value)} API key`,error:G.value.apiKey},null,8,["modelValue","placeholder","error"]),h(J,{modelValue:d.value,"onUpdate:modelValue":l[3]||(l[3]=r=>d.value=r),label:"Model (Optional)",placeholder:C(a.value),hint:"Leave empty to use the default model"},null,8,["modelValue","placeholder"]),e("div",Vo,[l[12]||(l[12]=e("h4",null,"How to get your API key:",-1)),a.value==="openai"?(o(),n("div",Uo,l[10]||(l[10]=[e("p",null,[y("1. Go to "),e("a",{href:"https://platform.openai.com/api-keys",target:"_blank"},"OpenAI API Keys")],-1),e("p",null,'2. Click "Create new secret key"',-1),e("p",null,"3. Copy the key and paste it above",-1),e("p",{class:"ai-config-modal__warning"},"⚠️ Keep your API key secure and never share it publicly",-1)]))):a.value==="anthropic"?(o(),n("div",Po,l[11]||(l[11]=[e("p",null,[y("1. Go to "),e("a",{href:"https://console.anthropic.com/",target:"_blank"},"Anthropic Console")],-1),e("p",null,"2. Navigate to API Keys section",-1),e("p",null,"3. Create a new API key",-1),e("p",null,"4. Copy the key and paste it above",-1),e("p",{class:"ai-config-modal__warning"},"⚠️ Keep your API key secure and never share it publicly",-1)]))):k("",!0)])])):(o(),n("div",Lo,[l[14]||(l[14]=e("h3",{class:"ai-config-modal__section-title"},"Local Model Configuration",-1)),h(J,{modelValue:s.value,"onUpdate:modelValue":l[4]||(l[4]=r=>s.value=r),label:"Base URL",placeholder:"http://localhost:11434",hint:"URL of your local LLM server (e.g., Ollama)"},null,8,["modelValue"]),h(J,{modelValue:d.value,"onUpdate:modelValue":l[5]||(l[5]=r=>d.value=r),label:"Model Name",placeholder:"llama2",hint:"Name of the local model to use"},null,8,["modelValue"]),l[15]||(l[15]=e("div",{class:"ai-config-modal__local-help"},[e("h4",null,"Setting up a local model:"),e("div",{class:"ai-config-modal__help-content"},[e("p",null,[y("1. Install "),e("a",{href:"https://ollama.ai/",target:"_blank"},"Ollama"),y(" or another local LLM server")]),e("p",null,[y("2. Download a model: "),e("code",null,"ollama pull llama2")]),e("p",null,[y("3. Start the server: "),e("code",null,"ollama serve")]),e("p",null,"4. The default URL should work if running locally")])],-1))])),e("div",Oo,[h(w,{variant:"secondary",loading:t.value,disabled:!F.value,onClick:m},{default:S(()=>[y(u(t.value?"Testing...":"Test Connection"),1)]),_:1},8,["loading","disabled"]),f.value?(o(),n("div",{key:0,class:M(["ai-config-modal__test-result",`ai-config-modal__test-result--${f.value.type}`])},u(f.value.message),3)):k("",!0)])]),e("div",Do,[h(w,{variant:"secondary",onClick:l[6]||(l[6]=r=>g.$emit("close"))},{default:S(()=>l[17]||(l[17]=[y(" Cancel ")])),_:1,__:[17]}),h(w,{variant:"primary",disabled:!B.value,onClick:A},{default:S(()=>l[18]||(l[18]=[y(" Save Configuration ")])),_:1,__:[18]},8,["disabled"])])]))}}),Eo=Y(Bo,[["__scopeId","data-v-b65fb1d9"]]),No={class:"story-generator"},Mo={class:"story-generator__content"},Go={key:0,class:"story-generator__step"},Fo={class:"story-generator__step-actions"},Ro={key:1,class:"story-generator__step"},jo={class:"story-generator__step-actions"},zo={key:2,class:"story-generator__step"},Ko={class:"story-generator__step-actions"},Wo={key:3,class:"story-generator__step"},qo={class:"story-generator__generation"},Yo={class:"story-generator__options"},Ho={class:"story-generator__option-group"},Jo={class:"story-generator__option-buttons"},Qo=["onClick"],Xo={class:"story-generator__option-label"},Zo={class:"story-generator__option-description"},xo={class:"story-generator__option-group"},en={class:"story-generator__option-buttons"},tn=["onClick"],sn={class:"story-generator__option-label"},an={class:"story-generator__option-description"},on={class:"story-generator__option-group"},nn={class:"story-generator__checkboxes"},ln={class:"story-generator__checkbox"},rn=["checked"],cn={class:"story-generator__checkbox"},dn=["checked"],un={key:0,class:"story-generator__ai-config"},_n={class:"story-generator__generation-actions"},pn={key:0,class:"story-generator__progress"},mn={class:"story-generator__progress-text"},hn={key:4,class:"story-generator__step"},gn={class:"story-generator__complete"},vn={key:0,class:"story-generator__story-preview"},yn={class:"story-generator__story-title"},fn={class:"story-generator__story-summary"},bn={class:"story-generator__story-stats"},$n={class:"story-generator__stat"},kn={class:"story-generator__stat-value"},Cn={class:"story-generator__stat"},Sn={class:"story-generator__stat-value"},Tn={class:"story-generator__stat"},wn={class:"story-generator__stat-value"},An={class:"story-generator__stat"},In={class:"story-generator__stat-value"},Vn={class:"story-generator__complete-actions"},Un=q({__name:"StoryGenerator",setup(R){const i=be(),p=fe(),a=de(),_=ue(),d=pe(),s=O(""),t=O(!1),f=O(null),G=[{key:"characters",label:"Characters"},{key:"settings",label:"Settings"},{key:"themes",label:"Themes"},{key:"generation",label:"Generate"},{key:"complete",label:"Complete"}],D=[{value:"short",label:"Short",description:"~2,000 words, 10 min read"},{value:"medium",label:"Medium",description:"~5,000 words, 25 min read"},{value:"long",label:"Long",description:"~10,000 words, 50 min read"}],F=[{value:"first",label:"First Person",description:"I, me, my"},{value:"third-limited",label:"Third Person Limited",description:"He, she, they (one viewpoint)"},{value:"third-omniscient",label:"Third Person Omniscient",description:"All-knowing narrator"}],B=N(()=>p.generatorState.currentStep),z=N(()=>p.currentStepIndex),C=N(()=>p.generatorState.generationOptions),m=N(()=>p.generatorState.isGenerating),A=N(()=>p.generatorState.progress),g=N(()=>p.currentStory),l=N(()=>a.selectedCharacters.length>0),r=N(()=>_.selectedSettings.length>0),c=N(()=>d.selectedThemes.length>0);function $(X,b){b<=z.value&&p.setCurrentStep(X)}function E(){B.value==="themes"&&(p.generatorState.selectedCharacters=[...a.selectedCharacters],p.generatorState.selectedSettings=[..._.selectedSettings],p.generatorState.selectedThemes=[...d.selectedThemes]),p.nextStep()}function H(){p.previousStep()}function I(X,b){p.updateGenerationOptions({[X]:b})}async function V(){s.value="Analyzing your selections...";try{await p.generateStory()?s.value="Story generated successfully!":s.value="Failed to generate story. Please try again."}catch(X){console.error("Story generation failed:",X),s.value="An error occurred during generation."}}function v(){p.resetGenerator(),a.clearSelectedCharacters(),_.clearSelectedSettings(),d.clearSelectedThemes()}function T(){g.value&&i.push(`/story/${g.value.id}`)}function U(){t.value=!1}function ee(X){f.value={type:"success",message:"✅ AI configuration saved successfully"},t.value=!1,setTimeout(()=>{f.value=null},3e3)}return(X,b)=>(o(),n("div",No,[b[24]||(b[24]=e("div",{class:"story-generator__header"},[e("h1",{class:"story-generator__title"},"AI Story Builder"),e("p",{class:"story-generator__subtitle"}," Create compelling stories by selecting characters, settings, and themes ")],-1)),h(De,{steps:G,"current-step-index":z.value,onStepClick:$},null,8,["current-step-index"]),e("div",Mo,[B.value==="characters"?(o(),n("div",Go,[h(Ht),e("div",Fo,[h(w,{variant:"primary",disabled:!l.value,onClick:E},{default:S(()=>b[3]||(b[3]=[y(" Continue to Settings ")])),_:1,__:[3]},8,["disabled"])])])):k("",!0),B.value==="settings"?(o(),n("div",Ro,[h(na),e("div",jo,[h(w,{variant:"secondary",onClick:H},{default:S(()=>b[4]||(b[4]=[y(" Back to Characters ")])),_:1,__:[4]}),h(w,{variant:"primary",disabled:!r.value,onClick:E},{default:S(()=>b[5]||(b[5]=[y(" Continue to Themes ")])),_:1,__:[5]},8,["disabled"])])])):k("",!0),B.value==="themes"?(o(),n("div",zo,[h(ho),e("div",Ko,[h(w,{variant:"secondary",onClick:H},{default:S(()=>b[6]||(b[6]=[y(" Back to Settings ")])),_:1,__:[6]}),h(w,{variant:"primary",disabled:!c.value,onClick:E},{default:S(()=>b[7]||(b[7]=[y(" Generate Story ")])),_:1,__:[7]},8,["disabled"])])])):k("",!0),B.value==="generation"?(o(),n("div",Wo,[e("div",qo,[b[16]||(b[16]=e("div",{class:"story-generator__generation-header"},[e("h2",{class:"story-generator__generation-title"},"Story Generation Options"),e("p",{class:"story-generator__generation-subtitle"}," Customize how your story will be generated ")],-1)),e("div",Yo,[e("div",Ho,[b[8]||(b[8]=e("h3",{class:"story-generator__option-title"},"Story Length",-1)),e("div",Jo,[(o(),n(P,null,L(D,K=>e("button",{key:K.value,class:M(["story-generator__option-button",{"story-generator__option-button--active":C.value.length===K.value}]),onClick:me=>I("length",K.value)},[e("div",Xo,u(K.label),1),e("div",Zo,u(K.description),1)],10,Qo)),64))])]),e("div",xo,[b[9]||(b[9]=e("h3",{class:"story-generator__option-title"},"Narrative Perspective",-1)),e("div",en,[(o(),n(P,null,L(F,K=>e("button",{key:K.value,class:M(["story-generator__option-button",{"story-generator__option-button--active":C.value.narrativePerspective===K.value}]),onClick:me=>I("narrativePerspective",K.value)},[e("div",sn,u(K.label),1),e("div",an,u(K.description),1)],10,tn)),64))])]),e("div",on,[b[13]||(b[13]=e("h3",{class:"story-generator__option-title"},"Additional Options",-1)),e("div",nn,[e("label",ln,[e("input",{type:"checkbox",checked:C.value.includeDialogue,onChange:b[0]||(b[0]=K=>I("includeDialogue",K.target.checked))},null,40,rn),b[10]||(b[10]=e("span",null,"Include dialogue between characters",-1))]),e("label",cn,[e("input",{type:"checkbox",checked:C.value.useAI,onChange:b[1]||(b[1]=K=>I("useAI",K.target.checked))},null,40,dn),b[11]||(b[11]=e("span",null,"Use AI assistance for enhanced content",-1))]),C.value.useAI?(o(),n("div",un,[h(w,{variant:"ghost",size:"small",onClick:b[2]||(b[2]=K=>t.value=!0)},{default:S(()=>b[12]||(b[12]=[y(" ⚙️ Configure AI Settings ")])),_:1,__:[12]}),f.value?(o(),n("span",{key:0,class:M(["story-generator__ai-status",`story-generator__ai-status--${f.value.type}`])},u(f.value.message),3)):k("",!0)])):k("",!0)])])]),e("div",_n,[h(w,{variant:"secondary",onClick:H},{default:S(()=>b[14]||(b[14]=[y(" Back to Themes ")])),_:1,__:[14]}),h(w,{variant:"primary",loading:m.value,onClick:V},{default:S(()=>[y(u(m.value?"Generating Story...":"Generate My Story"),1)]),_:1},8,["loading"])]),m.value?(o(),n("div",pn,[b[15]||(b[15]=e("h3",{class:"story-generator__progress-title"},"Generating Your Story",-1)),h(Te,{progress:A.value},null,8,["progress"]),e("p",mn,u(s.value),1)])):k("",!0)])])):k("",!0),B.value==="complete"?(o(),n("div",hn,[e("div",gn,[b[23]||(b[23]=e("div",{class:"story-generator__complete-header"},[e("div",{class:"story-generator__complete-icon"},"🎉"),e("h2",{class:"story-generator__complete-title"},"Story Generated Successfully!"),e("p",{class:"story-generator__complete-subtitle"}," Your story outline has been created and is ready for editing ")],-1)),g.value?(o(),n("div",vn,[e("h3",yn,u(g.value.outline.title),1),e("p",fn,u(g.value.outline.summary),1),e("div",bn,[e("div",$n,[b[17]||(b[17]=e("span",{class:"story-generator__stat-label"},"Characters:",-1)),e("span",kn,u(g.value.outline.characters.length),1)]),e("div",Cn,[b[18]||(b[18]=e("span",{class:"story-generator__stat-label"},"Settings:",-1)),e("span",Sn,u(g.value.outline.settings.length),1)]),e("div",Tn,[b[19]||(b[19]=e("span",{class:"story-generator__stat-label"},"Plot Points:",-1)),e("span",wn,u(g.value.outline.plotPoints.length),1)]),e("div",An,[b[20]||(b[20]=e("span",{class:"story-generator__stat-label"},"Est. Reading Time:",-1)),e("span",In,u(g.value.outline.estimatedReadingTime)+" min",1)])])])):k("",!0),e("div",Vn,[h(w,{variant:"secondary",onClick:v},{default:S(()=>b[21]||(b[21]=[y(" Create Another Story ")])),_:1,__:[21]}),h(w,{variant:"primary",onClick:T},{default:S(()=>b[22]||(b[22]=[y(" View & Edit Story ")])),_:1,__:[22]})])])])):k("",!0)]),t.value?(o(),n("div",{key:0,class:"story-generator__modal",onClick:W(U,["self"])},[h(Eo,{onClose:U,onSave:ee})])):k("",!0)]))}}),Pn=Y(Un,[["__scopeId","data-v-7a21babc"]]),Ln={class:"generator-view"},On=q({__name:"GeneratorView",setup(R){return(i,p)=>(o(),n("div",Ln,[h(Pn)]))}}),En=Y(On,[["__scopeId","data-v-2313f457"]]);export{En as default};
