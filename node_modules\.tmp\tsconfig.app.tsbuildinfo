{"root": ["../../env.d.ts", "../../src/app.vue", "../../src/main.ts", "../../src/components/helloworld.vue", "../../src/components/thewelcome.vue", "../../src/components/welcomeitem.vue", "../../src/components/character/characterbuilder.vue", "../../src/components/character/charactercard.vue", "../../src/components/character/characterselection.vue", "../../src/components/icons/iconcommunity.vue", "../../src/components/icons/icondocumentation.vue", "../../src/components/icons/iconecosystem.vue", "../../src/components/icons/iconsupport.vue", "../../src/components/icons/icontooling.vue", "../../src/components/setting/settingbuilder.vue", "../../src/components/setting/settingcard.vue", "../../src/components/setting/settingselection.vue", "../../src/components/story/storygenerator.vue", "../../src/components/theme/themebuilder.vue", "../../src/components/theme/themecard.vue", "../../src/components/theme/themeselection.vue", "../../src/components/ui/basebutton.vue", "../../src/components/ui/baseinput.vue", "../../src/components/ui/progressbar.vue", "../../src/components/ui/stepindicator.vue", "../../src/data/sampledata.ts", "../../src/router/index.ts", "../../src/stores/characterstore.ts", "../../src/stores/index.ts", "../../src/stores/settingstore.ts", "../../src/stores/storystore.ts", "../../src/stores/themestore.ts", "../../src/types/index.ts", "../../src/utils/contentgenerator.ts", "../../src/utils/storyengine.ts", "../../src/views/generatorview.vue", "../../src/views/homeview.vue", "../../src/views/libraryview.vue", "../../src/views/storyview.vue"], "version": "5.8.3"}