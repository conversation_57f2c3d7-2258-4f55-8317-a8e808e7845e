const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/GeneratorView-Gb5KoR6i.js","assets/BaseButton-CRuttqCK.js","assets/BaseButton-ClCmFRRR.css","assets/GeneratorView-DI1ogm4J.css","assets/StoryView-BiIZHXDY.js","assets/StoryView-4gIrAH9x.css","assets/LibraryView-E3UIPM9y.js","assets/LibraryView-goZ2bvE9.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Ps(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ae={},Lt=[],Xe=()=>{},xi=()=>!1,Hn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ms=e=>e.startsWith("onUpdate:"),Ae=Object.assign,Ds=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Oi=Object.prototype.hasOwnProperty,ie=(e,t)=>Oi.call(e,t),Y=Array.isArray,Ft=e=>vn(e)==="[object Map]",Wt=e=>vn(e)==="[object Set]",nr=e=>vn(e)==="[object Date]",ee=e=>typeof e=="function",_e=e=>typeof e=="string",Be=e=>typeof e=="symbol",he=e=>e!==null&&typeof e=="object",to=e=>(he(e)||ee(e))&&ee(e.then)&&ee(e.catch),no=Object.prototype.toString,vn=e=>no.call(e),Ii=e=>vn(e).slice(8,-1),so=e=>vn(e)==="[object Object]",Ns=e=>_e(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Qt=Ps(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),kn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Pi=/-(\w)/g,Ve=kn(e=>e.replace(Pi,(t,n)=>n?n.toUpperCase():"")),Mi=/\B([A-Z])/g,xt=kn(e=>e.replace(Mi,"-$1").toLowerCase()),Vn=kn(e=>e.charAt(0).toUpperCase()+e.slice(1)),es=kn(e=>e?`on${Vn(e)}`:""),mt=(e,t)=>!Object.is(e,t),Rn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ps=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},In=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let sr;const jn=()=>sr||(sr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ls(e){if(Y(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=_e(s)?Fi(s):Ls(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(_e(e)||he(e))return e}const Di=/;(?![^(]*\))/g,Ni=/:([^]+)/,Li=/\/\*[^]*?\*\//g;function Fi(e){const t={};return e.replace(Li,"").split(Di).forEach(n=>{if(n){const s=n.split(Ni);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Fs(e){let t="";if(_e(e))t=e;else if(Y(e))for(let n=0;n<e.length;n++){const s=Fs(e[n]);s&&(t+=s+" ")}else if(he(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const $i="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Hi=Ps($i);function ro(e){return!!e||e===""}function ki(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Tt(e[s],t[s]);return n}function Tt(e,t){if(e===t)return!0;let n=nr(e),s=nr(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=Be(e),s=Be(t),n||s)return e===t;if(n=Y(e),s=Y(t),n||s)return n&&s?ki(e,t):!1;if(n=he(e),s=he(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),c=t.hasOwnProperty(i);if(l&&!c||!l&&c||!Tt(e[i],t[i]))return!1}}return String(e)===String(t)}function $s(e,t){return e.findIndex(n=>Tt(n,t))}const oo=e=>!!(e&&e.__v_isRef===!0),Jt=e=>_e(e)?e:e==null?"":Y(e)||he(e)&&(e.toString===no||!ee(e.toString))?oo(e)?Jt(e.value):JSON.stringify(e,io,2):String(e),io=(e,t)=>oo(t)?io(e,t.value):Ft(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[ts(s,o)+" =>"]=r,n),{})}:Wt(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>ts(n))}:Be(t)?ts(t):he(t)&&!Y(t)&&!so(t)?String(t):t,ts=(e,t="")=>{var n;return Be(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ce;class lo{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ce,!t&&Ce&&(this.index=(Ce.scopes||(Ce.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Ce;try{return Ce=this,t()}finally{Ce=n}}}on(){++this._on===1&&(this.prevScope=Ce,Ce=this)}off(){this._on>0&&--this._on===0&&(Ce=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function co(e){return new lo(e)}function ao(){return Ce}function Vi(e,t=!1){Ce&&Ce.cleanups.push(e)}let de;const ns=new WeakSet;class uo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ce&&Ce.active&&Ce.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,ns.has(this)&&(ns.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ho(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,rr(this),po(this);const t=de,n=je;de=this,je=!0;try{return this.fn()}finally{go(this),de=t,je=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Vs(t);this.deps=this.depsTail=void 0,rr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?ns.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){gs(this)&&this.run()}get dirty(){return gs(this)}}let fo=0,Zt,Xt;function ho(e,t=!1){if(e.flags|=8,t){e.next=Xt,Xt=e;return}e.next=Zt,Zt=e}function Hs(){fo++}function ks(){if(--fo>0)return;if(Xt){let t=Xt;for(Xt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Zt;){let t=Zt;for(Zt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function po(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function go(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),Vs(s),ji(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function gs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(mo(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function mo(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===cn)||(e.globalVersion=cn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!gs(e))))return;e.flags|=2;const t=e.dep,n=de,s=je;de=e,je=!0;try{po(e);const r=e.fn(e._value);(t.version===0||mt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{de=n,je=s,go(e),e.flags&=-3}}function Vs(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Vs(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function ji(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let je=!0;const vo=[];function lt(){vo.push(je),je=!1}function ct(){const e=vo.pop();je=e===void 0?!0:e}function rr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=de;de=void 0;try{t()}finally{de=n}}}let cn=0;class Bi{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class js{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!de||!je||de===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==de)n=this.activeLink=new Bi(de,this),de.deps?(n.prevDep=de.depsTail,de.depsTail.nextDep=n,de.depsTail=n):de.deps=de.depsTail=n,yo(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=de.depsTail,n.nextDep=void 0,de.depsTail.nextDep=n,de.depsTail=n,de.deps===n&&(de.deps=s)}return n}trigger(t){this.version++,cn++,this.notify(t)}notify(t){Hs();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{ks()}}}function yo(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)yo(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Pn=new WeakMap,Rt=Symbol(""),ms=Symbol(""),an=Symbol("");function Re(e,t,n){if(je&&de){let s=Pn.get(e);s||Pn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new js),r.map=s,r.key=n),r.track()}}function rt(e,t,n,s,r,o){const i=Pn.get(e);if(!i){cn++;return}const l=c=>{c&&c.trigger()};if(Hs(),t==="clear")i.forEach(l);else{const c=Y(e),f=c&&Ns(n);if(c&&n==="length"){const a=Number(s);i.forEach((h,p)=>{(p==="length"||p===an||!Be(p)&&p>=a)&&l(h)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),f&&l(i.get(an)),t){case"add":c?f&&l(i.get("length")):(l(i.get(Rt)),Ft(e)&&l(i.get(ms)));break;case"delete":c||(l(i.get(Rt)),Ft(e)&&l(i.get(ms)));break;case"set":Ft(e)&&l(i.get(Rt));break}}ks()}function Ui(e,t){const n=Pn.get(e);return n&&n.get(t)}function Pt(e){const t=se(e);return t===e?t:(Re(t,"iterate",an),$e(e)?t:t.map(Ee))}function Bn(e){return Re(e=se(e),"iterate",an),e}const Wi={__proto__:null,[Symbol.iterator](){return ss(this,Symbol.iterator,Ee)},concat(...e){return Pt(this).concat(...e.map(t=>Y(t)?Pt(t):t))},entries(){return ss(this,"entries",e=>(e[1]=Ee(e[1]),e))},every(e,t){return tt(this,"every",e,t,void 0,arguments)},filter(e,t){return tt(this,"filter",e,t,n=>n.map(Ee),arguments)},find(e,t){return tt(this,"find",e,t,Ee,arguments)},findIndex(e,t){return tt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return tt(this,"findLast",e,t,Ee,arguments)},findLastIndex(e,t){return tt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return tt(this,"forEach",e,t,void 0,arguments)},includes(...e){return rs(this,"includes",e)},indexOf(...e){return rs(this,"indexOf",e)},join(e){return Pt(this).join(e)},lastIndexOf(...e){return rs(this,"lastIndexOf",e)},map(e,t){return tt(this,"map",e,t,void 0,arguments)},pop(){return Gt(this,"pop")},push(...e){return Gt(this,"push",e)},reduce(e,...t){return or(this,"reduce",e,t)},reduceRight(e,...t){return or(this,"reduceRight",e,t)},shift(){return Gt(this,"shift")},some(e,t){return tt(this,"some",e,t,void 0,arguments)},splice(...e){return Gt(this,"splice",e)},toReversed(){return Pt(this).toReversed()},toSorted(e){return Pt(this).toSorted(e)},toSpliced(...e){return Pt(this).toSpliced(...e)},unshift(...e){return Gt(this,"unshift",e)},values(){return ss(this,"values",Ee)}};function ss(e,t,n){const s=Bn(e),r=s[t]();return s!==e&&!$e(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const Ki=Array.prototype;function tt(e,t,n,s,r,o){const i=Bn(e),l=i!==e&&!$e(e),c=i[t];if(c!==Ki[t]){const h=c.apply(e,o);return l?Ee(h):h}let f=n;i!==e&&(l?f=function(h,p){return n.call(this,Ee(h),p,e)}:n.length>2&&(f=function(h,p){return n.call(this,h,p,e)}));const a=c.call(i,f,s);return l&&r?r(a):a}function or(e,t,n,s){const r=Bn(e);let o=n;return r!==e&&($e(e)?n.length>3&&(o=function(i,l,c){return n.call(this,i,l,c,e)}):o=function(i,l,c){return n.call(this,i,Ee(l),c,e)}),r[t](o,...s)}function rs(e,t,n){const s=se(e);Re(s,"iterate",an);const r=s[t](...n);return(r===-1||r===!1)&&Ws(n[0])?(n[0]=se(n[0]),s[t](...n)):r}function Gt(e,t,n=[]){lt(),Hs();const s=se(e)[t].apply(e,n);return ks(),ct(),s}const Gi=Ps("__proto__,__v_isRef,__isVue"),_o=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Be));function Yi(e){Be(e)||(e=String(e));const t=se(this);return Re(t,"has",e),t.hasOwnProperty(e)}class So{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?sl:Co:o?wo:Eo).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=Y(t);if(!r){let c;if(i&&(c=Wi[n]))return c;if(n==="hasOwnProperty")return Yi}const l=Reflect.get(t,n,ye(t)?t:s);return(Be(n)?_o.has(n):Gi(n))||(r||Re(t,"get",n),o)?l:ye(l)?i&&Ns(n)?l:l.value:he(l)?r?Ao(l):yn(l):l}}class bo extends So{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const c=yt(o);if(!$e(s)&&!yt(s)&&(o=se(o),s=se(s)),!Y(t)&&ye(o)&&!ye(s))return c?!1:(o.value=s,!0)}const i=Y(t)&&Ns(n)?Number(n)<t.length:ie(t,n),l=Reflect.set(t,n,s,ye(t)?t:r);return t===se(r)&&(i?mt(s,o)&&rt(t,"set",n,s):rt(t,"add",n,s)),l}deleteProperty(t,n){const s=ie(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&rt(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!Be(n)||!_o.has(n))&&Re(t,"has",n),s}ownKeys(t){return Re(t,"iterate",Y(t)?"length":Rt),Reflect.ownKeys(t)}}class qi extends So{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Ji=new bo,zi=new qi,Qi=new bo(!0);const vs=e=>e,En=e=>Reflect.getPrototypeOf(e);function Zi(e,t,n){return function(...s){const r=this.__v_raw,o=se(r),i=Ft(o),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,f=r[e](...s),a=n?vs:t?Mn:Ee;return!t&&Re(o,"iterate",c?ms:Rt),{next(){const{value:h,done:p}=f.next();return p?{value:h,done:p}:{value:l?[a(h[0]),a(h[1])]:a(h),done:p}},[Symbol.iterator](){return this}}}}function wn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Xi(e,t){const n={get(r){const o=this.__v_raw,i=se(o),l=se(r);e||(mt(r,l)&&Re(i,"get",r),Re(i,"get",l));const{has:c}=En(i),f=t?vs:e?Mn:Ee;if(c.call(i,r))return f(o.get(r));if(c.call(i,l))return f(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&Re(se(r),"iterate",Rt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=se(o),l=se(r);return e||(mt(r,l)&&Re(i,"has",r),Re(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,c=se(l),f=t?vs:e?Mn:Ee;return!e&&Re(c,"iterate",Rt),l.forEach((a,h)=>r.call(o,f(a),f(h),i))}};return Ae(n,e?{add:wn("add"),set:wn("set"),delete:wn("delete"),clear:wn("clear")}:{add(r){!t&&!$e(r)&&!yt(r)&&(r=se(r));const o=se(this);return En(o).has.call(o,r)||(o.add(r),rt(o,"add",r,r)),this},set(r,o){!t&&!$e(o)&&!yt(o)&&(o=se(o));const i=se(this),{has:l,get:c}=En(i);let f=l.call(i,r);f||(r=se(r),f=l.call(i,r));const a=c.call(i,r);return i.set(r,o),f?mt(o,a)&&rt(i,"set",r,o):rt(i,"add",r,o),this},delete(r){const o=se(this),{has:i,get:l}=En(o);let c=i.call(o,r);c||(r=se(r),c=i.call(o,r)),l&&l.call(o,r);const f=o.delete(r);return c&&rt(o,"delete",r,void 0),f},clear(){const r=se(this),o=r.size!==0,i=r.clear();return o&&rt(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=Zi(r,e,t)}),n}function Bs(e,t){const n=Xi(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(ie(n,r)&&r in s?n:s,r,o)}const el={get:Bs(!1,!1)},tl={get:Bs(!1,!0)},nl={get:Bs(!0,!1)};const Eo=new WeakMap,wo=new WeakMap,Co=new WeakMap,sl=new WeakMap;function rl(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ol(e){return e.__v_skip||!Object.isExtensible(e)?0:rl(Ii(e))}function yn(e){return yt(e)?e:Us(e,!1,Ji,el,Eo)}function Ro(e){return Us(e,!1,Qi,tl,wo)}function Ao(e){return Us(e,!0,zi,nl,Co)}function Us(e,t,n,s,r){if(!he(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=ol(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function vt(e){return yt(e)?vt(e.__v_raw):!!(e&&e.__v_isReactive)}function yt(e){return!!(e&&e.__v_isReadonly)}function $e(e){return!!(e&&e.__v_isShallow)}function Ws(e){return e?!!e.__v_raw:!1}function se(e){const t=e&&e.__v_raw;return t?se(t):e}function Ks(e){return!ie(e,"__v_skip")&&Object.isExtensible(e)&&ps(e,"__v_skip",!0),e}const Ee=e=>he(e)?yn(e):e,Mn=e=>he(e)?Ao(e):e;function ye(e){return e?e.__v_isRef===!0:!1}function Se(e){return To(e,!1)}function il(e){return To(e,!0)}function To(e,t){return ye(e)?e:new ll(e,t)}class ll{constructor(t,n){this.dep=new js,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:se(t),this._value=n?t:Ee(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||$e(t)||yt(t);t=s?t:se(t),mt(t,n)&&(this._rawValue=t,this._value=s?t:Ee(t),this.dep.trigger())}}function $t(e){return ye(e)?e.value:e}const cl={get:(e,t,n)=>t==="__v_raw"?e:$t(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return ye(r)&&!ye(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function xo(e){return vt(e)?e:new Proxy(e,cl)}function al(e){const t=Y(e)?new Array(e.length):{};for(const n in e)t[n]=fl(e,n);return t}class ul{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Ui(se(this._object),this._key)}}function fl(e,t,n){const s=e[t];return ye(s)?s:new ul(e,t,n)}class dl{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new js(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=cn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&de!==this)return ho(this,!0),!0}get value(){const t=this.dep.track();return mo(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function hl(e,t,n=!1){let s,r;return ee(e)?s=e:(s=e.get,r=e.set),new dl(s,r,n)}const Cn={},Dn=new WeakMap;let Et;function pl(e,t=!1,n=Et){if(n){let s=Dn.get(n);s||Dn.set(n,s=[]),s.push(e)}}function gl(e,t,n=ae){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:c}=n,f=F=>r?F:$e(F)||r===!1||r===0?ot(F,1):ot(F);let a,h,p,m,T=!1,I=!1;if(ye(e)?(h=()=>e.value,T=$e(e)):vt(e)?(h=()=>f(e),T=!0):Y(e)?(I=!0,T=e.some(F=>vt(F)||$e(F)),h=()=>e.map(F=>{if(ye(F))return F.value;if(vt(F))return f(F);if(ee(F))return c?c(F,2):F()})):ee(e)?t?h=c?()=>c(e,2):e:h=()=>{if(p){lt();try{p()}finally{ct()}}const F=Et;Et=a;try{return c?c(e,3,[m]):e(m)}finally{Et=F}}:h=Xe,t&&r){const F=h,Z=r===!0?1/0:r;h=()=>ot(F(),Z)}const q=ao(),j=()=>{a.stop(),q&&q.active&&Ds(q.effects,a)};if(o&&t){const F=t;t=(...Z)=>{F(...Z),j()}}let H=I?new Array(e.length).fill(Cn):Cn;const V=F=>{if(!(!(a.flags&1)||!a.dirty&&!F))if(t){const Z=a.run();if(r||T||(I?Z.some((ue,N)=>mt(ue,H[N])):mt(Z,H))){p&&p();const ue=Et;Et=a;try{const N=[Z,H===Cn?void 0:I&&H[0]===Cn?[]:H,m];H=Z,c?c(t,3,N):t(...N)}finally{Et=ue}}}else a.run()};return l&&l(V),a=new uo(h),a.scheduler=i?()=>i(V,!1):V,m=F=>pl(F,!1,a),p=a.onStop=()=>{const F=Dn.get(a);if(F){if(c)c(F,4);else for(const Z of F)Z();Dn.delete(a)}},t?s?V(!0):H=a.run():i?i(V.bind(null,!0),!0):a.run(),j.pause=a.pause.bind(a),j.resume=a.resume.bind(a),j.stop=j,j}function ot(e,t=1/0,n){if(t<=0||!he(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ye(e))ot(e.value,t,n);else if(Y(e))for(let s=0;s<e.length;s++)ot(e[s],t,n);else if(Wt(e)||Ft(e))e.forEach(s=>{ot(s,t,n)});else if(so(e)){for(const s in e)ot(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&ot(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function _n(e,t,n,s){try{return s?e(...s):e()}catch(r){Un(r,t,n)}}function et(e,t,n,s){if(ee(e)){const r=_n(e,t,n,s);return r&&to(r)&&r.catch(o=>{Un(o,t,n)}),r}if(Y(e)){const r=[];for(let o=0;o<e.length;o++)r.push(et(e[o],t,n,s));return r}}function Un(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ae;if(t){let l=t.parent;const c=t.proxy,f=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const a=l.ec;if(a){for(let h=0;h<a.length;h++)if(a[h](e,c,f)===!1)return}l=l.parent}if(o){lt(),_n(o,null,10,[e,c,f]),ct();return}}ml(e,n,r,s,i)}function ml(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const xe=[];let Qe=-1;const Ht=[];let ht=null,Dt=0;const Oo=Promise.resolve();let Nn=null;function Wn(e){const t=Nn||Oo;return e?t.then(this?e.bind(this):e):t}function vl(e){let t=Qe+1,n=xe.length;for(;t<n;){const s=t+n>>>1,r=xe[s],o=un(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function Gs(e){if(!(e.flags&1)){const t=un(e),n=xe[xe.length-1];!n||!(e.flags&2)&&t>=un(n)?xe.push(e):xe.splice(vl(t),0,e),e.flags|=1,Io()}}function Io(){Nn||(Nn=Oo.then(Mo))}function yl(e){Y(e)?Ht.push(...e):ht&&e.id===-1?ht.splice(Dt+1,0,e):e.flags&1||(Ht.push(e),e.flags|=1),Io()}function ir(e,t,n=Qe+1){for(;n<xe.length;n++){const s=xe[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;xe.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Po(e){if(Ht.length){const t=[...new Set(Ht)].sort((n,s)=>un(n)-un(s));if(Ht.length=0,ht){ht.push(...t);return}for(ht=t,Dt=0;Dt<ht.length;Dt++){const n=ht[Dt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}ht=null,Dt=0}}const un=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Mo(e){try{for(Qe=0;Qe<xe.length;Qe++){const t=xe[Qe];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),_n(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Qe<xe.length;Qe++){const t=xe[Qe];t&&(t.flags&=-2)}Qe=-1,xe.length=0,Po(),Nn=null,(xe.length||Ht.length)&&Mo()}}let be=null,Do=null;function Ln(e){const t=be;return be=e,Do=e&&e.type.__scopeId||null,t}function wt(e,t=be,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&mr(-1);const o=Ln(t);let i;try{i=e(...r)}finally{Ln(o),s._d&&mr(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function Vu(e,t){if(be===null)return e;const n=Jn(be),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,c=ae]=t[r];o&&(ee(o)&&(o={mounted:o,updated:o}),o.deep&&ot(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function St(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let c=l.dir[s];c&&(lt(),et(c,n,8,[e.el,l,e,t]),ct())}}const _l=Symbol("_vte"),Sl=e=>e.__isTeleport;function Ys(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ys(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Kn(e,t){return ee(e)?Ae({name:e.name},t,{setup:e}):e}function No(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function en(e,t,n,s,r=!1){if(Y(e)){e.forEach((T,I)=>en(T,t&&(Y(t)?t[I]:t),n,s,r));return}if(kt(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&en(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?Jn(s.component):s.el,i=r?null:o,{i:l,r:c}=e,f=t&&t.r,a=l.refs===ae?l.refs={}:l.refs,h=l.setupState,p=se(h),m=h===ae?()=>!1:T=>ie(p,T);if(f!=null&&f!==c&&(_e(f)?(a[f]=null,m(f)&&(h[f]=null)):ye(f)&&(f.value=null)),ee(c))_n(c,l,12,[i,a]);else{const T=_e(c),I=ye(c);if(T||I){const q=()=>{if(e.f){const j=T?m(c)?h[c]:a[c]:c.value;r?Y(j)&&Ds(j,o):Y(j)?j.includes(o)||j.push(o):T?(a[c]=[o],m(c)&&(h[c]=a[c])):(c.value=[o],e.k&&(a[e.k]=c.value))}else T?(a[c]=i,m(c)&&(h[c]=i)):I&&(c.value=i,e.k&&(a[e.k]=i))};i?(q.id=-1,De(q,n)):q()}}}jn().requestIdleCallback;jn().cancelIdleCallback;const kt=e=>!!e.type.__asyncLoader,Lo=e=>e.type.__isKeepAlive;function bl(e,t){Fo(e,"a",t)}function El(e,t){Fo(e,"da",t)}function Fo(e,t,n=we){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Gn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)Lo(r.parent.vnode)&&wl(s,t,n,r),r=r.parent}}function wl(e,t,n,s){const r=Gn(t,e,s,!0);Ho(()=>{Ds(s[t],r)},n)}function Gn(e,t,n=we,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{lt();const l=Sn(n),c=et(t,n,e,i);return l(),ct(),c});return s?r.unshift(o):r.push(o),o}}const ut=e=>(t,n=we)=>{(!pn||e==="sp")&&Gn(e,(...s)=>t(...s),n)},Cl=ut("bm"),$o=ut("m"),Rl=ut("bu"),Al=ut("u"),Tl=ut("bum"),Ho=ut("um"),xl=ut("sp"),Ol=ut("rtg"),Il=ut("rtc");function Pl(e,t=we){Gn("ec",e,t)}const Ml="components";function ys(e,t){return Nl(Ml,e,!0,t)||e}const Dl=Symbol.for("v-ndc");function Nl(e,t,n=!0,s=!1){const r=be||we;if(r){const o=r.type;{const l=bc(o,!1);if(l&&(l===t||l===Ve(t)||l===Vn(Ve(t))))return o}const i=lr(r[e]||o[e],t)||lr(r.appContext[e],t);return!i&&s?o:i}}function lr(e,t){return e&&(e[t]||e[Ve(t)]||e[Vn(Ve(t))])}function ju(e,t,n,s){let r;const o=n,i=Y(e);if(i||_e(e)){const l=i&&vt(e);let c=!1,f=!1;l&&(c=!$e(e),f=yt(e),e=Bn(e)),r=new Array(e.length);for(let a=0,h=e.length;a<h;a++)r[a]=t(c?f?Mn(Ee(e[a])):Ee(e[a]):e[a],a,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(he(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,f=l.length;c<f;c++){const a=l[c];r[c]=t(e[a],a,c,o)}}else r=[];return r}function Bu(e,t,n={},s,r){if(be.ce||be.parent&&kt(be.parent)&&be.parent.ce)return t!=="default"&&(n.name=t),fn(),ws(Fe,null,[me("slot",n,s)],64);let o=e[t];o&&o._c&&(o._d=!1),fn();const i=o&&ko(o(n)),l=n.key||i&&i.key,c=ws(Fe,{key:(l&&!Be(l)?l:`_${t}`)+(!i&&s?"_fb":"")},i||[],i&&e._===1?64:-2);return o&&o._c&&(o._d=!0),c}function ko(e){return e.some(t=>hn(t)?!(t.type===at||t.type===Fe&&!ko(t.children)):!0)?e:null}const _s=e=>e?ii(e)?Jn(e):_s(e.parent):null,tn=Ae(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>_s(e.parent),$root:e=>_s(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>jo(e),$forceUpdate:e=>e.f||(e.f=()=>{Gs(e.update)}),$nextTick:e=>e.n||(e.n=Wn.bind(e.proxy)),$watch:e=>nc.bind(e)}),os=(e,t)=>e!==ae&&!e.__isScriptSetup&&ie(e,t),Ll={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:c}=e;let f;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(os(s,t))return i[t]=1,s[t];if(r!==ae&&ie(r,t))return i[t]=2,r[t];if((f=e.propsOptions[0])&&ie(f,t))return i[t]=3,o[t];if(n!==ae&&ie(n,t))return i[t]=4,n[t];Ss&&(i[t]=0)}}const a=tn[t];let h,p;if(a)return t==="$attrs"&&Re(e.attrs,"get",""),a(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(n!==ae&&ie(n,t))return i[t]=4,n[t];if(p=c.config.globalProperties,ie(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return os(r,t)?(r[t]=n,!0):s!==ae&&ie(s,t)?(s[t]=n,!0):ie(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==ae&&ie(e,i)||os(t,i)||(l=o[0])&&ie(l,i)||ie(s,i)||ie(tn,i)||ie(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ie(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function cr(e){return Y(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Ss=!0;function Fl(e){const t=jo(e),n=e.proxy,s=e.ctx;Ss=!1,t.beforeCreate&&ar(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:c,inject:f,created:a,beforeMount:h,mounted:p,beforeUpdate:m,updated:T,activated:I,deactivated:q,beforeDestroy:j,beforeUnmount:H,destroyed:V,unmounted:F,render:Z,renderTracked:ue,renderTriggered:N,errorCaptured:b,serverPrefetch:E,expose:v,inheritAttrs:L,components:z,directives:A,filters:R}=t;if(f&&$l(f,s,null),i)for(const k in i){const J=i[k];ee(J)&&(s[k]=J.bind(n))}if(r){const k=r.call(n,n);he(k)&&(e.data=yn(k))}if(Ss=!0,o)for(const k in o){const J=o[k],ge=ee(J)?J.bind(n,n):ee(J.get)?J.get.bind(n,n):Xe,We=!ee(J)&&ee(J.set)?J.set.bind(n):Xe,Ke=te({get:ge,set:We});Object.defineProperty(s,k,{enumerable:!0,configurable:!0,get:()=>Ke.value,set:Oe=>Ke.value=Oe})}if(l)for(const k in l)Vo(l[k],s,n,k);if(c){const k=ee(c)?c.call(n):c;Reflect.ownKeys(k).forEach(J=>{An(J,k[J])})}a&&ar(a,e,"c");function B(k,J){Y(J)?J.forEach(ge=>k(ge.bind(n))):J&&k(J.bind(n))}if(B(Cl,h),B($o,p),B(Rl,m),B(Al,T),B(bl,I),B(El,q),B(Pl,b),B(Il,ue),B(Ol,N),B(Tl,H),B(Ho,F),B(xl,E),Y(v))if(v.length){const k=e.exposed||(e.exposed={});v.forEach(J=>{Object.defineProperty(k,J,{get:()=>n[J],set:ge=>n[J]=ge})})}else e.exposed||(e.exposed={});Z&&e.render===Xe&&(e.render=Z),L!=null&&(e.inheritAttrs=L),z&&(e.components=z),A&&(e.directives=A),E&&No(e)}function $l(e,t,n=Xe){Y(e)&&(e=bs(e));for(const s in e){const r=e[s];let o;he(r)?"default"in r?o=He(r.from||s,r.default,!0):o=He(r.from||s):o=He(r),ye(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function ar(e,t,n){et(Y(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Vo(e,t,n,s){let r=s.includes(".")?ei(n,s):()=>n[s];if(_e(e)){const o=t[e];ee(o)&&nn(r,o)}else if(ee(e))nn(r,e.bind(n));else if(he(e))if(Y(e))e.forEach(o=>Vo(o,t,n,s));else{const o=ee(e.handler)?e.handler.bind(n):t[e.handler];ee(o)&&nn(r,o,e)}}function jo(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(f=>Fn(c,f,i,!0)),Fn(c,t,i)),he(t)&&o.set(t,c),c}function Fn(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&Fn(e,o,n,!0),r&&r.forEach(i=>Fn(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=Hl[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Hl={data:ur,props:fr,emits:fr,methods:zt,computed:zt,beforeCreate:Te,created:Te,beforeMount:Te,mounted:Te,beforeUpdate:Te,updated:Te,beforeDestroy:Te,beforeUnmount:Te,destroyed:Te,unmounted:Te,activated:Te,deactivated:Te,errorCaptured:Te,serverPrefetch:Te,components:zt,directives:zt,watch:Vl,provide:ur,inject:kl};function ur(e,t){return t?e?function(){return Ae(ee(e)?e.call(this,this):e,ee(t)?t.call(this,this):t)}:t:e}function kl(e,t){return zt(bs(e),bs(t))}function bs(e){if(Y(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Te(e,t){return e?[...new Set([].concat(e,t))]:t}function zt(e,t){return e?Ae(Object.create(null),e,t):t}function fr(e,t){return e?Y(e)&&Y(t)?[...new Set([...e,...t])]:Ae(Object.create(null),cr(e),cr(t??{})):t}function Vl(e,t){if(!e)return t;if(!t)return e;const n=Ae(Object.create(null),e);for(const s in t)n[s]=Te(e[s],t[s]);return n}function Bo(){return{app:null,config:{isNativeTag:xi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let jl=0;function Bl(e,t){return function(s,r=null){ee(s)||(s=Ae({},s)),r!=null&&!he(r)&&(r=null);const o=Bo(),i=new WeakSet,l=[];let c=!1;const f=o.app={_uid:jl++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:wc,get config(){return o.config},set config(a){},use(a,...h){return i.has(a)||(a&&ee(a.install)?(i.add(a),a.install(f,...h)):ee(a)&&(i.add(a),a(f,...h))),f},mixin(a){return o.mixins.includes(a)||o.mixins.push(a),f},component(a,h){return h?(o.components[a]=h,f):o.components[a]},directive(a,h){return h?(o.directives[a]=h,f):o.directives[a]},mount(a,h,p){if(!c){const m=f._ceVNode||me(s,r);return m.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),e(m,a,p),c=!0,f._container=a,a.__vue_app__=f,Jn(m.component)}},onUnmount(a){l.push(a)},unmount(){c&&(et(l,f._instance,16),e(null,f._container),delete f._container.__vue_app__)},provide(a,h){return o.provides[a]=h,f},runWithContext(a){const h=At;At=f;try{return a()}finally{At=h}}};return f}}let At=null;function An(e,t){if(we){let n=we.provides;const s=we.parent&&we.parent.provides;s===n&&(n=we.provides=Object.create(s)),n[e]=t}}function He(e,t,n=!1){const s=we||be;if(s||At){let r=At?At._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&ee(t)?t.call(s&&s.proxy):t}}function Ul(){return!!(we||be||At)}const Uo={},Wo=()=>Object.create(Uo),Ko=e=>Object.getPrototypeOf(e)===Uo;function Wl(e,t,n,s=!1){const r={},o=Wo();e.propsDefaults=Object.create(null),Go(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:Ro(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Kl(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=se(r),[c]=e.propsOptions;let f=!1;if((s||i>0)&&!(i&16)){if(i&8){const a=e.vnode.dynamicProps;for(let h=0;h<a.length;h++){let p=a[h];if(Yn(e.emitsOptions,p))continue;const m=t[p];if(c)if(ie(o,p))m!==o[p]&&(o[p]=m,f=!0);else{const T=Ve(p);r[T]=Es(c,l,T,m,e,!1)}else m!==o[p]&&(o[p]=m,f=!0)}}}else{Go(e,t,r,o)&&(f=!0);let a;for(const h in l)(!t||!ie(t,h)&&((a=xt(h))===h||!ie(t,a)))&&(c?n&&(n[h]!==void 0||n[a]!==void 0)&&(r[h]=Es(c,l,h,void 0,e,!0)):delete r[h]);if(o!==l)for(const h in o)(!t||!ie(t,h))&&(delete o[h],f=!0)}f&&rt(e.attrs,"set","")}function Go(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(Qt(c))continue;const f=t[c];let a;r&&ie(r,a=Ve(c))?!o||!o.includes(a)?n[a]=f:(l||(l={}))[a]=f:Yn(e.emitsOptions,c)||(!(c in s)||f!==s[c])&&(s[c]=f,i=!0)}if(o){const c=se(n),f=l||ae;for(let a=0;a<o.length;a++){const h=o[a];n[h]=Es(r,c,h,f[h],e,!ie(f,h))}}return i}function Es(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=ie(i,"default");if(l&&s===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&ee(c)){const{propsDefaults:f}=r;if(n in f)s=f[n];else{const a=Sn(r);s=f[n]=c.call(null,t),a()}}else s=c;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===xt(n))&&(s=!0))}return s}const Gl=new WeakMap;function Yo(e,t,n=!1){const s=n?Gl:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let c=!1;if(!ee(e)){const a=h=>{c=!0;const[p,m]=Yo(h,t,!0);Ae(i,p),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!o&&!c)return he(e)&&s.set(e,Lt),Lt;if(Y(o))for(let a=0;a<o.length;a++){const h=Ve(o[a]);dr(h)&&(i[h]=ae)}else if(o)for(const a in o){const h=Ve(a);if(dr(h)){const p=o[a],m=i[h]=Y(p)||ee(p)?{type:p}:Ae({},p),T=m.type;let I=!1,q=!0;if(Y(T))for(let j=0;j<T.length;++j){const H=T[j],V=ee(H)&&H.name;if(V==="Boolean"){I=!0;break}else V==="String"&&(q=!1)}else I=ee(T)&&T.name==="Boolean";m[0]=I,m[1]=q,(I||ie(m,"default"))&&l.push(h)}}const f=[i,l];return he(e)&&s.set(e,f),f}function dr(e){return e[0]!=="$"&&!Qt(e)}const qs=e=>e[0]==="_"||e==="$stable",Js=e=>Y(e)?e.map(Ze):[Ze(e)],Yl=(e,t,n)=>{if(t._n)return t;const s=wt((...r)=>Js(t(...r)),n);return s._c=!1,s},qo=(e,t,n)=>{const s=e._ctx;for(const r in e){if(qs(r))continue;const o=e[r];if(ee(o))t[r]=Yl(r,o,s);else if(o!=null){const i=Js(o);t[r]=()=>i}}},Jo=(e,t)=>{const n=Js(t);e.slots.default=()=>n},zo=(e,t,n)=>{for(const s in t)(n||!qs(s))&&(e[s]=t[s])},ql=(e,t,n)=>{const s=e.slots=Wo();if(e.vnode.shapeFlag&32){const r=t.__;r&&ps(s,"__",r,!0);const o=t._;o?(zo(s,t,n),n&&ps(s,"_",o,!0)):qo(t,s)}else t&&Jo(e,t)},Jl=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=ae;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:zo(r,t,n):(o=!t.$stable,qo(t,r)),i=t}else t&&(Jo(e,t),i={default:1});if(o)for(const l in r)!qs(l)&&i[l]==null&&delete r[l]},De=ac;function zl(e){return Ql(e)}function Ql(e,t){const n=jn();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:c,setText:f,setElementText:a,parentNode:h,nextSibling:p,setScopeId:m=Xe,insertStaticContent:T}=e,I=(u,d,g,y=null,w=null,S=null,P=void 0,O=null,x=!!d.dynamicChildren)=>{if(u===d)return;u&&!Yt(u,d)&&(y=_(u),Oe(u,w,S,!0),u=null),d.patchFlag===-2&&(x=!1,d.dynamicChildren=null);const{type:C,ref:G,shapeFlag:D}=d;switch(C){case qn:q(u,d,g,y);break;case at:j(u,d,g,y);break;case Tn:u==null&&H(d,g,y,P);break;case Fe:z(u,d,g,y,w,S,P,O,x);break;default:D&1?Z(u,d,g,y,w,S,P,O,x):D&6?A(u,d,g,y,w,S,P,O,x):(D&64||D&128)&&C.process(u,d,g,y,w,S,P,O,x,U)}G!=null&&w?en(G,u&&u.ref,S,d||u,!d):G==null&&u&&u.ref!=null&&en(u.ref,null,S,u,!0)},q=(u,d,g,y)=>{if(u==null)s(d.el=l(d.children),g,y);else{const w=d.el=u.el;d.children!==u.children&&f(w,d.children)}},j=(u,d,g,y)=>{u==null?s(d.el=c(d.children||""),g,y):d.el=u.el},H=(u,d,g,y)=>{[u.el,u.anchor]=T(u.children,d,g,y,u.el,u.anchor)},V=({el:u,anchor:d},g,y)=>{let w;for(;u&&u!==d;)w=p(u),s(u,g,y),u=w;s(d,g,y)},F=({el:u,anchor:d})=>{let g;for(;u&&u!==d;)g=p(u),r(u),u=g;r(d)},Z=(u,d,g,y,w,S,P,O,x)=>{d.type==="svg"?P="svg":d.type==="math"&&(P="mathml"),u==null?ue(d,g,y,w,S,P,O,x):E(u,d,w,S,P,O,x)},ue=(u,d,g,y,w,S,P,O)=>{let x,C;const{props:G,shapeFlag:D,transition:K,dirs:X}=u;if(x=u.el=i(u.type,S,G&&G.is,G),D&8?a(x,u.children):D&16&&b(u.children,x,null,y,w,is(u,S),P,O),X&&St(u,null,y,"created"),N(x,u,u.scopeId,P,y),G){for(const fe in G)fe!=="value"&&!Qt(fe)&&o(x,fe,null,G[fe],S,y);"value"in G&&o(x,"value",null,G.value,S),(C=G.onVnodeBeforeMount)&&Je(C,y,u)}X&&St(u,null,y,"beforeMount");const ne=Zl(w,K);ne&&K.beforeEnter(x),s(x,d,g),((C=G&&G.onVnodeMounted)||ne||X)&&De(()=>{C&&Je(C,y,u),ne&&K.enter(x),X&&St(u,null,y,"mounted")},w)},N=(u,d,g,y,w)=>{if(g&&m(u,g),y)for(let S=0;S<y.length;S++)m(u,y[S]);if(w){let S=w.subTree;if(d===S||ni(S.type)&&(S.ssContent===d||S.ssFallback===d)){const P=w.vnode;N(u,P,P.scopeId,P.slotScopeIds,w.parent)}}},b=(u,d,g,y,w,S,P,O,x=0)=>{for(let C=x;C<u.length;C++){const G=u[C]=O?pt(u[C]):Ze(u[C]);I(null,G,d,g,y,w,S,P,O)}},E=(u,d,g,y,w,S,P)=>{const O=d.el=u.el;let{patchFlag:x,dynamicChildren:C,dirs:G}=d;x|=u.patchFlag&16;const D=u.props||ae,K=d.props||ae;let X;if(g&&bt(g,!1),(X=K.onVnodeBeforeUpdate)&&Je(X,g,d,u),G&&St(d,u,g,"beforeUpdate"),g&&bt(g,!0),(D.innerHTML&&K.innerHTML==null||D.textContent&&K.textContent==null)&&a(O,""),C?v(u.dynamicChildren,C,O,g,y,is(d,w),S):P||J(u,d,O,null,g,y,is(d,w),S,!1),x>0){if(x&16)L(O,D,K,g,w);else if(x&2&&D.class!==K.class&&o(O,"class",null,K.class,w),x&4&&o(O,"style",D.style,K.style,w),x&8){const ne=d.dynamicProps;for(let fe=0;fe<ne.length;fe++){const le=ne[fe],Ie=D[le],Pe=K[le];(Pe!==Ie||le==="value")&&o(O,le,Ie,Pe,w,g)}}x&1&&u.children!==d.children&&a(O,d.children)}else!P&&C==null&&L(O,D,K,g,w);((X=K.onVnodeUpdated)||G)&&De(()=>{X&&Je(X,g,d,u),G&&St(d,u,g,"updated")},y)},v=(u,d,g,y,w,S,P)=>{for(let O=0;O<d.length;O++){const x=u[O],C=d[O],G=x.el&&(x.type===Fe||!Yt(x,C)||x.shapeFlag&198)?h(x.el):g;I(x,C,G,null,y,w,S,P,!0)}},L=(u,d,g,y,w)=>{if(d!==g){if(d!==ae)for(const S in d)!Qt(S)&&!(S in g)&&o(u,S,d[S],null,w,y);for(const S in g){if(Qt(S))continue;const P=g[S],O=d[S];P!==O&&S!=="value"&&o(u,S,O,P,w,y)}"value"in g&&o(u,"value",d.value,g.value,w)}},z=(u,d,g,y,w,S,P,O,x)=>{const C=d.el=u?u.el:l(""),G=d.anchor=u?u.anchor:l("");let{patchFlag:D,dynamicChildren:K,slotScopeIds:X}=d;X&&(O=O?O.concat(X):X),u==null?(s(C,g,y),s(G,g,y),b(d.children||[],g,G,w,S,P,O,x)):D>0&&D&64&&K&&u.dynamicChildren?(v(u.dynamicChildren,K,g,w,S,P,O),(d.key!=null||w&&d===w.subTree)&&Qo(u,d,!0)):J(u,d,g,G,w,S,P,O,x)},A=(u,d,g,y,w,S,P,O,x)=>{d.slotScopeIds=O,u==null?d.shapeFlag&512?w.ctx.activate(d,g,y,P,x):R(d,g,y,w,S,P,x):W(u,d,x)},R=(u,d,g,y,w,S,P)=>{const O=u.component=mc(u,y,w);if(Lo(u)&&(O.ctx.renderer=U),vc(O,!1,P),O.asyncDep){if(w&&w.registerDep(O,B,P),!u.el){const x=O.subTree=me(at);j(null,x,d,g)}}else B(O,u,d,g,w,S,P)},W=(u,d,g)=>{const y=d.component=u.component;if(lc(u,d,g))if(y.asyncDep&&!y.asyncResolved){k(y,d,g);return}else y.next=d,y.update();else d.el=u.el,y.vnode=d},B=(u,d,g,y,w,S,P)=>{const O=()=>{if(u.isMounted){let{next:D,bu:K,u:X,parent:ne,vnode:fe}=u;{const Ye=Zo(u);if(Ye){D&&(D.el=fe.el,k(u,D,P)),Ye.asyncDep.then(()=>{u.isUnmounted||O()});return}}let le=D,Ie;bt(u,!1),D?(D.el=fe.el,k(u,D,P)):D=fe,K&&Rn(K),(Ie=D.props&&D.props.onVnodeBeforeUpdate)&&Je(Ie,ne,D,fe),bt(u,!0);const Pe=pr(u),Ge=u.subTree;u.subTree=Pe,I(Ge,Pe,h(Ge.el),_(Ge),u,w,S),D.el=Pe.el,le===null&&cc(u,Pe.el),X&&De(X,w),(Ie=D.props&&D.props.onVnodeUpdated)&&De(()=>Je(Ie,ne,D,fe),w)}else{let D;const{el:K,props:X}=d,{bm:ne,m:fe,parent:le,root:Ie,type:Pe}=u,Ge=kt(d);bt(u,!1),ne&&Rn(ne),!Ge&&(D=X&&X.onVnodeBeforeMount)&&Je(D,le,d),bt(u,!0);{Ie.ce&&Ie.ce._def.shadowRoot!==!1&&Ie.ce._injectChildStyle(Pe);const Ye=u.subTree=pr(u);I(null,Ye,g,y,u,w,S),d.el=Ye.el}if(fe&&De(fe,w),!Ge&&(D=X&&X.onVnodeMounted)){const Ye=d;De(()=>Je(D,le,Ye),w)}(d.shapeFlag&256||le&&kt(le.vnode)&&le.vnode.shapeFlag&256)&&u.a&&De(u.a,w),u.isMounted=!0,d=g=y=null}};u.scope.on();const x=u.effect=new uo(O);u.scope.off();const C=u.update=x.run.bind(x),G=u.job=x.runIfDirty.bind(x);G.i=u,G.id=u.uid,x.scheduler=()=>Gs(G),bt(u,!0),C()},k=(u,d,g)=>{d.component=u;const y=u.vnode.props;u.vnode=d,u.next=null,Kl(u,d.props,y,g),Jl(u,d.children,g),lt(),ir(u),ct()},J=(u,d,g,y,w,S,P,O,x=!1)=>{const C=u&&u.children,G=u?u.shapeFlag:0,D=d.children,{patchFlag:K,shapeFlag:X}=d;if(K>0){if(K&128){We(C,D,g,y,w,S,P,O,x);return}else if(K&256){ge(C,D,g,y,w,S,P,O,x);return}}X&8?(G&16&&Le(C,w,S),D!==C&&a(g,D)):G&16?X&16?We(C,D,g,y,w,S,P,O,x):Le(C,w,S,!0):(G&8&&a(g,""),X&16&&b(D,g,y,w,S,P,O,x))},ge=(u,d,g,y,w,S,P,O,x)=>{u=u||Lt,d=d||Lt;const C=u.length,G=d.length,D=Math.min(C,G);let K;for(K=0;K<D;K++){const X=d[K]=x?pt(d[K]):Ze(d[K]);I(u[K],X,g,null,w,S,P,O,x)}C>G?Le(u,w,S,!0,!1,D):b(d,g,y,w,S,P,O,x,D)},We=(u,d,g,y,w,S,P,O,x)=>{let C=0;const G=d.length;let D=u.length-1,K=G-1;for(;C<=D&&C<=K;){const X=u[C],ne=d[C]=x?pt(d[C]):Ze(d[C]);if(Yt(X,ne))I(X,ne,g,null,w,S,P,O,x);else break;C++}for(;C<=D&&C<=K;){const X=u[D],ne=d[K]=x?pt(d[K]):Ze(d[K]);if(Yt(X,ne))I(X,ne,g,null,w,S,P,O,x);else break;D--,K--}if(C>D){if(C<=K){const X=K+1,ne=X<G?d[X].el:y;for(;C<=K;)I(null,d[C]=x?pt(d[C]):Ze(d[C]),g,ne,w,S,P,O,x),C++}}else if(C>K)for(;C<=D;)Oe(u[C],w,S,!0),C++;else{const X=C,ne=C,fe=new Map;for(C=ne;C<=K;C++){const Me=d[C]=x?pt(d[C]):Ze(d[C]);Me.key!=null&&fe.set(Me.key,C)}let le,Ie=0;const Pe=K-ne+1;let Ge=!1,Ye=0;const Kt=new Array(Pe);for(C=0;C<Pe;C++)Kt[C]=0;for(C=X;C<=D;C++){const Me=u[C];if(Ie>=Pe){Oe(Me,w,S,!0);continue}let qe;if(Me.key!=null)qe=fe.get(Me.key);else for(le=ne;le<=K;le++)if(Kt[le-ne]===0&&Yt(Me,d[le])){qe=le;break}qe===void 0?Oe(Me,w,S,!0):(Kt[qe-ne]=C+1,qe>=Ye?Ye=qe:Ge=!0,I(Me,d[qe],g,null,w,S,P,O,x),Ie++)}const er=Ge?Xl(Kt):Lt;for(le=er.length-1,C=Pe-1;C>=0;C--){const Me=ne+C,qe=d[Me],tr=Me+1<G?d[Me+1].el:y;Kt[C]===0?I(null,qe,g,tr,w,S,P,O,x):Ge&&(le<0||C!==er[le]?Ke(qe,g,tr,2):le--)}}},Ke=(u,d,g,y,w=null)=>{const{el:S,type:P,transition:O,children:x,shapeFlag:C}=u;if(C&6){Ke(u.component.subTree,d,g,y);return}if(C&128){u.suspense.move(d,g,y);return}if(C&64){P.move(u,d,g,U);return}if(P===Fe){s(S,d,g);for(let D=0;D<x.length;D++)Ke(x[D],d,g,y);s(u.anchor,d,g);return}if(P===Tn){V(u,d,g);return}if(y!==2&&C&1&&O)if(y===0)O.beforeEnter(S),s(S,d,g),De(()=>O.enter(S),w);else{const{leave:D,delayLeave:K,afterLeave:X}=O,ne=()=>{u.ctx.isUnmounted?r(S):s(S,d,g)},fe=()=>{D(S,()=>{ne(),X&&X()})};K?K(S,ne,fe):fe()}else s(S,d,g)},Oe=(u,d,g,y=!1,w=!1)=>{const{type:S,props:P,ref:O,children:x,dynamicChildren:C,shapeFlag:G,patchFlag:D,dirs:K,cacheIndex:X}=u;if(D===-2&&(w=!1),O!=null&&(lt(),en(O,null,g,u,!0),ct()),X!=null&&(d.renderCache[X]=void 0),G&256){d.ctx.deactivate(u);return}const ne=G&1&&K,fe=!kt(u);let le;if(fe&&(le=P&&P.onVnodeBeforeUnmount)&&Je(le,d,u),G&6)bn(u.component,g,y);else{if(G&128){u.suspense.unmount(g,y);return}ne&&St(u,null,d,"beforeUnmount"),G&64?u.type.remove(u,d,g,U,y):C&&!C.hasOnce&&(S!==Fe||D>0&&D&64)?Le(C,d,g,!1,!0):(S===Fe&&D&384||!w&&G&16)&&Le(x,d,g),y&&Ot(u)}(fe&&(le=P&&P.onVnodeUnmounted)||ne)&&De(()=>{le&&Je(le,d,u),ne&&St(u,null,d,"unmounted")},g)},Ot=u=>{const{type:d,el:g,anchor:y,transition:w}=u;if(d===Fe){It(g,y);return}if(d===Tn){F(u);return}const S=()=>{r(g),w&&!w.persisted&&w.afterLeave&&w.afterLeave()};if(u.shapeFlag&1&&w&&!w.persisted){const{leave:P,delayLeave:O}=w,x=()=>P(g,S);O?O(u.el,S,x):x()}else S()},It=(u,d)=>{let g;for(;u!==d;)g=p(u),r(u),u=g;r(d)},bn=(u,d,g)=>{const{bum:y,scope:w,job:S,subTree:P,um:O,m:x,a:C,parent:G,slots:{__:D}}=u;hr(x),hr(C),y&&Rn(y),G&&Y(D)&&D.forEach(K=>{G.renderCache[K]=void 0}),w.stop(),S&&(S.flags|=8,Oe(P,u,d,g)),O&&De(O,d),De(()=>{u.isUnmounted=!0},d),d&&d.pendingBranch&&!d.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===d.pendingId&&(d.deps--,d.deps===0&&d.resolve())},Le=(u,d,g,y=!1,w=!1,S=0)=>{for(let P=S;P<u.length;P++)Oe(u[P],d,g,y,w)},_=u=>{if(u.shapeFlag&6)return _(u.component.subTree);if(u.shapeFlag&128)return u.suspense.next();const d=p(u.anchor||u.el),g=d&&d[_l];return g?p(g):d};let $=!1;const M=(u,d,g)=>{u==null?d._vnode&&Oe(d._vnode,null,null,!0):I(d._vnode||null,u,d,null,null,null,g),d._vnode=u,$||($=!0,ir(),Po(),$=!1)},U={p:I,um:Oe,m:Ke,r:Ot,mt:R,mc:b,pc:J,pbc:v,n:_,o:e};return{render:M,hydrate:void 0,createApp:Bl(M)}}function is({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function bt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Zl(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Qo(e,t,n=!1){const s=e.children,r=t.children;if(Y(s)&&Y(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=pt(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&Qo(i,l)),l.type===qn&&(l.el=i.el),l.type===at&&!l.el&&(l.el=i.el)}}function Xl(e){const t=e.slice(),n=[0];let s,r,o,i,l;const c=e.length;for(s=0;s<c;s++){const f=e[s];if(f!==0){if(r=n[n.length-1],e[r]<f){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<f?o=l+1:i=l;f<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Zo(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Zo(t)}function hr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ec=Symbol.for("v-scx"),tc=()=>He(ec);function nn(e,t,n){return Xo(e,t,n)}function Xo(e,t,n=ae){const{immediate:s,deep:r,flush:o,once:i}=n,l=Ae({},n),c=t&&s||!t&&o!=="post";let f;if(pn){if(o==="sync"){const m=tc();f=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=Xe,m.resume=Xe,m.pause=Xe,m}}const a=we;l.call=(m,T,I)=>et(m,a,T,I);let h=!1;o==="post"?l.scheduler=m=>{De(m,a&&a.suspense)}:o!=="sync"&&(h=!0,l.scheduler=(m,T)=>{T?m():Gs(m)}),l.augmentJob=m=>{t&&(m.flags|=4),h&&(m.flags|=2,a&&(m.id=a.uid,m.i=a))};const p=gl(e,t,l);return pn&&(f?f.push(p):c&&p()),p}function nc(e,t,n){const s=this.proxy,r=_e(e)?e.includes(".")?ei(s,e):()=>s[e]:e.bind(s,s);let o;ee(t)?o=t:(o=t.handler,n=t);const i=Sn(this),l=Xo(r,o.bind(s),n);return i(),l}function ei(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const sc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Ve(t)}Modifiers`]||e[`${xt(t)}Modifiers`];function rc(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||ae;let r=n;const o=t.startsWith("update:"),i=o&&sc(s,t.slice(7));i&&(i.trim&&(r=n.map(a=>_e(a)?a.trim():a)),i.number&&(r=n.map(In)));let l,c=s[l=es(t)]||s[l=es(Ve(t))];!c&&o&&(c=s[l=es(xt(t))]),c&&et(c,e,6,r);const f=s[l+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,et(f,e,6,r)}}function ti(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!ee(e)){const c=f=>{const a=ti(f,t,!0);a&&(l=!0,Ae(i,a))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(he(e)&&s.set(e,null),null):(Y(o)?o.forEach(c=>i[c]=null):Ae(i,o),he(e)&&s.set(e,i),i)}function Yn(e,t){return!e||!Hn(t)?!1:(t=t.slice(2).replace(/Once$/,""),ie(e,t[0].toLowerCase()+t.slice(1))||ie(e,xt(t))||ie(e,t))}function pr(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:c,render:f,renderCache:a,props:h,data:p,setupState:m,ctx:T,inheritAttrs:I}=e,q=Ln(e);let j,H;try{if(n.shapeFlag&4){const F=r||s,Z=F;j=Ze(f.call(Z,F,a,h,m,p,T)),H=l}else{const F=t;j=Ze(F.length>1?F(h,{attrs:l,slots:i,emit:c}):F(h,null)),H=t.props?l:oc(l)}}catch(F){sn.length=0,Un(F,e,1),j=me(at)}let V=j;if(H&&I!==!1){const F=Object.keys(H),{shapeFlag:Z}=V;F.length&&Z&7&&(o&&F.some(Ms)&&(H=ic(H,o)),V=Vt(V,H,!1,!0))}return n.dirs&&(V=Vt(V,null,!1,!0),V.dirs=V.dirs?V.dirs.concat(n.dirs):n.dirs),n.transition&&Ys(V,n.transition),j=V,Ln(q),j}const oc=e=>{let t;for(const n in e)(n==="class"||n==="style"||Hn(n))&&((t||(t={}))[n]=e[n]);return t},ic=(e,t)=>{const n={};for(const s in e)(!Ms(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function lc(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:c}=t,f=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?gr(s,i,f):!!i;if(c&8){const a=t.dynamicProps;for(let h=0;h<a.length;h++){const p=a[h];if(i[p]!==s[p]&&!Yn(f,p))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?gr(s,i,f):!0:!!i;return!1}function gr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!Yn(n,o))return!0}return!1}function cc({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const ni=e=>e.__isSuspense;function ac(e,t){t&&t.pendingBranch?Y(e)?t.effects.push(...e):t.effects.push(e):yl(e)}const Fe=Symbol.for("v-fgt"),qn=Symbol.for("v-txt"),at=Symbol.for("v-cmt"),Tn=Symbol.for("v-stc"),sn=[];let Ne=null;function fn(e=!1){sn.push(Ne=e?null:[])}function uc(){sn.pop(),Ne=sn[sn.length-1]||null}let dn=1;function mr(e,t=!1){dn+=e,e<0&&Ne&&t&&(Ne.hasOnce=!0)}function si(e){return e.dynamicChildren=dn>0?Ne||Lt:null,uc(),dn>0&&Ne&&Ne.push(e),e}function ri(e,t,n,s,r,o){return si(pe(e,t,n,s,r,o,!0))}function ws(e,t,n,s,r){return si(me(e,t,n,s,r,!0))}function hn(e){return e?e.__v_isVNode===!0:!1}function Yt(e,t){return e.type===t.type&&e.key===t.key}const oi=({key:e})=>e??null,xn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?_e(e)||ye(e)||ee(e)?{i:be,r:e,k:t,f:!!n}:e:null);function pe(e,t=null,n=null,s=0,r=null,o=e===Fe?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&oi(t),ref:t&&xn(t),scopeId:Do,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:be};return l?(zs(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=_e(n)?8:16),dn>0&&!i&&Ne&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&Ne.push(c),c}const me=fc;function fc(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===Dl)&&(e=at),hn(e)){const l=Vt(e,t,!0);return n&&zs(l,n),dn>0&&!o&&Ne&&(l.shapeFlag&6?Ne[Ne.indexOf(e)]=l:Ne.push(l)),l.patchFlag=-2,l}if(Ec(e)&&(e=e.__vccOpts),t){t=dc(t);let{class:l,style:c}=t;l&&!_e(l)&&(t.class=Fs(l)),he(c)&&(Ws(c)&&!Y(c)&&(c=Ae({},c)),t.style=Ls(c))}const i=_e(e)?1:ni(e)?128:Sl(e)?64:he(e)?4:ee(e)?2:0;return pe(e,t,n,s,r,i,o,!0)}function dc(e){return e?Ws(e)||Ko(e)?Ae({},e):e:null}function Vt(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:c}=e,f=t?hc(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&oi(f),ref:t&&t.ref?n&&o?Y(o)?o.concat(xn(t)):[o,xn(t)]:xn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Fe?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Vt(e.ssContent),ssFallback:e.ssFallback&&Vt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Ys(a,c.clone(a)),a}function Ct(e=" ",t=0){return me(qn,null,e,t)}function vr(e,t){const n=me(Tn,null,e);return n.staticCount=t,n}function Uu(e="",t=!1){return t?(fn(),ws(at,null,e)):me(at,null,e)}function Ze(e){return e==null||typeof e=="boolean"?me(at):Y(e)?me(Fe,null,e.slice()):hn(e)?pt(e):me(qn,null,String(e))}function pt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Vt(e)}function zs(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(Y(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),zs(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!Ko(t)?t._ctx=be:r===3&&be&&(be.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else ee(t)?(t={default:t,_ctx:be},n=32):(t=String(t),s&64?(n=16,t=[Ct(t)]):n=8);e.children=t,e.shapeFlag|=n}function hc(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Fs([t.class,s.class]));else if(r==="style")t.style=Ls([t.style,s.style]);else if(Hn(r)){const o=t[r],i=s[r];i&&o!==i&&!(Y(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function Je(e,t,n,s=null){et(e,t,7,[n,s])}const pc=Bo();let gc=0;function mc(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||pc,o={uid:gc++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new lo(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Yo(s,r),emitsOptions:ti(s,r),emit:null,emitted:null,propsDefaults:ae,inheritAttrs:s.inheritAttrs,ctx:ae,data:ae,props:ae,attrs:ae,slots:ae,refs:ae,setupState:ae,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=rc.bind(null,o),e.ce&&e.ce(o),o}let we=null,$n,Cs;{const e=jn(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};$n=t("__VUE_INSTANCE_SETTERS__",n=>we=n),Cs=t("__VUE_SSR_SETTERS__",n=>pn=n)}const Sn=e=>{const t=we;return $n(e),e.scope.on(),()=>{e.scope.off(),$n(t)}},yr=()=>{we&&we.scope.off(),$n(null)};function ii(e){return e.vnode.shapeFlag&4}let pn=!1;function vc(e,t=!1,n=!1){t&&Cs(t);const{props:s,children:r}=e.vnode,o=ii(e);Wl(e,s,o,t),ql(e,r,n||t);const i=o?yc(e,t):void 0;return t&&Cs(!1),i}function yc(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Ll);const{setup:s}=n;if(s){lt();const r=e.setupContext=s.length>1?Sc(e):null,o=Sn(e),i=_n(s,e,0,[e.props,r]),l=to(i);if(ct(),o(),(l||e.sp)&&!kt(e)&&No(e),l){if(i.then(yr,yr),t)return i.then(c=>{_r(e,c)}).catch(c=>{Un(c,e,0)});e.asyncDep=i}else _r(e,i)}else li(e)}function _r(e,t,n){ee(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:he(t)&&(e.setupState=xo(t)),li(e)}function li(e,t,n){const s=e.type;e.render||(e.render=s.render||Xe);{const r=Sn(e);lt();try{Fl(e)}finally{ct(),r()}}}const _c={get(e,t){return Re(e,"get",""),e[t]}};function Sc(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,_c),slots:e.slots,emit:e.emit,expose:t}}function Jn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(xo(Ks(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in tn)return tn[n](e)},has(t,n){return n in t||n in tn}})):e.proxy}function bc(e,t=!0){return ee(e)?e.displayName||e.name:e.name||t&&e.__name}function Ec(e){return ee(e)&&"__vccOpts"in e}const te=(e,t)=>hl(e,t,pn);function ci(e,t,n){const s=arguments.length;return s===2?he(t)&&!Y(t)?hn(t)?me(e,null,[t]):me(e,t):me(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&hn(n)&&(n=[n]),me(e,t,n))}const wc="3.5.17";/**
* @vue/runtime-dom v3.5.17
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Rs;const Sr=typeof window<"u"&&window.trustedTypes;if(Sr)try{Rs=Sr.createPolicy("vue",{createHTML:e=>e})}catch{}const ai=Rs?e=>Rs.createHTML(e):e=>e,Cc="http://www.w3.org/2000/svg",Rc="http://www.w3.org/1998/Math/MathML",st=typeof document<"u"?document:null,br=st&&st.createElement("template"),Ac={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?st.createElementNS(Cc,e):t==="mathml"?st.createElementNS(Rc,e):n?st.createElement(e,{is:n}):st.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>st.createTextNode(e),createComment:e=>st.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>st.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{br.innerHTML=ai(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=br.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Tc=Symbol("_vtc");function xc(e,t,n){const s=e[Tc];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Er=Symbol("_vod"),Oc=Symbol("_vsh"),Ic=Symbol(""),Pc=/(^|;)\s*display\s*:/;function Mc(e,t,n){const s=e.style,r=_e(n);let o=!1;if(n&&!r){if(t)if(_e(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&On(s,l,"")}else for(const i in t)n[i]==null&&On(s,i,"");for(const i in n)i==="display"&&(o=!0),On(s,i,n[i])}else if(r){if(t!==n){const i=s[Ic];i&&(n+=";"+i),s.cssText=n,o=Pc.test(n)}}else t&&e.removeAttribute("style");Er in e&&(e[Er]=o?s.display:"",e[Oc]&&(s.display="none"))}const wr=/\s*!important$/;function On(e,t,n){if(Y(n))n.forEach(s=>On(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Dc(e,t);wr.test(n)?e.setProperty(xt(s),n.replace(wr,""),"important"):e[s]=n}}const Cr=["Webkit","Moz","ms"],ls={};function Dc(e,t){const n=ls[t];if(n)return n;let s=Ve(t);if(s!=="filter"&&s in e)return ls[t]=s;s=Vn(s);for(let r=0;r<Cr.length;r++){const o=Cr[r]+s;if(o in e)return ls[t]=o}return t}const Rr="http://www.w3.org/1999/xlink";function Ar(e,t,n,s,r,o=Hi(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Rr,t.slice(6,t.length)):e.setAttributeNS(Rr,t,n):n==null||o&&!ro(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Be(n)?String(n):n)}function Tr(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?ai(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=ro(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function it(e,t,n,s){e.addEventListener(t,n,s)}function Nc(e,t,n,s){e.removeEventListener(t,n,s)}const xr=Symbol("_vei");function Lc(e,t,n,s,r=null){const o=e[xr]||(e[xr]={}),i=o[t];if(s&&i)i.value=s;else{const[l,c]=Fc(t);if(s){const f=o[t]=kc(s,r);it(e,l,f,c)}else i&&(Nc(e,l,i,c),o[t]=void 0)}}const Or=/(?:Once|Passive|Capture)$/;function Fc(e){let t;if(Or.test(e)){t={};let s;for(;s=e.match(Or);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):xt(e.slice(2)),t]}let cs=0;const $c=Promise.resolve(),Hc=()=>cs||($c.then(()=>cs=0),cs=Date.now());function kc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;et(Vc(s,n.value),t,5,[s])};return n.value=e,n.attached=Hc(),n}function Vc(e,t){if(Y(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const Ir=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,jc=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?xc(e,s,i):t==="style"?Mc(e,n,s):Hn(t)?Ms(t)||Lc(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Bc(e,t,s,i))?(Tr(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Ar(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!_e(s))?Tr(e,Ve(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Ar(e,t,s,i))};function Bc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ir(t)&&ee(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Ir(t)&&_e(n)?!1:t in e}const _t=e=>{const t=e.props["onUpdate:modelValue"]||!1;return Y(t)?n=>Rn(t,n):t};function Uc(e){e.target.composing=!0}function Pr(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const ke=Symbol("_assign"),Wu={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[ke]=_t(r);const o=s||r.props&&r.props.type==="number";it(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=In(l)),e[ke](l)}),n&&it(e,"change",()=>{e.value=e.value.trim()}),t||(it(e,"compositionstart",Uc),it(e,"compositionend",Pr),it(e,"change",Pr))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[ke]=_t(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?In(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},Ku={deep:!0,created(e,t,n){e[ke]=_t(n),it(e,"change",()=>{const s=e._modelValue,r=jt(e),o=e.checked,i=e[ke];if(Y(s)){const l=$s(s,r),c=l!==-1;if(o&&!c)i(s.concat(r));else if(!o&&c){const f=[...s];f.splice(l,1),i(f)}}else if(Wt(s)){const l=new Set(s);o?l.add(r):l.delete(r),i(l)}else i(ui(e,o))})},mounted:Mr,beforeUpdate(e,t,n){e[ke]=_t(n),Mr(e,t,n)}};function Mr(e,{value:t,oldValue:n},s){e._modelValue=t;let r;if(Y(t))r=$s(t,s.props.value)>-1;else if(Wt(t))r=t.has(s.props.value);else{if(t===n)return;r=Tt(t,ui(e,!0))}e.checked!==r&&(e.checked=r)}const Gu={created(e,{value:t},n){e.checked=Tt(t,n.props.value),e[ke]=_t(n),it(e,"change",()=>{e[ke](jt(e))})},beforeUpdate(e,{value:t,oldValue:n},s){e[ke]=_t(s),t!==n&&(e.checked=Tt(t,s.props.value))}},Yu={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=Wt(t);it(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?In(jt(i)):jt(i));e[ke](e.multiple?r?new Set(o):o:o[0]),e._assigning=!0,Wn(()=>{e._assigning=!1})}),e[ke]=_t(s)},mounted(e,{value:t}){Dr(e,t)},beforeUpdate(e,t,n){e[ke]=_t(n)},updated(e,{value:t}){e._assigning||Dr(e,t)}};function Dr(e,t){const n=e.multiple,s=Y(t);if(!(n&&!s&&!Wt(t))){for(let r=0,o=e.options.length;r<o;r++){const i=e.options[r],l=jt(i);if(n)if(s){const c=typeof l;c==="string"||c==="number"?i.selected=t.some(f=>String(f)===String(l)):i.selected=$s(t,l)>-1}else i.selected=t.has(l);else if(Tt(jt(i),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function jt(e){return"_value"in e?e._value:e.value}function ui(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Wc=["ctrl","shift","alt","meta"],Kc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Wc.some(n=>e[`${n}Key`]&&!t.includes(n))},qu=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...o)=>{for(let i=0;i<t.length;i++){const l=Kc[t[i]];if(l&&l(r,t))return}return e(r,...o)})},Gc=Ae({patchProp:jc},Ac);let Nr;function Yc(){return Nr||(Nr=zl(Gc))}const qc=(...e)=>{const t=Yc().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=zc(s);if(!r)return;const o=t._component;!ee(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,Jc(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function Jc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function zc(e){return _e(e)?document.querySelector(e):e}const Qc={id:"app"},Zc={class:"app-nav"},Xc={class:"app-nav__content"},ea={class:"app-nav__links"},ta={class:"app-main"},na=Kn({__name:"App",setup(e){return(t,n)=>{const s=ys("router-link"),r=ys("router-view");return fn(),ri("div",Qc,[pe("nav",Zc,[pe("div",Xc,[me(s,{to:"/",class:"app-nav__logo"},{default:wt(()=>n[0]||(n[0]=[Ct(" 🎲 AI Story Builder ")])),_:1,__:[0]}),pe("div",ea,[me(s,{to:"/",class:"app-nav__link"},{default:wt(()=>n[1]||(n[1]=[Ct("Home")])),_:1,__:[1]}),me(s,{to:"/generator",class:"app-nav__link"},{default:wt(()=>n[2]||(n[2]=[Ct("Generator")])),_:1,__:[2]}),me(s,{to:"/library",class:"app-nav__link"},{default:wt(()=>n[3]||(n[3]=[Ct("Library")])),_:1,__:[3]})])])]),pe("main",ta,[me(r)])])}}}),sa="modulepreload",ra=function(e){return"/"+e},Lr={},as=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){let c=function(f){return Promise.all(f.map(a=>Promise.resolve(a).then(h=>({status:"fulfilled",value:h}),h=>({status:"rejected",reason:h}))))};document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),l=i?.nonce||i?.getAttribute("nonce");r=c(n.map(f=>{if(f=ra(f),f in Lr)return;Lr[f]=!0;const a=f.endsWith(".css"),h=a?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${f}"]${h}`))return;const p=document.createElement("link");if(p.rel=a?"stylesheet":sa,a||(p.as="script"),p.crossOrigin="",p.href=f,l&&p.setAttribute("nonce",l),document.head.appendChild(p),a)return new Promise((m,T)=>{p.addEventListener("load",m),p.addEventListener("error",()=>T(new Error(`Unable to preload CSS for ${f}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return r.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})};/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Nt=typeof document<"u";function fi(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function oa(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&fi(e.default)}const oe=Object.assign;function us(e,t){const n={};for(const s in t){const r=t[s];n[s]=Ue(r)?r.map(e):e(r)}return n}const rn=()=>{},Ue=Array.isArray,di=/#/g,ia=/&/g,la=/\//g,ca=/=/g,aa=/\?/g,hi=/\+/g,ua=/%5B/g,fa=/%5D/g,pi=/%5E/g,da=/%60/g,gi=/%7B/g,ha=/%7C/g,mi=/%7D/g,pa=/%20/g;function Qs(e){return encodeURI(""+e).replace(ha,"|").replace(ua,"[").replace(fa,"]")}function ga(e){return Qs(e).replace(gi,"{").replace(mi,"}").replace(pi,"^")}function As(e){return Qs(e).replace(hi,"%2B").replace(pa,"+").replace(di,"%23").replace(ia,"%26").replace(da,"`").replace(gi,"{").replace(mi,"}").replace(pi,"^")}function ma(e){return As(e).replace(ca,"%3D")}function va(e){return Qs(e).replace(di,"%23").replace(aa,"%3F")}function ya(e){return e==null?"":va(e).replace(la,"%2F")}function gn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const _a=/\/$/,Sa=e=>e.replace(_a,"");function fs(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),o=t.slice(c+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=Ca(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:gn(i)}}function ba(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Fr(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Ea(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Bt(t.matched[s],n.matched[r])&&vi(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Bt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function vi(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!wa(e[n],t[n]))return!1;return!0}function wa(e,t){return Ue(e)?$r(e,t):Ue(t)?$r(t,e):e===t}function $r(e,t){return Ue(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Ca(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const ft={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var mn;(function(e){e.pop="pop",e.push="push"})(mn||(mn={}));var on;(function(e){e.back="back",e.forward="forward",e.unknown=""})(on||(on={}));function Ra(e){if(!e)if(Nt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Sa(e)}const Aa=/^[^#]+#/;function Ta(e,t){return e.replace(Aa,"#")+t}function xa(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const zn=()=>({left:window.scrollX,top:window.scrollY});function Oa(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=xa(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Hr(e,t){return(history.state?history.state.position-t:-1)+e}const Ts=new Map;function Ia(e,t){Ts.set(e,t)}function Pa(e){const t=Ts.get(e);return Ts.delete(e),t}let Ma=()=>location.protocol+"//"+location.host;function yi(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,c=r.slice(l);return c[0]!=="/"&&(c="/"+c),Fr(c,"")}return Fr(n,e)+s+r}function Da(e,t,n,s){let r=[],o=[],i=null;const l=({state:p})=>{const m=yi(e,location),T=n.value,I=t.value;let q=0;if(p){if(n.value=m,t.value=p,i&&i===T){i=null;return}q=I?p.position-I.position:0}else s(m);r.forEach(j=>{j(n.value,T,{delta:q,type:mn.pop,direction:q?q>0?on.forward:on.back:on.unknown})})};function c(){i=n.value}function f(p){r.push(p);const m=()=>{const T=r.indexOf(p);T>-1&&r.splice(T,1)};return o.push(m),m}function a(){const{history:p}=window;p.state&&p.replaceState(oe({},p.state,{scroll:zn()}),"")}function h(){for(const p of o)p();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:c,listen:f,destroy:h}}function kr(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?zn():null}}function Na(e){const{history:t,location:n}=window,s={value:yi(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,f,a){const h=e.indexOf("#"),p=h>-1?(n.host&&document.querySelector("base")?e:e.slice(h))+c:Ma()+e+c;try{t[a?"replaceState":"pushState"](f,"",p),r.value=f}catch(m){console.error(m),n[a?"replace":"assign"](p)}}function i(c,f){const a=oe({},t.state,kr(r.value.back,c,r.value.forward,!0),f,{position:r.value.position});o(c,a,!0),s.value=c}function l(c,f){const a=oe({},r.value,t.state,{forward:c,scroll:zn()});o(a.current,a,!0);const h=oe({},kr(s.value,c,null),{position:a.position+1},f);o(c,h,!1),s.value=c}return{location:s,state:r,push:l,replace:i}}function La(e){e=Ra(e);const t=Na(e),n=Da(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=oe({location:"",base:e,go:s,createHref:Ta.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Fa(e){return typeof e=="string"||e&&typeof e=="object"}function _i(e){return typeof e=="string"||typeof e=="symbol"}const Si=Symbol("");var Vr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Vr||(Vr={}));function Ut(e,t){return oe(new Error,{type:e,[Si]:!0},t)}function nt(e,t){return e instanceof Error&&Si in e&&(t==null||!!(e.type&t))}const jr="[^/]+?",$a={sensitive:!1,strict:!1,start:!0,end:!0},Ha=/[.+*?^${}()[\]/\\]/g;function ka(e,t){const n=oe({},$a,t),s=[];let r=n.start?"^":"";const o=[];for(const f of e){const a=f.length?[]:[90];n.strict&&!f.length&&(r+="/");for(let h=0;h<f.length;h++){const p=f[h];let m=40+(n.sensitive?.25:0);if(p.type===0)h||(r+="/"),r+=p.value.replace(Ha,"\\$&"),m+=40;else if(p.type===1){const{value:T,repeatable:I,optional:q,regexp:j}=p;o.push({name:T,repeatable:I,optional:q});const H=j||jr;if(H!==jr){m+=10;try{new RegExp(`(${H})`)}catch(F){throw new Error(`Invalid custom RegExp for param "${T}" (${H}): `+F.message)}}let V=I?`((?:${H})(?:/(?:${H}))*)`:`(${H})`;h||(V=q&&f.length<2?`(?:/${V})`:"/"+V),q&&(V+="?"),r+=V,m+=20,q&&(m+=-8),I&&(m+=-20),H===".*"&&(m+=-50)}a.push(m)}s.push(a)}if(n.strict&&n.end){const f=s.length-1;s[f][s[f].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(f){const a=f.match(i),h={};if(!a)return null;for(let p=1;p<a.length;p++){const m=a[p]||"",T=o[p-1];h[T.name]=m&&T.repeatable?m.split("/"):m}return h}function c(f){let a="",h=!1;for(const p of e){(!h||!a.endsWith("/"))&&(a+="/"),h=!1;for(const m of p)if(m.type===0)a+=m.value;else if(m.type===1){const{value:T,repeatable:I,optional:q}=m,j=T in f?f[T]:"";if(Ue(j)&&!I)throw new Error(`Provided param "${T}" is an array but it is not repeatable (* or + modifiers)`);const H=Ue(j)?j.join("/"):j;if(!H)if(q)p.length<2&&(a.endsWith("/")?a=a.slice(0,-1):h=!0);else throw new Error(`Missing required param "${T}"`);a+=H}}return a||"/"}return{re:i,score:s,keys:o,parse:l,stringify:c}}function Va(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function bi(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Va(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(Br(s))return 1;if(Br(r))return-1}return r.length-s.length}function Br(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ja={type:0,value:""},Ba=/[a-zA-Z0-9_]/;function Ua(e){if(!e)return[[]];if(e==="/")return[[ja]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${f}": ${m}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,c,f="",a="";function h(){f&&(n===0?o.push({type:0,value:f}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${f}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:f,regexp:a,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),f="")}function p(){f+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(f&&h(),i()):c===":"?(h(),n=1):p();break;case 4:p(),n=s;break;case 1:c==="("?n=2:Ba.test(c)?p():(h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+c:n=3:a+=c;break;case 3:h(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,a="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${f}"`),h(),i(),r}function Wa(e,t,n){const s=ka(Ua(e.path),n),r=oe(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Ka(e,t){const n=[],s=new Map;t=Gr({strict:!1,end:!0,sensitive:!1},t);function r(h){return s.get(h)}function o(h,p,m){const T=!m,I=Wr(h);I.aliasOf=m&&m.record;const q=Gr(t,h),j=[I];if("alias"in h){const F=typeof h.alias=="string"?[h.alias]:h.alias;for(const Z of F)j.push(Wr(oe({},I,{components:m?m.record.components:I.components,path:Z,aliasOf:m?m.record:I})))}let H,V;for(const F of j){const{path:Z}=F;if(p&&Z[0]!=="/"){const ue=p.record.path,N=ue[ue.length-1]==="/"?"":"/";F.path=p.record.path+(Z&&N+Z)}if(H=Wa(F,p,q),m?m.alias.push(H):(V=V||H,V!==H&&V.alias.push(H),T&&h.name&&!Kr(H)&&i(h.name)),Ei(H)&&c(H),I.children){const ue=I.children;for(let N=0;N<ue.length;N++)o(ue[N],H,m&&m.children[N])}m=m||H}return V?()=>{i(V)}:rn}function i(h){if(_i(h)){const p=s.get(h);p&&(s.delete(h),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(h);p>-1&&(n.splice(p,1),h.record.name&&s.delete(h.record.name),h.children.forEach(i),h.alias.forEach(i))}}function l(){return n}function c(h){const p=qa(h,n);n.splice(p,0,h),h.record.name&&!Kr(h)&&s.set(h.record.name,h)}function f(h,p){let m,T={},I,q;if("name"in h&&h.name){if(m=s.get(h.name),!m)throw Ut(1,{location:h});q=m.record.name,T=oe(Ur(p.params,m.keys.filter(V=>!V.optional).concat(m.parent?m.parent.keys.filter(V=>V.optional):[]).map(V=>V.name)),h.params&&Ur(h.params,m.keys.map(V=>V.name))),I=m.stringify(T)}else if(h.path!=null)I=h.path,m=n.find(V=>V.re.test(I)),m&&(T=m.parse(I),q=m.record.name);else{if(m=p.name?s.get(p.name):n.find(V=>V.re.test(p.path)),!m)throw Ut(1,{location:h,currentLocation:p});q=m.record.name,T=oe({},p.params,h.params),I=m.stringify(T)}const j=[];let H=m;for(;H;)j.unshift(H.record),H=H.parent;return{name:q,path:I,params:T,matched:j,meta:Ya(j)}}e.forEach(h=>o(h));function a(){n.length=0,s.clear()}return{addRoute:o,resolve:f,removeRoute:i,clearRoutes:a,getRoutes:l,getRecordMatcher:r}}function Ur(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function Wr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Ga(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Ga(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function Kr(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Ya(e){return e.reduce((t,n)=>oe(t,n.meta),{})}function Gr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function qa(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;bi(e,t[o])<0?s=o:n=o+1}const r=Ja(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function Ja(e){let t=e;for(;t=t.parent;)if(Ei(t)&&bi(e,t)===0)return t}function Ei({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function za(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(hi," "),i=o.indexOf("="),l=gn(i<0?o:o.slice(0,i)),c=i<0?null:gn(o.slice(i+1));if(l in t){let f=t[l];Ue(f)||(f=t[l]=[f]),f.push(c)}else t[l]=c}return t}function Yr(e){let t="";for(let n in e){const s=e[n];if(n=ma(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Ue(s)?s.map(o=>o&&As(o)):[s&&As(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Qa(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Ue(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const Za=Symbol(""),qr=Symbol(""),Qn=Symbol(""),Zs=Symbol(""),xs=Symbol("");function qt(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function gt(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,c)=>{const f=p=>{p===!1?c(Ut(4,{from:n,to:t})):p instanceof Error?c(p):Fa(p)?c(Ut(2,{from:t,to:p})):(i&&s.enterCallbacks[r]===i&&typeof p=="function"&&i.push(p),l())},a=o(()=>e.call(s&&s.instances[r],t,n,f));let h=Promise.resolve(a);e.length<3&&(h=h.then(f)),h.catch(p=>c(p))})}function ds(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let c=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(fi(c)){const a=(c.__vccOpts||c)[t];a&&o.push(gt(a,n,s,i,l,r))}else{let f=c();o.push(()=>f.then(a=>{if(!a)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const h=oa(a)?a.default:a;i.mods[l]=a,i.components[l]=h;const m=(h.__vccOpts||h)[t];return m&&gt(m,n,s,i,l,r)()}))}}return o}function Jr(e){const t=He(Qn),n=He(Zs),s=te(()=>{const c=$t(e.to);return t.resolve(c)}),r=te(()=>{const{matched:c}=s.value,{length:f}=c,a=c[f-1],h=n.matched;if(!a||!h.length)return-1;const p=h.findIndex(Bt.bind(null,a));if(p>-1)return p;const m=zr(c[f-2]);return f>1&&zr(a)===m&&h[h.length-1].path!==m?h.findIndex(Bt.bind(null,c[f-2])):p}),o=te(()=>r.value>-1&&su(n.params,s.value.params)),i=te(()=>r.value>-1&&r.value===n.matched.length-1&&vi(n.params,s.value.params));function l(c={}){if(nu(c)){const f=t[$t(e.replace)?"replace":"push"]($t(e.to)).catch(rn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>f),f}return Promise.resolve()}return{route:s,href:te(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function Xa(e){return e.length===1?e[0]:e}const eu=Kn({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Jr,setup(e,{slots:t}){const n=yn(Jr(e)),{options:s}=He(Qn),r=te(()=>({[Qr(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Qr(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&Xa(t.default(n));return e.custom?o:ci("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),tu=eu;function nu(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function su(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!Ue(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function zr(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Qr=(e,t,n)=>e??t??n,ru=Kn({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=He(xs),r=te(()=>e.route||s.value),o=He(qr,0),i=te(()=>{let f=$t(o);const{matched:a}=r.value;let h;for(;(h=a[f])&&!h.components;)f++;return f}),l=te(()=>r.value.matched[i.value]);An(qr,te(()=>i.value+1)),An(Za,l),An(xs,r);const c=Se();return nn(()=>[c.value,l.value,e.name],([f,a,h],[p,m,T])=>{a&&(a.instances[h]=f,m&&m!==a&&f&&f===p&&(a.leaveGuards.size||(a.leaveGuards=m.leaveGuards),a.updateGuards.size||(a.updateGuards=m.updateGuards))),f&&a&&(!m||!Bt(a,m)||!p)&&(a.enterCallbacks[h]||[]).forEach(I=>I(f))},{flush:"post"}),()=>{const f=r.value,a=e.name,h=l.value,p=h&&h.components[a];if(!p)return Zr(n.default,{Component:p,route:f});const m=h.props[a],T=m?m===!0?f.params:typeof m=="function"?m(f):m:null,q=ci(p,oe({},T,t,{onVnodeUnmounted:j=>{j.component.isUnmounted&&(h.instances[a]=null)},ref:c}));return Zr(n.default,{Component:q,route:f})||q}}});function Zr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const ou=ru;function iu(e){const t=Ka(e.routes,e),n=e.parseQuery||za,s=e.stringifyQuery||Yr,r=e.history,o=qt(),i=qt(),l=qt(),c=il(ft);let f=ft;Nt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=us.bind(null,_=>""+_),h=us.bind(null,ya),p=us.bind(null,gn);function m(_,$){let M,U;return _i(_)?(M=t.getRecordMatcher(_),U=$):U=_,t.addRoute(U,M)}function T(_){const $=t.getRecordMatcher(_);$&&t.removeRoute($)}function I(){return t.getRoutes().map(_=>_.record)}function q(_){return!!t.getRecordMatcher(_)}function j(_,$){if($=oe({},$||c.value),typeof _=="string"){const g=fs(n,_,$.path),y=t.resolve({path:g.path},$),w=r.createHref(g.fullPath);return oe(g,y,{params:p(y.params),hash:gn(g.hash),redirectedFrom:void 0,href:w})}let M;if(_.path!=null)M=oe({},_,{path:fs(n,_.path,$.path).path});else{const g=oe({},_.params);for(const y in g)g[y]==null&&delete g[y];M=oe({},_,{params:h(g)}),$.params=h($.params)}const U=t.resolve(M,$),ce=_.hash||"";U.params=a(p(U.params));const u=ba(s,oe({},_,{hash:ga(ce),path:U.path})),d=r.createHref(u);return oe({fullPath:u,hash:ce,query:s===Yr?Qa(_.query):_.query||{}},U,{redirectedFrom:void 0,href:d})}function H(_){return typeof _=="string"?fs(n,_,c.value.path):oe({},_)}function V(_,$){if(f!==_)return Ut(8,{from:$,to:_})}function F(_){return N(_)}function Z(_){return F(oe(H(_),{replace:!0}))}function ue(_){const $=_.matched[_.matched.length-1];if($&&$.redirect){const{redirect:M}=$;let U=typeof M=="function"?M(_):M;return typeof U=="string"&&(U=U.includes("?")||U.includes("#")?U=H(U):{path:U},U.params={}),oe({query:_.query,hash:_.hash,params:U.path!=null?{}:_.params},U)}}function N(_,$){const M=f=j(_),U=c.value,ce=_.state,u=_.force,d=_.replace===!0,g=ue(M);if(g)return N(oe(H(g),{state:typeof g=="object"?oe({},ce,g.state):ce,force:u,replace:d}),$||M);const y=M;y.redirectedFrom=$;let w;return!u&&Ea(s,U,M)&&(w=Ut(16,{to:y,from:U}),Ke(U,U,!0,!1)),(w?Promise.resolve(w):v(y,U)).catch(S=>nt(S)?nt(S,2)?S:We(S):J(S,y,U)).then(S=>{if(S){if(nt(S,2))return N(oe({replace:d},H(S.to),{state:typeof S.to=="object"?oe({},ce,S.to.state):ce,force:u}),$||y)}else S=z(y,U,!0,d,ce);return L(y,U,S),S})}function b(_,$){const M=V(_,$);return M?Promise.reject(M):Promise.resolve()}function E(_){const $=It.values().next().value;return $&&typeof $.runWithContext=="function"?$.runWithContext(_):_()}function v(_,$){let M;const[U,ce,u]=lu(_,$);M=ds(U.reverse(),"beforeRouteLeave",_,$);for(const g of U)g.leaveGuards.forEach(y=>{M.push(gt(y,_,$))});const d=b.bind(null,_,$);return M.push(d),Le(M).then(()=>{M=[];for(const g of o.list())M.push(gt(g,_,$));return M.push(d),Le(M)}).then(()=>{M=ds(ce,"beforeRouteUpdate",_,$);for(const g of ce)g.updateGuards.forEach(y=>{M.push(gt(y,_,$))});return M.push(d),Le(M)}).then(()=>{M=[];for(const g of u)if(g.beforeEnter)if(Ue(g.beforeEnter))for(const y of g.beforeEnter)M.push(gt(y,_,$));else M.push(gt(g.beforeEnter,_,$));return M.push(d),Le(M)}).then(()=>(_.matched.forEach(g=>g.enterCallbacks={}),M=ds(u,"beforeRouteEnter",_,$,E),M.push(d),Le(M))).then(()=>{M=[];for(const g of i.list())M.push(gt(g,_,$));return M.push(d),Le(M)}).catch(g=>nt(g,8)?g:Promise.reject(g))}function L(_,$,M){l.list().forEach(U=>E(()=>U(_,$,M)))}function z(_,$,M,U,ce){const u=V(_,$);if(u)return u;const d=$===ft,g=Nt?history.state:{};M&&(U||d?r.replace(_.fullPath,oe({scroll:d&&g&&g.scroll},ce)):r.push(_.fullPath,ce)),c.value=_,Ke(_,$,M,d),We()}let A;function R(){A||(A=r.listen((_,$,M)=>{if(!bn.listening)return;const U=j(_),ce=ue(U);if(ce){N(oe(ce,{replace:!0,force:!0}),U).catch(rn);return}f=U;const u=c.value;Nt&&Ia(Hr(u.fullPath,M.delta),zn()),v(U,u).catch(d=>nt(d,12)?d:nt(d,2)?(N(oe(H(d.to),{force:!0}),U).then(g=>{nt(g,20)&&!M.delta&&M.type===mn.pop&&r.go(-1,!1)}).catch(rn),Promise.reject()):(M.delta&&r.go(-M.delta,!1),J(d,U,u))).then(d=>{d=d||z(U,u,!1),d&&(M.delta&&!nt(d,8)?r.go(-M.delta,!1):M.type===mn.pop&&nt(d,20)&&r.go(-1,!1)),L(U,u,d)}).catch(rn)}))}let W=qt(),B=qt(),k;function J(_,$,M){We(_);const U=B.list();return U.length?U.forEach(ce=>ce(_,$,M)):console.error(_),Promise.reject(_)}function ge(){return k&&c.value!==ft?Promise.resolve():new Promise((_,$)=>{W.add([_,$])})}function We(_){return k||(k=!_,R(),W.list().forEach(([$,M])=>_?M(_):$()),W.reset()),_}function Ke(_,$,M,U){const{scrollBehavior:ce}=e;if(!Nt||!ce)return Promise.resolve();const u=!M&&Pa(Hr(_.fullPath,0))||(U||!M)&&history.state&&history.state.scroll||null;return Wn().then(()=>ce(_,$,u)).then(d=>d&&Oa(d)).catch(d=>J(d,_,$))}const Oe=_=>r.go(_);let Ot;const It=new Set,bn={currentRoute:c,listening:!0,addRoute:m,removeRoute:T,clearRoutes:t.clearRoutes,hasRoute:q,getRoutes:I,resolve:j,options:e,push:F,replace:Z,go:Oe,back:()=>Oe(-1),forward:()=>Oe(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:B.add,isReady:ge,install(_){const $=this;_.component("RouterLink",tu),_.component("RouterView",ou),_.config.globalProperties.$router=$,Object.defineProperty(_.config.globalProperties,"$route",{enumerable:!0,get:()=>$t(c)}),Nt&&!Ot&&c.value===ft&&(Ot=!0,F(r.location).catch(ce=>{}));const M={};for(const ce in ft)Object.defineProperty(M,ce,{get:()=>c.value[ce],enumerable:!0});_.provide(Qn,$),_.provide(Zs,Ro(M)),_.provide(xs,c);const U=_.unmount;It.add(_),_.unmount=function(){It.delete(_),It.size<1&&(f=ft,A&&A(),A=null,c.value=ft,Ot=!1,k=!1),U()}}};function Le(_){return _.reduce(($,M)=>$.then(()=>E(M)),Promise.resolve())}return bn}function lu(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(f=>Bt(f,l))?s.push(l):n.push(l));const c=e.matched[i];c&&(t.matched.find(f=>Bt(f,c))||r.push(c))}return[n,s,r]}function Ju(){return He(Qn)}function zu(e){return He(Zs)}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let wi;const Zn=e=>wi=e,Ci=Symbol();function Os(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var ln;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(ln||(ln={}));function cu(){const e=co(!0),t=e.run(()=>Se({}));let n=[],s=[];const r=Ks({install(o){Zn(r),r._a=o,o.provide(Ci,r),o.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const Ri=()=>{};function Xr(e,t,n,s=Ri){e.push(t);const r=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),s())};return!n&&ao()&&Vi(r),r}function Mt(e,...t){e.slice().forEach(n=>{n(...t)})}const au=e=>e(),eo=Symbol(),hs=Symbol();function Is(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,s)=>e.set(s,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],r=e[n];Os(r)&&Os(s)&&e.hasOwnProperty(n)&&!ye(s)&&!vt(s)?e[n]=Is(r,s):e[n]=s}return e}const uu=Symbol();function fu(e){return!Os(e)||!Object.prototype.hasOwnProperty.call(e,uu)}const{assign:dt}=Object;function du(e){return!!(ye(e)&&e.effect)}function hu(e,t,n,s){const{state:r,actions:o,getters:i}=t,l=n.state.value[e];let c;function f(){l||(n.state.value[e]=r?r():{});const a=al(n.state.value[e]);return dt(a,o,Object.keys(i||{}).reduce((h,p)=>(h[p]=Ks(te(()=>{Zn(n);const m=n._s.get(e);return i[p].call(m,m)})),h),{}))}return c=Ai(e,f,t,n,s,!0),c}function Ai(e,t,n={},s,r,o){let i;const l=dt({actions:{}},n),c={deep:!0};let f,a,h=[],p=[],m;const T=s.state.value[e];!o&&!T&&(s.state.value[e]={}),Se({});let I;function q(b){let E;f=a=!1,typeof b=="function"?(b(s.state.value[e]),E={type:ln.patchFunction,storeId:e,events:m}):(Is(s.state.value[e],b),E={type:ln.patchObject,payload:b,storeId:e,events:m});const v=I=Symbol();Wn().then(()=>{I===v&&(f=!0)}),a=!0,Mt(h,E,s.state.value[e])}const j=o?function(){const{state:E}=n,v=E?E():{};this.$patch(L=>{dt(L,v)})}:Ri;function H(){i.stop(),h=[],p=[],s._s.delete(e)}const V=(b,E="")=>{if(eo in b)return b[hs]=E,b;const v=function(){Zn(s);const L=Array.from(arguments),z=[],A=[];function R(k){z.push(k)}function W(k){A.push(k)}Mt(p,{args:L,name:v[hs],store:Z,after:R,onError:W});let B;try{B=b.apply(this&&this.$id===e?this:Z,L)}catch(k){throw Mt(A,k),k}return B instanceof Promise?B.then(k=>(Mt(z,k),k)).catch(k=>(Mt(A,k),Promise.reject(k))):(Mt(z,B),B)};return v[eo]=!0,v[hs]=E,v},F={_p:s,$id:e,$onAction:Xr.bind(null,p),$patch:q,$reset:j,$subscribe(b,E={}){const v=Xr(h,b,E.detached,()=>L()),L=i.run(()=>nn(()=>s.state.value[e],z=>{(E.flush==="sync"?a:f)&&b({storeId:e,type:ln.direct,events:m},z)},dt({},c,E)));return v},$dispose:H},Z=yn(F);s._s.set(e,Z);const N=(s._a&&s._a.runWithContext||au)(()=>s._e.run(()=>(i=co()).run(()=>t({action:V}))));for(const b in N){const E=N[b];if(ye(E)&&!du(E)||vt(E))o||(T&&fu(E)&&(ye(E)?E.value=T[b]:Is(E,T[b])),s.state.value[e][b]=E);else if(typeof E=="function"){const v=V(E,b);N[b]=v,l.actions[b]=E}}return dt(Z,N),dt(se(Z),N),Object.defineProperty(Z,"$state",{get:()=>s.state.value[e],set:b=>{q(E=>{dt(E,b)})}}),s._p.forEach(b=>{dt(Z,i.run(()=>b({store:Z,app:s._a,pinia:s,options:l})))}),T&&o&&n.hydrate&&n.hydrate(Z.$state,T),f=!0,a=!0,Z}/*! #__NO_SIDE_EFFECTS__ */function Xn(e,t,n){let s;const r=typeof t=="function";s=r?n:t;function o(i,l){const c=Ul();return i=i||(c?He(Ci,null):null),i&&Zn(i),i=wi,i._s.has(e)||(r?Ai(e,t,s,i):hu(e,s,i)),i._s.get(e)}return o.$id=e,o}var re=(e=>(e.FANTASY="fantasy",e.SCIFI="sci-fi",e.MODERN="modern",e.HISTORICAL="historical",e.POST_APOCALYPTIC="post-apocalyptic",e.MYSTERY="mystery",e.ROMANCE="romance",e.HORROR="horror",e.COMEDY="comedy",e.ADVENTURE="adventure",e.THRILLER="thriller",e.WESTERN="western",e))(re||{}),ze=(e=>(e.BRAVE="brave",e.COWARDLY="cowardly",e.INTELLIGENT="intelligent",e.NAIVE="naive",e.KIND="kind",e.CRUEL="cruel",e.AMBITIOUS="ambitious",e.LAZY="lazy",e.HONEST="honest",e.DECEPTIVE="deceptive",e.LOYAL="loyal",e.TREACHEROUS="treacherous",e.OPTIMISTIC="optimistic",e.PESSIMISTIC="pessimistic",e.CREATIVE="creative",e.PRACTICAL="practical",e))(ze||{}),Q=(e=>(e.BETRAYAL="betrayal",e.FRIENDSHIP="friendship",e.LOVE="love",e.REVENGE="revenge",e.REDEMPTION="redemption",e.SACRIFICE="sacrifice",e.POWER="power",e.FREEDOM="freedom",e.JUSTICE="justice",e.SURVIVAL="survival",e.DISCOVERY="discovery",e.TRANSFORMATION="transformation",e.TIME_TRAVEL="time-travel",e.FAMILY="family",e.IDENTITY="identity",e.COMING_OF_AGE="coming-of-age",e))(Q||{}),Ti=(e=>(e.THREE_ACT="three-act",e.FIVE_ACT="five-act",e.HERO_JOURNEY="hero-journey",e.FREYTAG_PYRAMID="freytag-pyramid",e))(Ti||{}),ve=(e=>(e.URBAN="urban",e.RURAL="rural",e.WILDERNESS="wilderness",e.UNDERGROUND="underground",e.SPACE="space",e.UNDERWATER="underwater",e.MAGICAL_REALM="magical-realm",e.DYSTOPIAN_CITY="dystopian-city",e.SMALL_TOWN="small-town",e.CASTLE="castle",e.SPACESHIP="spaceship",e.DESERT="desert",e.FOREST="forest",e.MOUNTAIN="mountain",e.ISLAND="island",e))(ve||{});const pu=[{id:"char-1",name:"Aria Shadowbane",age:24,description:"A skilled rogue with a mysterious past",personalityTraits:[ze.BRAVE,ze.DECEPTIVE,ze.LOYAL],goals:["Find her lost brother","Uncover the truth about her heritage"],flaws:["Trusts too easily","Haunted by past mistakes"],backstory:"Raised in the streets after her family disappeared",appearance:"Dark hair, green eyes, always wears a hooded cloak",role:"protagonist",isTemplate:!0,createdAt:new Date,updatedAt:new Date},{id:"char-2",name:"Lord Malachar",age:45,description:"A powerful sorcerer seeking immortality",personalityTraits:[ze.AMBITIOUS,ze.INTELLIGENT,ze.CRUEL],goals:["Achieve immortality","Rule the kingdom"],flaws:["Arrogant","Underestimates others"],backstory:"Former court wizard who was banished for dark magic",appearance:"Tall, pale, with silver hair and piercing blue eyes",role:"antagonist",isTemplate:!0,createdAt:new Date,updatedAt:new Date},{id:"char-3",name:"Captain Rex Sterling",age:35,description:"A space marine with a strong moral compass",personalityTraits:[ze.BRAVE,ze.HONEST,ze.LOYAL],goals:["Protect the innocent","Uncover the conspiracy"],flaws:["Too rigid in thinking","Struggles with authority"],backstory:"Veteran of the Galactic Wars, now fights corruption",appearance:"Muscular build, scarred face, always in military gear",role:"protagonist",isTemplate:!0,createdAt:new Date,updatedAt:new Date}],gu=[{id:"setting-1",name:"The Whispering Woods",genre:re.FANTASY,type:ve.FOREST,description:"An ancient forest where the trees seem to whisper secrets",atmosphere:"Mysterious and enchanting, with dappled sunlight filtering through ancient oaks",keyLocations:["The Elder Tree","Moonlit Clearing","Hidden Grove"],timeOfDay:"evening",weather:"Misty",season:"autumn",isTemplate:!0,createdAt:new Date,updatedAt:new Date},{id:"setting-2",name:"Neo-Tokyo Megacity",genre:re.SCIFI,type:ve.URBAN,description:"A sprawling cyberpunk metropolis with towering skyscrapers",atmosphere:"Neon-lit, bustling, and technologically advanced",keyLocations:["Corporate District","Underground Markets","Skyway Networks"],timeOfDay:"night",weather:"Rain",season:"winter",isTemplate:!0,createdAt:new Date,updatedAt:new Date},{id:"setting-3",name:"The Crimson Desert",genre:re.POST_APOCALYPTIC,type:ve.DESERT,description:"A vast wasteland of red sand and ruined cities",atmosphere:"Harsh, unforgiving, with remnants of lost civilization",keyLocations:["The Glass City Ruins","Oasis of Hope","The Bone Bridge"],timeOfDay:"noon",weather:"Sandstorm",season:"summer",isTemplate:!0,createdAt:new Date,updatedAt:new Date}],mu=[{id:"theme-1",primary:Q.REDEMPTION,secondary:[Q.FRIENDSHIP,Q.SACRIFICE],description:"A story about finding redemption through friendship and sacrifice",conflictType:"both",mood:"mixed",isTemplate:!0,createdAt:new Date,updatedAt:new Date},{id:"theme-2",primary:Q.SURVIVAL,secondary:[Q.DISCOVERY,Q.TRANSFORMATION],description:"A tale of survival that leads to self-discovery and transformation",conflictType:"external",mood:"dark",isTemplate:!0,createdAt:new Date,updatedAt:new Date},{id:"theme-3",primary:Q.LOVE,secondary:[Q.BETRAYAL,Q.JUSTICE],description:"A love story complicated by betrayal and the pursuit of justice",conflictType:"internal",mood:"mixed",isTemplate:!0,createdAt:new Date,updatedAt:new Date}];re.FANTASY+"",re.SCIFI+"",re.MODERN+"",re.HISTORICAL+"",re.POST_APOCALYPTIC+"",re.MYSTERY+"",re.ROMANCE+"",re.HORROR+"",re.COMEDY+"",re.ADVENTURE+"",re.THRILLER+"",re.WESTERN+"";const vu=[{primary:Q.REDEMPTION,secondary:[Q.SACRIFICE,Q.FRIENDSHIP],description:"A powerful combination exploring how sacrifice and friendship can lead to redemption"},{primary:Q.POWER,secondary:[Q.BETRAYAL,Q.JUSTICE],description:"Classic themes of corruption, betrayal, and the fight for justice"},{primary:Q.DISCOVERY,secondary:[Q.TRANSFORMATION,Q.IDENTITY],description:"A journey of self-discovery that transforms the character's identity"},{primary:Q.SURVIVAL,secondary:[Q.FAMILY,Q.SACRIFICE],description:"Survival story where family bonds drive characters to sacrifice for each other"}],yu=Xn("character",()=>{const e=Se([...pu]),t=Se([]),n=Se(!1),s=Se(null),r=te(()=>e.value.filter(N=>N.role==="protagonist")),o=te(()=>e.value.filter(N=>N.role==="antagonist")),i=te(()=>e.value.filter(N=>N.role==="supporting")),l=te(()=>e.value.filter(N=>N.isTemplate)),c=te(()=>e.value.length);function f(N){const b={...N,id:`char-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,createdAt:new Date,updatedAt:new Date};return e.value.push(b),b}function a(N,b){const E=e.value.findIndex(v=>v.id===N);E!==-1&&(e.value[E]={...e.value[E],...b,updatedAt:new Date})}function h(N){const b=e.value.findIndex(E=>E.id===N);if(b!==-1){e.value.splice(b,1);const E=t.value.findIndex(v=>v.id===N);E!==-1&&t.value.splice(E,1)}}function p(N){t.value.find(b=>b.id===N.id)||t.value.push(N)}function m(N){const b=t.value.findIndex(E=>E.id===N);b!==-1&&t.value.splice(b,1)}function T(){t.value=[]}function I(N){const b=e.value.find(E=>E.id===N);if(b){const E={...b,name:`${b.name} (Copy)`,isTemplate:!1};return f(E)}return null}function q(N){const b=["Aiden","Luna","Kai","Zara","Orion","Nova","Sage","Phoenix","River","Storm","Ember","Atlas","Iris","Jasper","Lyra","Dante"],E=["Find their true purpose","Protect their loved ones","Uncover a hidden truth","Master their abilities","Seek revenge","Find redemption","Discover their heritage","Save their world"],v=["Too trusting","Quick to anger","Haunted by the past","Overly cautious","Struggles with self-doubt","Too proud","Fears commitment","Impulsive decisions"],L=b[Math.floor(Math.random()*b.length)],z=E.sort(()=>.5-Math.random()).slice(0,2),A=v.sort(()=>.5-Math.random()).slice(0,2);return f({name:L,age:Math.floor(Math.random()*50)+18,description:"A mysterious character with an unknown past",personalityTraits:N||[],goals:z,flaws:A,role:"supporting",isTemplate:!1})}function j(N){return e.value.find(b=>b.id===N)}function H(N){const b=N.toLowerCase();return e.value.filter(E=>E.name.toLowerCase().includes(b)||E.description.toLowerCase().includes(b)||E.personalityTraits.some(v=>v.toLowerCase().includes(b)))}function V(N){s.value=N}function F(){n.value=!n.value}function Z(){localStorage.setItem("storyBuilder_characters",JSON.stringify(e.value)),localStorage.setItem("storyBuilder_selectedCharacters",JSON.stringify(t.value))}function ue(){const N=localStorage.getItem("storyBuilder_characters"),b=localStorage.getItem("storyBuilder_selectedCharacters");if(N)try{e.value=JSON.parse(N)}catch(E){console.error("Failed to load characters from storage:",E)}if(b)try{t.value=JSON.parse(b)}catch(E){console.error("Failed to load selected characters from storage:",E)}}return{characters:e,selectedCharacters:t,isCreatingCharacter:n,editingCharacter:s,protagonists:r,antagonists:o,supportingCharacters:i,templates:l,characterCount:c,addCharacter:f,updateCharacter:a,deleteCharacter:h,selectCharacter:p,deselectCharacter:m,clearSelectedCharacters:T,duplicateCharacter:I,generateRandomCharacter:q,getCharacterById:j,searchCharacters:H,setEditingCharacter:V,toggleCreatingCharacter:F,saveToStorage:Z,loadFromStorage:ue}}),_u=Xn("setting",()=>{const e=Se([...gu]),t=Se([]),n=Se(!1),s=Se(null),r=te(()=>{const b={};return e.value.forEach(E=>{b[E.genre]||(b[E.genre]=[]),b[E.genre].push(E)}),b}),o=te(()=>{const b={};return e.value.forEach(E=>{b[E.type]||(b[E.type]=[]),b[E.type].push(E)}),b}),i=te(()=>e.value.filter(b=>b.isTemplate)),l=te(()=>e.value.length);function c(b){const E={...b,id:`setting-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,createdAt:new Date,updatedAt:new Date};return e.value.push(E),E}function f(b,E){const v=e.value.findIndex(L=>L.id===b);v!==-1&&(e.value[v]={...e.value[v],...E,updatedAt:new Date})}function a(b){const E=e.value.findIndex(v=>v.id===b);if(E!==-1){e.value.splice(E,1);const v=t.value.findIndex(L=>L.id===b);v!==-1&&t.value.splice(v,1)}}function h(b){t.value.find(E=>E.id===b.id)||t.value.push(b)}function p(b){const E=t.value.findIndex(v=>v.id===b);E!==-1&&t.value.splice(E,1)}function m(){t.value=[]}function T(b){const E=e.value.find(v=>v.id===b);if(E){const v={...E,name:`${E.name} (Copy)`,isTemplate:!1};return c(v)}return null}function I(b,E){const v={[ve.URBAN]:["Neon City","Steel Harbor","Glass Metropolis","Chrome District"],[ve.FOREST]:["Whispering Woods","Shadowleaf Forest","Ancient Grove","Moonlit Thicket"],[ve.DESERT]:["Crimson Sands","Glass Desert","Bone Valley","Mirage Wastes"],[ve.MOUNTAIN]:["Frost Peaks","Dragon Spine","Cloud Reach","Stone Crown"],[ve.SPACE]:["Void Station","Nebula Outpost","Star Gate","Cosmic Harbor"],[ve.CASTLE]:["Iron Keep","Shadowmere Castle","Thornwall Fortress","Moonstone Palace"],[ve.ISLAND]:["Coral Haven","Storm Isle","Mist Island","Treasure Cove"],[ve.UNDERGROUND]:["Deep Tunnels","Crystal Caverns","Shadow Depths","Lost Catacombs"],[ve.UNDERWATER]:["Coral City","Abyssal Depths","Kelp Gardens","Sunken Palace"],[ve.MAGICAL_REALM]:["Ethereal Plane","Dreamscape","Fae Realm","Spirit World"],[ve.DYSTOPIAN_CITY]:["Sector 7","The Ruins","New Babylon","Iron District"],[ve.SMALL_TOWN]:["Millbrook","Cedar Falls","Willowdale","Harmony Springs"],[ve.SPACESHIP]:["Star Cruiser","Deep Space Vessel","Battle Frigate","Explorer Ship"],[ve.RURAL]:["Green Valley","Harvest Fields","Countryside","Meadowlands"],[ve.WILDERNESS]:["Wild Frontier","Untamed Lands","Savage Territory","Lost Wilderness"]},L=["Mysterious and foreboding","Bright and welcoming","Dark and oppressive","Peaceful and serene","Chaotic and dangerous","Ancient and mystical","Modern and sleek","Rustic and charming"],z=E||Object.values(ve)[Math.floor(Math.random()*Object.values(ve).length)],A=b||Object.values(re)[Math.floor(Math.random()*Object.values(re).length)],R=v[z]||["Mysterious Place"],W=R[Math.floor(Math.random()*R.length)],B=L[Math.floor(Math.random()*L.length)];return c({name:W,genre:A,type:z,description:`A ${z.replace("-"," ")} with a unique character`,atmosphere:B,keyLocations:["Main Area","Hidden Spot","Important Landmark"],isTemplate:!1})}function q(b){return e.value.find(E=>E.id===b)}function j(b){const E=b.toLowerCase();return e.value.filter(v=>v.name.toLowerCase().includes(E)||v.description.toLowerCase().includes(E)||v.atmosphere.toLowerCase().includes(E)||v.genre.toLowerCase().includes(E)||v.type.toLowerCase().includes(E))}function H(b){return e.value.filter(E=>E.genre===b)}function V(b){return e.value.filter(E=>E.type===b)}function F(b){s.value=b}function Z(){n.value=!n.value}function ue(){localStorage.setItem("storyBuilder_settings",JSON.stringify(e.value)),localStorage.setItem("storyBuilder_selectedSettings",JSON.stringify(t.value))}function N(){const b=localStorage.getItem("storyBuilder_settings"),E=localStorage.getItem("storyBuilder_selectedSettings");if(b)try{e.value=JSON.parse(b)}catch(v){console.error("Failed to load settings from storage:",v)}if(E)try{t.value=JSON.parse(E)}catch(v){console.error("Failed to load selected settings from storage:",v)}}return{settings:e,selectedSettings:t,isCreatingSetting:n,editingSetting:s,settingsByGenre:r,settingsByType:o,templates:i,settingCount:l,addSetting:c,updateSetting:f,deleteSetting:a,selectSetting:h,deselectSetting:p,clearSelectedSettings:m,duplicateSetting:T,generateRandomSetting:I,getSettingById:q,searchSettings:j,getSettingsByGenre:H,getSettingsByType:V,setEditingSetting:F,toggleCreatingSetting:Z,saveToStorage:ue,loadFromStorage:N}}),Su=Xn("theme",()=>{const e=Se([...mu]),t=Se([]),n=Se(!1),s=Se(null),r=te(()=>{const A={};return e.value.forEach(R=>{A[R.mood]||(A[R.mood]=[]),A[R.mood].push(R)}),A}),o=te(()=>{const A={};return e.value.forEach(R=>{A[R.conflictType]||(A[R.conflictType]=[]),A[R.conflictType].push(R)}),A}),i=te(()=>e.value.filter(A=>A.isTemplate)),l=te(()=>e.value.length),c=te(()=>vu);function f(A){const R={...A,id:`theme-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,createdAt:new Date,updatedAt:new Date};return e.value.push(R),R}function a(A,R){const W=e.value.findIndex(B=>B.id===A);W!==-1&&(e.value[W]={...e.value[W],...R,updatedAt:new Date})}function h(A){const R=e.value.findIndex(W=>W.id===A);if(R!==-1){e.value.splice(R,1);const W=t.value.findIndex(B=>B.id===A);W!==-1&&t.value.splice(W,1)}}function p(A){t.value.find(R=>R.id===A.id)||t.value.push(A)}function m(A){const R=t.value.findIndex(W=>W.id===A);R!==-1&&t.value.splice(R,1)}function T(){t.value=[]}function I(A){const R=e.value.find(W=>W.id===A);if(R){const W={...R,description:`${R.description} (Copy)`,isTemplate:!1};return f(W)}return null}function q(){const A=Object.values(Q),R=["light","dark","neutral","mixed"],W=["internal","external","both"],B=A[Math.floor(Math.random()*A.length)],k=A.filter(We=>We!==B).sort(()=>.5-Math.random()).slice(0,Math.floor(Math.random()*3)+1),J=R[Math.floor(Math.random()*R.length)],ge=W[Math.floor(Math.random()*W.length)];return f({primary:B,secondary:k,description:`A story exploring ${B} with elements of ${k.join(", ")}`,conflictType:ge,mood:J,isTemplate:!1})}function j(A){return f({primary:A.primary,secondary:A.secondary,description:A.description,conflictType:"both",mood:"mixed",isTemplate:!1})}function H(A){return e.value.find(R=>R.id===A)}function V(A){const R=A.toLowerCase();return e.value.filter(W=>W.description.toLowerCase().includes(R)||W.primary.toLowerCase().includes(R)||W.secondary.some(B=>B.toLowerCase().includes(R))||W.mood.toLowerCase().includes(R)||W.conflictType.toLowerCase().includes(R))}function F(A){return e.value.filter(R=>R.mood===A)}function Z(A){return e.value.filter(R=>R.conflictType===A)}function ue(A){return e.value.filter(R=>R.primary===A)}function N(A){return t.value.some(R=>R.primary===A||R.secondary.includes(A))}function b(A){const R={[Q.LOVE]:[Q.BETRAYAL],[Q.JUSTICE]:[Q.REVENGE],[Q.REDEMPTION]:[Q.REVENGE],[Q.FRIENDSHIP]:[Q.BETRAYAL],[Q.FREEDOM]:[Q.POWER]},W=[];return t.value.forEach(B=>{const k=R[B.primary]||[],J=B.secondary.flatMap(ge=>R[ge]||[]);(k.includes(A.primary)||J.includes(A.primary)||A.secondary.some(ge=>k.includes(ge)||J.includes(ge)))&&W.push(B)}),W}function E(A){s.value=A}function v(){n.value=!n.value}function L(){localStorage.setItem("storyBuilder_themes",JSON.stringify(e.value)),localStorage.setItem("storyBuilder_selectedThemes",JSON.stringify(t.value))}function z(){const A=localStorage.getItem("storyBuilder_themes"),R=localStorage.getItem("storyBuilder_selectedThemes");if(A)try{e.value=JSON.parse(A)}catch(W){console.error("Failed to load themes from storage:",W)}if(R)try{t.value=JSON.parse(R)}catch(W){console.error("Failed to load selected themes from storage:",W)}}return{themes:e,selectedThemes:t,isCreatingTheme:n,editingTheme:s,themesByMood:r,themesByConflictType:o,templates:i,themeCount:l,suggestions:c,addTheme:f,updateTheme:a,deleteTheme:h,selectTheme:p,deselectTheme:m,clearSelectedThemes:T,duplicateTheme:I,generateRandomTheme:q,createThemeFromSuggestion:j,getThemeById:H,searchThemes:V,getThemesByMood:F,getThemesByConflictType:Z,getThemesByPrimary:ue,hasTheme:N,getConflictingThemes:b,setEditingTheme:E,toggleCreatingTheme:v,saveToStorage:L,loadFromStorage:z}}),bu=Xn("story",()=>{const e=Se([]),t=Se(null),n=Se(null),s=Se({currentStep:"characters",selectedCharacters:[],selectedSettings:[],selectedThemes:[],generationOptions:{useAI:!1,creativity:.7,length:"medium",includeDialogue:!0,narrativePerspective:"third-limited",tense:"past"},isGenerating:!1,progress:0}),r=te(()=>e.value.length),o=te(()=>e.value.filter(v=>v.isComplete)),i=te(()=>e.value.filter(v=>!v.isComplete)),l=te(()=>s.value.selectedCharacters.length>0&&s.value.selectedSettings.length>0&&s.value.selectedThemes.length>0),c=te(()=>["characters","settings","themes","generation","editing","complete"].indexOf(s.value.currentStep));function f(v,L,z,A){const R=L[0]?.genre||re.FANTASY,W=z.map(k=>k.primary);return{id:`outline-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,title:h(W,R),genre:R,themes:W,structure:Ti.THREE_ACT,characters:[...v],settings:[...L],plotPoints:a(v,L,z),summary:p(v,L,z),wordCountTarget:m(A.length),estimatedReadingTime:T(A.length),createdAt:new Date,updatedAt:new Date}}function a(v,L,z,A){const R=v.find(ge=>ge.role==="protagonist")||v[0],W=v.find(ge=>ge.role==="antagonist"),B=L[0],k=z[0],J=[];return J.push({id:`plot-${Date.now()}-1`,title:"Opening Scene",description:`Introduce ${R.name} in ${B.name}. Establish the ordinary world and hint at the central conflict.`,act:1,order:1,type:"setup",characters:[R.id],setting:B.id}),J.push({id:`plot-${Date.now()}-2`,title:"Inciting Incident",description:`An event disrupts ${R.name}'s normal life, setting the story in motion. This relates to the theme of ${k.primary}.`,act:1,order:2,type:"inciting-incident",characters:[R.id],setting:B.id}),J.push({id:`plot-${Date.now()}-3`,title:"First Plot Point",description:`${R.name} commits to the journey or quest. No turning back.`,act:1,order:3,type:"plot-point",characters:[R.id],setting:B.id}),J.push({id:`plot-${Date.now()}-4`,title:"First Obstacle",description:`${R.name} faces their first major challenge. ${W?`${W.name} begins to emerge as a threat.`:"The central conflict intensifies."}`,act:2,order:4,type:"plot-point",characters:W?[R.id,W.id]:[R.id],setting:L[1]?.id||B.id}),J.push({id:`plot-${Date.now()}-5`,title:"Midpoint",description:`A major revelation or turning point. ${R.name} gains new understanding but faces greater stakes.`,act:2,order:5,type:"midpoint",characters:[R.id],setting:L[1]?.id||B.id}),J.push({id:`plot-${Date.now()}-6`,title:"Crisis",description:`${R.name} faces their darkest moment. All seems lost. This tests the theme of ${k.primary}.`,act:2,order:6,type:"crisis",characters:[R.id],setting:L[2]?.id||B.id}),J.push({id:`plot-${Date.now()}-7`,title:"Climax",description:`The final confrontation. ${R.name} uses everything they've learned to face the ultimate challenge.`,act:3,order:7,type:"climax",characters:v.map(ge=>ge.id),setting:L[L.length-1]?.id||B.id}),J.push({id:`plot-${Date.now()}-8`,title:"Resolution",description:`The aftermath. ${R.name} has changed, and the world reflects the theme of ${k.primary}.`,act:3,order:8,type:"resolution",characters:[R.id],setting:B.id}),J}function h(v,L){const z={[Q.REDEMPTION]:["Redemption","Second Chance","Forgiveness"],[Q.BETRAYAL]:["Betrayal","Broken Trust","False Friend"],[Q.LOVE]:["Love","Heart","Devotion"],[Q.REVENGE]:["Vengeance","Retribution","Payback"],[Q.SURVIVAL]:["Survival","Last Stand","Endurance"],[Q.DISCOVERY]:["Discovery","Revelation","Hidden Truth"],[Q.POWER]:["Power","Crown","Dominion"],[Q.FREEDOM]:["Freedom","Liberation","Escape"],[Q.SACRIFICE]:["Sacrifice","Price","Cost"],[Q.TRANSFORMATION]:["Transformation","Change","Metamorphosis"],[Q.FRIENDSHIP]:["Friendship","Bond","Alliance"],[Q.FAMILY]:["Family","Legacy","Heritage"],[Q.IDENTITY]:["Identity","Self","Truth"],[Q.JUSTICE]:["Justice","Balance","Judgment"],[Q.COMING_OF_AGE]:["Journey","Awakening","Growth"],[Q.TIME_TRAVEL]:["Time","Destiny","Paradox"]},A={[re.FANTASY]:["Realm","Kingdom","Magic","Dragon","Quest"],[re.SCIFI]:["Galaxy","Star","Future","Cyber","Nova"],[re.MYSTERY]:["Shadow","Secret","Clue","Mystery","Case"],[re.HORROR]:["Darkness","Fear","Nightmare","Terror","Haunted"],[re.ROMANCE]:["Heart","Love","Passion","Desire","Soul"],[re.ADVENTURE]:["Adventure","Quest","Journey","Expedition"],[re.THRILLER]:["Thriller","Chase","Hunt","Race"],[re.WESTERN]:["Frontier","Trail","Range","Territory"],[re.HISTORICAL]:["Era","Age","Time","Period"],[re.MODERN]:["City","Life","World","Reality"],[re.POST_APOCALYPTIC]:["Wasteland","Ruins","Ashes","Remnant"],[re.COMEDY]:["Comedy","Farce","Jest","Humor"]},R=v[0],W=z[R]?.[Math.floor(Math.random()*z[R].length)]||"Story",B=A[L]?.[Math.floor(Math.random()*(A[L]?.length||1))]||"Tale",k=[`The ${W} of ${B}`,`${W}'s ${B}`,`The ${B} ${W}`,`${B} of ${W}`];return k[Math.floor(Math.random()*k.length)]}function p(v,L,z){const A=v.find(J=>J.role==="protagonist")||v[0],R=v.find(J=>J.role==="antagonist"),W=L[0],B=z[0];let k=`In ${W.name}, ${A.name} must confront `;return R?k+=`${R.name} while dealing with themes of ${B.primary}`:k+=`challenges that test their understanding of ${B.primary}`,z.length>1&&(k+=` and ${z.slice(1).map(J=>J.primary).join(", ")}`),k+=`. Through their journey, they will discover what it truly means to ${A.goals[0]||"find their purpose"}.`,k}function m(v){switch(v){case"short":return 2e3;case"medium":return 5e3;case"long":return 1e4;default:return 5e3}}function T(v){const z=m(v);return Math.ceil(z/200)}async function I(){if(!l.value)return null;s.value.isGenerating=!0,s.value.progress=0;try{s.value.progress=25;const v=f(s.value.selectedCharacters,s.value.selectedSettings,s.value.selectedThemes,s.value.generationOptions);n.value=v,s.value.progress=50;const L=await q(v);s.value.progress=75;const z={id:`story-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,outline:v,content:L,isComplete:!1,chapters:[],metadata:{wordCount:L.split(" ").length,readingTime:Math.ceil(L.split(" ").length/200),lastEditedAt:new Date,version:1,tags:[],isPublic:!1},createdAt:new Date,updatedAt:new Date};return e.value.push(z),t.value=z,s.value.progress=100,s.value.currentStep="complete",z}catch(v){return console.error("Failed to generate story:",v),null}finally{s.value.isGenerating=!1}}async function q(v){let L=`# ${v.title}

`;return L+=`${v.summary}

`,v.plotPoints.forEach((z,A)=>{L+=`## Chapter ${A+1}: ${z.title}

`,L+=`${z.description}

`,L+=`[This is where the actual story content would be generated based on the plot point, characters, and settings.]

`}),L}function j(v){s.value.currentStep=v}function H(){const v=["characters","settings","themes","generation","editing","complete"],L=v.indexOf(s.value.currentStep);L<v.length-1&&(s.value.currentStep=v[L+1])}function V(){const v=["characters","settings","themes","generation","editing","complete"],L=v.indexOf(s.value.currentStep);L>0&&(s.value.currentStep=v[L-1])}function F(){s.value={currentStep:"characters",selectedCharacters:[],selectedSettings:[],selectedThemes:[],generationOptions:{useAI:!1,creativity:.7,length:"medium",includeDialogue:!0,narrativePerspective:"third-limited",tense:"past"},isGenerating:!1,progress:0},t.value=null,n.value=null}function Z(v){s.value.generationOptions={...s.value.generationOptions,...v}}function ue(v){const L=e.value.findIndex(z=>z.id===v);L!==-1&&(e.value.splice(L,1),t.value?.id===v&&(t.value=null))}function N(v){return e.value.find(L=>L.id===v)}function b(){localStorage.setItem("storyBuilder_stories",JSON.stringify(e.value)),localStorage.setItem("storyBuilder_generatorState",JSON.stringify(s.value))}function E(){const v=localStorage.getItem("storyBuilder_stories"),L=localStorage.getItem("storyBuilder_generatorState");if(v)try{e.value=JSON.parse(v)}catch(z){console.error("Failed to load stories from storage:",z)}if(L)try{s.value=JSON.parse(L)}catch(z){console.error("Failed to load generator state from storage:",z)}}return{stories:e,currentStory:t,currentOutline:n,generatorState:s,storyCount:r,completedStories:o,draftStories:i,canGenerateStory:l,currentStepIndex:c,generateStory:I,setCurrentStep:j,nextStep:H,previousStep:V,resetGenerator:F,updateGenerationOptions:Z,deleteStory:ue,getStoryById:N,saveToStorage:b,loadFromStorage:E}}),Eu={class:"home"},wu={class:"home__hero"},Cu={class:"home__hero-content"},Ru={class:"home__actions"},Au={class:"home__stats"},Tu={class:"home__stat"},xu={class:"home__stat-number"},Ou={class:"home__stat"},Iu={class:"home__stat-number"},Pu={class:"home__stat"},Mu={class:"home__stat-number"},Du={class:"home__stat"},Nu={class:"home__stat-number"},Lu=Kn({__name:"HomeView",setup(e){const t=yu(),n=_u(),s=Su(),r=bu(),o=te(()=>t.characterCount),i=te(()=>n.settingCount),l=te(()=>s.themeCount),c=te(()=>r.storyCount);return $o(()=>{t.loadFromStorage(),n.loadFromStorage(),s.loadFromStorage(),r.loadFromStorage()}),(f,a)=>{const h=ys("router-link");return fn(),ri("div",Eu,[pe("div",wu,[pe("div",Cu,[a[2]||(a[2]=pe("h1",{class:"home__title"},"AI Story Builder",-1)),a[3]||(a[3]=pe("p",{class:"home__subtitle"}," Create compelling stories with our interactive story generator. Choose characters, settings, and themes to craft unique narratives. ",-1)),pe("div",Ru,[me(h,{to:"/generator",class:"home__cta"},{default:wt(()=>a[0]||(a[0]=[Ct(" Start Creating ")])),_:1,__:[0]}),me(h,{to:"/library",class:"home__secondary"},{default:wt(()=>a[1]||(a[1]=[Ct(" View Library ")])),_:1,__:[1]})])]),a[4]||(a[4]=vr('<div class="home__hero-visual" data-v-4433fcbf><div class="home__feature-grid" data-v-4433fcbf><div class="home__feature" data-v-4433fcbf><div class="home__feature-icon" data-v-4433fcbf>👤</div><h3 data-v-4433fcbf>Rich Characters</h3><p data-v-4433fcbf>Create detailed characters with personalities, goals, and flaws</p></div><div class="home__feature" data-v-4433fcbf><div class="home__feature-icon" data-v-4433fcbf>🌍</div><h3 data-v-4433fcbf>Vivid Settings</h3><p data-v-4433fcbf>Build immersive worlds across multiple genres and time periods</p></div><div class="home__feature" data-v-4433fcbf><div class="home__feature-icon" data-v-4433fcbf>🎭</div><h3 data-v-4433fcbf>Compelling Themes</h3><p data-v-4433fcbf>Explore deep themes like redemption, love, and transformation</p></div><div class="home__feature" data-v-4433fcbf><div class="home__feature-icon" data-v-4433fcbf>📚</div><h3 data-v-4433fcbf>Story Generation</h3><p data-v-4433fcbf>Generate structured outlines with 3-act story progression</p></div></div></div>',1))]),pe("div",Au,[pe("div",Tu,[pe("div",xu,Jt(o.value),1),a[5]||(a[5]=pe("div",{class:"home__stat-label"},"Characters Created",-1))]),pe("div",Ou,[pe("div",Iu,Jt(i.value),1),a[6]||(a[6]=pe("div",{class:"home__stat-label"},"Settings Available",-1))]),pe("div",Pu,[pe("div",Mu,Jt(l.value),1),a[7]||(a[7]=pe("div",{class:"home__stat-label"},"Themes to Explore",-1))]),pe("div",Du,[pe("div",Nu,Jt(c.value),1),a[8]||(a[8]=pe("div",{class:"home__stat-label"},"Stories Generated",-1))])]),a[9]||(a[9]=vr('<div class="home__how-it-works" data-v-4433fcbf><h2 class="home__section-title" data-v-4433fcbf>How It Works</h2><div class="home__steps" data-v-4433fcbf><div class="home__step" data-v-4433fcbf><div class="home__step-number" data-v-4433fcbf>1</div><h3 class="home__step-title" data-v-4433fcbf>Choose Characters</h3><p class="home__step-description" data-v-4433fcbf> Select from pre-made characters or create your own with unique personalities and motivations </p></div><div class="home__step" data-v-4433fcbf><div class="home__step-number" data-v-4433fcbf>2</div><h3 class="home__step-title" data-v-4433fcbf>Pick Settings</h3><p class="home__step-description" data-v-4433fcbf> Choose the worlds and locations where your story will unfold, from fantasy realms to sci-fi cities </p></div><div class="home__step" data-v-4433fcbf><div class="home__step-number" data-v-4433fcbf>3</div><h3 class="home__step-title" data-v-4433fcbf>Select Themes</h3><p class="home__step-description" data-v-4433fcbf> Define the central conflicts and themes that will drive your narrative forward </p></div><div class="home__step" data-v-4433fcbf><div class="home__step-number" data-v-4433fcbf>4</div><h3 class="home__step-title" data-v-4433fcbf>Generate Story</h3><p class="home__step-description" data-v-4433fcbf> Our engine creates a structured story outline with plot points, character arcs, and narrative flow </p></div></div></div>',1))])}}}),Fu=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},$u=Fu(Lu,[["__scopeId","data-v-4433fcbf"]]),Hu=iu({history:La("/"),routes:[{path:"/",name:"home",component:$u},{path:"/generator",name:"generator",component:()=>as(()=>import("./GeneratorView-Gb5KoR6i.js"),__vite__mapDeps([0,1,2,3]))},{path:"/story/:id",name:"story",component:()=>as(()=>import("./StoryView-BiIZHXDY.js"),__vite__mapDeps([4,1,2,5]))},{path:"/library",name:"library",component:()=>as(()=>import("./LibraryView-E3UIPM9y.js"),__vite__mapDeps([6,7]))}]}),ku=cu(),Xs=qc(na);Xs.use(ku);Xs.use(Hu);Xs.mount("#app");export{vr as A,_u as B,vu as C,Su as D,Gu as E,Fe as F,re as G,bu as H,Ju as I,zu as J,ys as K,ze as P,ve as S,Q as T,Fu as _,pe as a,Uu as b,ri as c,Kn as d,Fs as e,te as f,Ct as g,Bu as h,Se as i,yn as j,nn as k,me as l,wt as m,Ls as n,fn as o,Vu as p,Wu as q,ju as r,Ku as s,Jt as t,$t as u,Yu as v,qu as w,yu as x,$o as y,ws as z};
